html, body {
    height: 100%;
    background: var(--primary-white, #FFFFFF);
    min-width: 970px;
    font-family: 'Sofia Pro';
}

html.ipad, .ipad body, body.msIntegration, html.msIntegration {
    min-width: 0;
}

body.msIntegration {
    overflow: hidden;
}

body.publicFolderPage {
    height: 100%;
}

input::-ms-clear {
    display: none;
}

label, input, button, select, textarea {
    font-size: 12px;
}

#editAttributeForm {
    height: auto;
}

#wrap {
    height: 100%;
    margin: 0 auto -23px;
}

form {
    margin: 0;
}

#push, #footer {
    height: 23px;
}

#footer {
    background: #eeeded;
    color: #6d6b6b;
    font-size: 12px;
    padding-top: 5px;
    border-top: 1px solid #d0d0d0;
    box-sizing: border-box;
}

#footer p {
    margin: 0;
    padding-left: 20px;
    line-height: 15px;
}

h3 {
    font-size: 18px;
}

img {
    vertical-align: baseline;
}

.btn.btn-inverse {
    color: var(--primary-white, #FFFFFF);
}

.btn.btn-tertiary{
    color: var(--tertiary-btn-text, #3569AE);
}

.btn.btn-secondary{
    color: var(--tertiary-btn-text, #3569AE);
}

a:hover {
    outline: none;
    text-decoration: none;
}

a:active {
    outline: none;
    text-decoration: none;
}

a:focus {
    -moz-outline-style: none;
    outline-style: none;
    outline: none;
    text-decoration: none;
}

#userProfImg {
    height: 30px;
    width: 30px;
    margin: 5px 2px 0 2px;
}

.loading-no-record-msg {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-color: var(--primary-white, #FFFFFF);
    text-align: center;
}

.table th.filelistchkbox {
    height: 100%;
    padding-left: 15px;
    padding-right: 8px;
    width: 10px;
}

.filelistchkbox {
    padding: 8px 4px 0 4px;
    height: 28px;
    width: 31px;
}

#basketContent .filelistchkbox input {
    z-index: 1050;
}

.filelistchkbox input {
    width: 13px;
    height: 13px;
    padding: 0;
    margin: 0;
    vertical-align: bottom;
    position: relative;
    top: -1px;
    *overflow: hidden;
    left: 26%;
}

.view-switch-outer .filelistchkbox input {
    width: 13px;
    height: 13px;
    padding: 0;
    margin: 0;
    vertical-align: bottom;
    position: relative;
    top: -3px;
    margin-right: 5px;
    *overflow: hidden;
    left: 26%;
}

#fourCol .filelistchkbox input[type='checkbox'] {
    left: 0;
}

#assocFilesModal .filelistchkbox, #assocDiscussModal .filelistchkbox, #assocCommsModal .filelistchkbox, #assocListsContent .filelistchkbox {
    width: 12px;
}

#adoddleDockInformationUI button {
    right: 10px;
    position: absolute;
    top: 5px;
    margin: 0;
    color: #333;
    min-width: 0;
}

#assocFilesModal .modal-body, #assocDiscussModal .modal-body, #assocCommsModal .modal-body {
    overflow: visible!important;
}

.listingImage {
    width: 50px;
    padding: 8px 4px;
    text-align: center;
}

.table th.filelisttype {
    padding-left: 15px;
    padding-right: 8px;
    width: 30px;
}

.table th.filelistname {
    width: 150px;
}

.table th.filelistiss {
    width: 70px;
}

.table th.filelistpublisher {
    width: 80px;
}

.table th.filelistdate {
    width: 100px;
}

.table th.filelistcomment {
    width: 25px;
}

.table th.filelistattach {
    width: 25px;
}

.table th.filelistcheckout {
    width: 25px;
}

.table th.filelistmyact {
    width: 113px;
}

.setwidth10 {
    width: 10px;
}

.setwidth30 {
    width: 30px;
}

.setwidth50 {
    width: 50px;
}

.setwidth60 {
    width: 60px;
}

.setwidth70 {
    width: 70px;
}

.setwidth80 {
    width: 80px;
}

.setwidth100 {
    width: 100px;
}

.setwidth120 {
    width: 120px;
}

.setwidth140 {
    width: 140px;
}

.setwidth150 {
    width: 150px;
}

.setwidth170 {
    width: 170px;
}

.setwidth180 {
    width: 180px;
}

.setwidth200 {
    width: 200px;
}

.margin-in {
    padding: 5px;
    background: var(--primary-white, #FFFFFF);
}

.nav {
    color: var(--primary-black, #000000);
    font-size: 16px;
}

#my-nav .nav {
    color: #6d6b6b;
    list-style: none outside none;
    margin-bottom: 0;
    margin-left: 0;
}

ul.inline>li, ol.inline>li {
    padding: 0;
}

.header-expend-arrow {
    float: right;
}

.open-apps-active #my-nav {
    background-color: var(--primary-white, #FFFFFF);
}
#header .left-side-header{
    display: flex;
    align-items: center;
    gap: 16px;
}

#header .left-side-header .brand-logo {
    /* display: block; */
    display: flex;
    align-items: center;
    gap: 8px;
    margin-right: 20px;
}
#header #top-search.ribbon-view {
    margin: 0;
    padding: 0;
    height: auto;
}

.collapsed-header #header .user-avatar {
    margin-right: 6px;
}
.collapsed-header #header #top-search.ribbon-view {
    margin-left: 16px;
}

#my-nav .nav>li>a {
    color: var(--primary-black, #0C1315);
    display: block;
    font-size: 14px;
    padding: 4px 8px 5px;
    text-align: center;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    transition: .3s all;
    border: 0;
}

#my-nav .nav>li>a.active{
    border-bottom-color: var(--primary-color, #3569AE);
    font-weight: bold;
    text-decoration: none;
    background-color: transparent;
}

#my-nav .nav>li>a::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    border-top: 5px solid var(--primary-color);
    border-radius: 8px 8px 0 0;
    bottom: 0px;
    opacity: 0;
}

#my-nav .nav>li>a:hover, #my-nav .nav>li>a:focus{
    border-bottom-color: transparent;
    text-decoration: none;
    background-color: transparent;
}

#my-nav .nav>li.active>a::after {
    opacity: 1;
}

#my-nav .nav>li>a>span {
    padding: 4px 8px;
    display: block;
    border-radius: 10px;
}
#my-nav .nav>li.active>a>span {
    background-color: transparent;
}

#my-nav .nav li.active {
    color: var(--primary-white, #FFFFFF);
    text-decoration: none;
    background-color: transparent;
}

#my-nav .nav>li:not(.active)>a:hover>span,
#my-nav .nav>li:not(.active)>a:focus>span {
    background-color: var(--primary-hover-gray, #F1F2F5);
    color: var(--primary-color, #3569AE);
}

#my-nav .portal-nav>li>a {
    padding: 5px 8px 4px;
    width: auto !important;
    max-width: 150px;
}
#my-nav .portal-nav>li>a:hover, #my-nav .portal-nav>li>a:focus {
    border-bottom-color: transparent;
    font-weight: normal;
    text-decoration: none;
    background-color: transparent;
}
#my-nav .portal-nav>li>a>span {
    padding: 7px 16px;
    border-radius: 10px;
    display: block;
}
#my-nav .portal-nav>li:not(.active)>a:hover>span,
#my-nav .portal-nav>li:not(.active)>a:focus>span {
    background-color: var(--light-hover-color, #CFCDF4);
}

#header #my-nav {
    width: fit-content;
}

#my-nav .nav li.active a {
    font-weight: bold;
    border-bottom-color: transparent;
}

#my-nav .nav li.divider {
    border-left: 1px dotted #d4d4d4;
    height: 20px;
    padding: 0;
}

#my-nav .dropdown-menu.filterui-list-section {
    left: auto;
    right: 53px;
    position: relative;
}

#resizeDiv {
    margin: 0 10px 0 0;
    border: 1px solid #d1d1d1;
    border-top: none;
    padding: 0;
    -webkit-border-radius: 4px 4px 4px 4px;
    -moz-border-radius: 4px 4px 4px 4px;
    border-radius: 4px 4px 4px 4px;
    float: left;
    height: 100%;
    position: relative;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

#resizeDiv.tree-wrapper {
    width: 341px;
    padding: 0;
    margin-right: 0;
    border: 1px solid #D1D1D1;
}

#resizeDiv.tree-wrapper #folderTree {
    height: 200px;
}

.jqHandle {
    height: 15px;
}

.jqDrag {
    cursor: move;
    width: 100%;
}

.jqResize {
    bottom: 0;
    cursor: se-resize;
    position: absolute;
    right: 0;
    width: 15px;
}

.jqDnR {
    background-color: #EEE;
    border: 1px solid #CCC;
    color: #618d5e;
    font-size: .77em;
    margin: 5px 10px 10px 10px;
    padding: 8px;
    position: relative;
    width: 180px;
    z-index: 3;
}

#home-welcome {
    right: 50px;
    position: absolute;
    top: 129px;
    z-index: 9999;
}

#home-welcome-top {
    direction: ltr;
}

#home-welcome-top-inner {
    color: #848484;
    font-size: 12px;
    width: 275px;
    margin-left: 14px;
    background: linear-gradient(to bottom,#3569AE 0,#15304a 100%);
    line-height: 14px;
    min-height: 180px;
}

#home-welcome-top-inner span.welcome-logo-context-sensitive-help img {
    margin-top: 10px;
    margin-left: 14px;
}

#home-welcome-top-inner.ar_SA {
    direction: rtl;
}

#home-welcome-top-inner h1 {
    color: var(--primary-white, #FFFFFF);
    margin: 10px 11px 0px;
    padding: 10px 5px 6px;
    font-family: 'Sofia Pro';
    font-style: normal;
    font-weight: 500;
    font-size: 28px;
    line-height: 28px;
}

#home-welcome-top-inner p {
    color: #D4EAFF;
    margin: 0px 10px 0px 10px;
    padding: 6px 4px 15px 6px;
    font-family: 'Sofia Pro';
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 14px;
}

#home-welcome-top-inner h1.ru_RU, #home-welcome-top-inner h1.es_ES, #home-welcome-top-inner h1.de_DE, #home-welcome-top-inner h1.fr_FR {
    font-size: 22px;
    line-height: 20px;
}

#home-welcome-lower {
    background: url(../images/welcome_lower.png) bottom no-repeat;
    padding-bottom: 15px;
    max-height: 580px;
    overflow: auto;
    width: 303px;
    overflow-x: hidden;
}

#home-welcome-lower-inner {
    color: #848484;
    font-size: 12px;
    line-height: 14px;
    max-height: 300px;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 17px 0 0 32px;
    width: 250px;
}

#step-video {
    color: #848484;
    font-size: 12px;
    line-height: 14px;
    padding: 0 0 0 32px;
    width: 250px;
    margin-top: 10px;
    display: none;
}

#step-video.ar_SA {
    padding: 0 32px 0 0!important;
}

.home-step {
    margin-bottom: 0!important;
    float: left;
    width: 100%;
}

.home-step strong {
    color: var(--primary-black, #000000);
    display: block;
    float: left;
    font-size: 14px!important;
    font-weight: 300;
    padding: 0!important;
    width: 35px!important;
}

.home-step span {
    background: url(../images/welcome_step_circle.gif);
    color: var(--primary-white, #FFFFFF);
    display: block;
    float: left;
    font-size: 14px!important;
    font-weight: bold;
    height: 20px!important;
    text-align: center;
    width: 20px!important;
    background-size: 20px!important;
    margin: 0!important;
    padding: 0!important;
}

.close-welcomepanel {
    cursor: pointer;
    display: block;
    float: right;
    height: 20px;
    width: 20px;
    margin-right: 25px;
    margin-top: 15px;
}
a.close-welcomepanel i {
    color: var(--primary-white, #FFFFFF);
    font-size: 15px;
}
a.close-welcomepanel i:hover {
    color: var(--primary-white, #FFFFFF);
}

.wraper-for-context-help .table-wraper-for-context-help {
    margin-left: 5px;
}


.wraper-for-context-help .step-label-table-row {
    padding-top: 8px;
    margin: 3px;
}

.wraper-for-context-help .step-label-table-row .step-label-heading {
    font-family: 'Sofia Pro';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 14px;
    color: #0D2745;
}

.wraper-for-context-help .help-step {
    /* text-align: justify; */
    position: relative;
    margin-top: 5px;
    padding-bottom: 5px;
    font-family: 'Sofia Pro';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    color: #263238;
}

.wraper-for-context-help .help-step::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: -8%; 
    width: 118%; 
    border-bottom: 1px solid #EEEEEE; 
}
.wraper-for-context-help .help-step.help-step-collabrate-border::after {
    box-shadow: 0px -2px 4px rgba(0, 0, 0, 0.1);
}

.wraper-for-context-help .learn-more-icons {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
    margin-top: 10px;
}

.wraper-for-context-help .learn-more-icons .learn-more-img {
    flex-basis: 45%;
    padding-top: 6px;
    box-sizing: border-box;
    margin: 0px auto;
    border-radius: 4px;
    box-shadow: 0px 0px 10px 5px #EEEEEE;
}

.wraper-for-context-help .learn-more-icons .learn-more-img:hover {
    box-shadow: 0px 0px 10px 2px lightblue;
}

.wraper-for-context-help .learn-more-img .help-icon-bottom{
    margin: 0px 32px;
}

.wraper-for-context-help .learn-more-img .help-icon-text-bottom {
    color: var(--primary-color, #3569AE);
    text-align: center;
    font-family: 'Sofia Pro';
    font-style: normal;
    font-weight: 500;
    font-size: 22px;
    line-height: 22px;
}

.wraper-for-context-help .learn-more-img .aLearning-icon-bottom{
    margin: 15px 9px;
}

.wraper-for-context-help .help-link {
    color: var(--primary-black, #000000);
    padding: 5px 0px 5px 0px;
    font-family: 'Sofia Pro';
    font-style: normal;
    font-weight: 500;
    font-size: 14px !important;
    line-height: 14px;
}

.wraper-for-context-help .to-learn-more-click {
    position: relative;
    padding-top: 12px;
    padding-right: 8px;

}

.wraper-for-context-help .to-learn-more-click::after {
    content: "";
    position: absolute;
    top: 5px;
    left: -6%;
    width: 110%;
    border-top: 1px solid #EEEEEE;
    box-shadow: 0px -2px 4px rgba(0, 0, 0, 0.1);
}

.wraper-for-context-help .context-helper-description {
    color: #263238;
    font-family: 'Sofia Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    margin-top: 10px;
    margin-bottom: 13px;
}

.wraper-for-context-help .context-help-links-heading {
    color: #0D2745;
    font-family: 'Sofia Pro';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 14px;
    margin-bottom: 22px;
}

.wraper-for-context-help table tbody table tbody tr td p {
    color: var(--primary-color, #3569AE);
    font-family: 'Sofia Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    text-decoration-line: underline;
}

.wraper-for-context-help table tbody table tbody tr td p a {
    color: var(--primary-color, #3569AE);
    font-family: 'Sofia Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    text-decoration-line: underline;
}

.step-line {
    border-top: 1px dotted #c7c7c7;
    font-size: 0;
    line-height: 0;
    margin: 10px 0 10px 0;
    width: 240px;
}

#home-welcome-lower-inner strong {
    color: var(--primary-black, #000000);
    font-size: 17px;
    font-weight: 300;
}

#step-video strong {
    color: var(--primary-black, #000000);
    font-size: 17px;
    font-weight: 300;
}

.home-step>div.left {
    float: left;
}

.home-step>div.right {
    float: left;
    width: 71%;
}

.right>img {
    float: right;
    margin-right: -5px;
    margin-top: 2px;
}

.right>strong {
    float: left;
    text-align: right;
    width: 72%;
}

.jquery-custom-search-p{
	margin: 0;
}

#home-welcome-lower-inner a {
    float: right;
    margin-right: 10px;
}

#home-welcome-announcement {
    right: 100px;
    position: absolute;
    top: 129px;
    z-index: 999;
}

#home-welcome-top-announcement {
    background: url(../images/welcome_top.png) top no-repeat;
    height: 178px;
    width: 303px;
}

#home-welcome-top-inner-announcement {
    color: #848484;
    font-size: 12px;
    line-height: 14px;
    padding: 17px 0 0 77px;
    width: 200px;
}

#home-welcome-top-inner-announcement h1 {
    color: #eee;
    font-size: 26px;
    font-weight: 300;
    margin-bottom: 4px;
}

#home-welcome-lower-announcement {
    background: url(../images/welcome_lower.png) bottom no-repeat;
    padding-bottom: 15px;
    width: 303px;
}

#home-welcome-lower-inner-announcement {
    color: #848484;
    font-size: 12px;
    height: 240px;
    line-height: 14px;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 17px 0 0 32px;
    width: 257px;
}

#step-video-announcement {
    color: #848484;
    font-size: 12px;
    line-height: 14px;
    padding: 17px 0 0 32px;
    width: 250px;
}

#home-welcome-lower-inner-announcement strong {
    color: var(--primary-black, #000000);
    font-size: 17px;
    font-weight: 300;
}

#step-video-announcement strong {
    color: var(--primary-black, #000000);
    font-size: 17px;
    font-weight: 300;
}

#home-welcome-lower-inner-announcement a {
    float: right;
    margin-right: 10px;
}

#dismis_button button {
    bottom: 40px;
    font-size: 13px;
    height: 25px;
    line-height: 16px;
    margin-right: 10px;
    padding: 4px 12px;
    position: absolute;
    right: 20px;
    width: 65px;
}

.dashboard-box {
    width: 328px;
    margin-bottom: 20px;
    float: left;
    margin-right: 10px;
}

.widgetHeader>span {
    font-size: 14px;
}

.dashboard-box-top {
    background: transparent;
    height: 42px;
    width: 100%;
    -webkit-border-radius: 4px 4px 0 0;
    -moz-border-radius: 4px 4px 0 0;
    border-radius: 4px 4px 0 0;
    font-weight: bold;
    border-bottom: 1px solid #D4D8E0;
    box-sizing: border-box;
}

.power-bi-chart-frame {
    border: none;
    width: 100%;
    height: 100%;
    padding: 0px 10px 10px 10px;
    box-sizing: border-box;
}

.dashboard-box-lower {
    border: 1px solid #d1d1d1;
    border-top: none;
    -webkit-border-radius: 0 0 4px 4px;
    -moz-border-radius: 0 0 4px 4px;
    border-radius: 0 0 4px 4px;
}

.dashboard-box-lower h3 {
    color: var(--primary-black, #000000);
    font-size: 16px;
    margin-top: 21%;
    line-height: 20px;
}

.dashboard-box-lower h3 a {
    margin-left: 10px;
}

.toggle-delete {
    background: url(../images/icons/icon_dash_close.gif) no-repeat;
    display: block;
    float: right;
    height: 22px;
    margin: 8px 10px 0 4px;
    width: 22px;
}

.toggle-dash {
    background: url(../images/icons/icon_dash_minus.gif) no-repeat;
    display: block;
    float: right;
    height: 22px;
    margin: 4px 10px 0 4px;
    width: 22px;
}

.toggle-dash.dash-toggle {
    background: url(../images/icons/icon_dash_plus.gif) no-repeat;
    display: block;
    height: 22px;
    width: 22px;
}

.fullwidth-box {
    margin: 1px 0 0 0;
}

.modal .fullwidth-box-top {
    background-color: var(--primary-color, #3569AE);
    padding: 6px 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    height: auto;
    padding: 0;
}

#fileCommsDetails .manage-files .fullwidth-box-top {
    background-color: var(--primary-color, #3569AE);
    margin-top: 0;
}

#pws_container .manage-files .fullwidth-box-top, #sectionWelcome .manage-files .fullwidth-box-top, .modal .manage-files .fullwidth-box-top, .modal .manage-folders .fullwidth-box-top, .viewer .manage-files .fullwidth-box-top, .viewer .manage-folders .fullwidth-box-top, .manage-files #shareViewerHeader.fullwidth-box-top, .newdiscussionLayout.attchedFileViewPref .manage-files .fullwidth-box-top, .viewCatalogModalContent .manage-folders .fullwidth-box-top, .viewCatalogModalContent .manage-product .fullwidth-box-top {
    position: static;
    background-color: var(--primary-color, #3569AE);
    margin-top: 0;
    -webkit-border-radius: 4px 4px 0 0;
    -moz-border-radius: 4px 4px 0 0;
    border-radius: 4px 4px 0 0;
}

#pws_container .manage-files .fullwidth-box-top h2, #sectionWelcome .manage-files .fullwidth-box-top h2, .modal .manage-files .fullwidth-box-top h2, .modal .manage-folders .fullwidth-box-top h2, .viewer .manage-files .fullwidth-box-top h2, .viewer .manage-folders .fullwidth-box-top h2, .manage-files #shareViewerHeader.fullwidth-box-top h2, .newdiscussionLayout.attchedFileViewPref .manage-files .fullwidth-box-top h2, .viewCatalogModalContent .manage-folders .fullwidth-box-top h2, .viewCatalogModalContent .manage-product .fullwidth-box-top h2 {
    color: var(--primary-white, #FFFFFF);
    line-height: 33px;
}

#appSelectionHeader {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    position: fixed;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 2;
}

#appSelectionHeader>.close {
    margin-right: 7px;
    padding: 0 4px;
    margin-top: 6px;
}

#appSelectionHeader>.helpcontent {
    width: 23px!important;
    padding: 0!important;
    margin-left: 3px!important;
    margin-right: 2px;
    height: 24px;
}

#appSelectionHeader>.manage-selects>.restored {
    display: none;
}

#appSelectionWrapper #optshow~*, #accessHistoryDetails #optshow~* {
    display: none;
}

#appSelectionWrapper .innerContainer {
    overflow-x: hidden;
}

#appSelectionWrapper #formsPaging {
    margin-left: 35px;
}

#appsListSection {
    min-height: 150px;
    height: 537px;
    border: none;
    border-top: 30px solid var(--primary-white, #FFFFFF);
}

#appsListSection #filterFormContainer {
    display: none;
    height: 36px;
}

#appsListSection #filterFormContainer .divthead.divtr {
    padding: 0;
}

.viewer .resizable .fullwidth-box-top {
    overflow: hidden;
}

.window-maximize .fullwidth-box-top, .window-maximize-navigator .fullwidth-box-top {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}

.window-maximize.tab-listing .manage-files, .window-maximize .manage-files.tab-listing {
    padding-right: 0;
}

.window-maximize.manage-files.tab-listing{
    z-index: 1001;
}

.add-minwidth {
    min-width: 100%;
}

.fullwidth-box-lower {
    border: 1px solid #d1d1d1;
    border-top: none;
    padding: 20px;
    -webkit-border-radius: 0 0 4px 4px;
    -moz-border-radius: 0 0 4px 4px;
    border-radius: 0 0 4px 4px;
}

.nopadding-box-lower {
    border-top: none;
    padding: 0;
    -webkit-border-radius: 0 0 4px 4px;
    -moz-border-radius: 0 0 4px 4px;
    border-radius: 0 0 4px 4px;
}

.modal .nopadding-box-lower {
    position: relative;
}

#fileViewer>.nopadding-box-lower {
    clear: both;
}

.fullwidth-box-top h2 {
    color: var(--neutral-gray, #616161);
    float: left;
    font-size: 14px;
    line-height: 34px;
    margin: 0;
    font-weight: bold;
    padding-left: 10px;
}

.fullwidth-box-top .manage-dashboard-heading{
    color:var(--primary-black,#000000);
    line-height: 27px;
    font-size: 16px;
    padding-left: 0;
}

.manage-dashboard-topbar{
    background-color: var(--primary-white,#FFFFFF);
    padding: 7px 16px;
    border-top-left-radius: 8px;
}

#docListingSection .fullwidth-box-top h2 {
    font-style: italic;
    font-size: 12px;
    display: none;
}

#docListingSection.viewer-open .fullwidth-box-top h2, .modal #docListingSection .fullwidth-box-top h2 {
    font-style: normal;
    font-size: 14px;
    display: inline-block;
}

.appsSelection .fullwidth-box-top {
    background-color: var(--primary-color, #3569AE);
}

.modal .fullwidth-box-top h2, #pws_container .fullwidth-box-top h2, .appsSelection .fullwidth-box-top h2 {
    color: var(--primary-white, #FFFFFF);
    font-weight: normal!important;
}

.modal .manage-selects span, .modal .resize-view {
    color: var(--primary-white, #FFFFFF);
}

.modal .switch-view {
    color: var(--primary-white, #FFFFFF);
}

.modal .switch-view.active {
    color: #c1c1c1d9;
}

.modal .adoddle-opt-show {
    background-color: var(--primary-white, #FFFFFF);
}

.appsSelection .fullwidth-box-top .listing-records-details {
    color: var(--primary-white, #FFFFFF);
    line-height: 34px;
}

.clear {
    clear: both;
    font-size: 0;
    height: 0;
    line-height: 0;
    margin: 0;
    padding: 0;
}

.table {
    margin: 0;
}

.table a {
    color: var(--primary-black, #000000);
}

#freezeFileNameDiv {
    position: absolute;
    overflow: hidden;
    box-shadow: 2px 1px 2px #dbd9d9;
    width: 150px;
    background: var(--primary-white, #FFFFFF);
    display: none;
    top: 50px;
    max-height: 573px;
    left: 0;
}

#selectFileEditAttributeModal #freezeFileNameDiv {
    max-height: none;
}

#divSelectedFiles #freezeFileNameDiv .text-elipssis, #divAttachedFiles #freezeFileNameDiv .text-elipssis {
    max-width: 100px;
}

#divSelectedFiles {
    position: relative;
}

#divSelectedFiles #freezeFileNameDiv th, #divSelectedFiles #freezeFileNameDiv td, #divAttachedFiles #freezeFileNameDiv th, #divAttachedFiles #freezeFileNameDiv td {
    padding: 8px;
}

#divSelectedFiles #freezeFileNameDiv th, #divAttachedFiles #freezeFileNameDiv th {
    border-bottom: 1px dotted #DCDCDC;
}

#editAttributeTable tr th, #freezeFileNameDiv tr th {
    border-bottom: none;
}

#tblbodyAttributesSection thead tr:first-child th, #editAttributeTable thead tr:first-child th {
    cursor: pointer;
}

#left-nav-blocks .manage-dashboard-left-block{
    height: 67px;
    width: 78px;
    display: flex;
    flex-direction: column;
    row-gap: 3px;
    border-radius: 4px;
    padding: 7px 4px;
    padding-top: 30px;
    cursor: pointer;
    box-sizing: border-box;
    border: 1px solid transparent;
    position: relative;
    background-color: var(--primary-color, #3569AE) !important;
    border-color: var(--primary-color, #3569AE);
}

#left-nav-blocks .manage-dashboard-left-block i{
    color: var(--primary-white, #FFFFFF) !important;
    font-size: 20px;
    position: absolute;
    top: 5px;
    left: 4px;
}

#left-nav-blocks .manage-dashboard-left-block .primary-label{
    color: var(--primary-white, #FFFFFF) !important;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;  
    text-align: left;  
}

.table tr, #editAttributeTable tr, #freezeFileNameDiv tr {
    background-color: transparent!important;
    background: url(../images/icons/tr_repeat.png) bottom repeat-x;
}

.table tr.last, #editAttributeTable tr.last, #freezeFileNameDiv tr.last {
    background-color: transparent!important;
    background: url(../images/icons/tr_repeat_last.png) bottom repeat-x;
}

#dashborad-listing .table tr{
    background: none;
    border-bottom: 1px solid #ddd;
}

#freezeFileNameDiv .redicon-remove {
    cursor: pointer;
}

#editAttributeTable td.leftAlignCell input[type="text"], #editAttributeTable td.leftAlignCell textarea {
    min-width: 100%;
}

#editAttributeTable td.leftAlignCell input[type="text"].docTitleAttribute, #editAttributeTable td.leftAlignCell select {
    max-width: 400px;
}

#editAttributeTable td select, #myModal-upload td select{
    width: auto;
}

#editAttributeTable .revColumn {
    min-width: 65px;
    max-width: 65px;
}

#editAttributeTable .revColumn input, #tblbodyAttributesSection input.uploadRevInpt {
    text-align: center;
}

#editAttributeTable .merge-panel th.leftAlignCell input[type="text"], #editAttributeTable .merge-panel th.leftAlignCell textarea, #editAttributeTable .merge-panel th select {
    min-width: 85%;
    max-width: 85%;
}

#myModal-upload .merge-panel th .hasDatepicker, #editAttributeTable .merge-panel th .hasDatepicker {
    max-width: 58px;
}

#editAttributeTable .merge-panel th.revColumn input[type="text"].thMergePanel {
    min-width: 28px;
    margin-left: 0;
    margin-right: 3px;
}

#editAttributeTable td.leftAlignCell input[type="text"], #editAttributeTable .merge-panel th.leftAlignCell input[type="text"] {
    -webkit-box-sizing: border-box;
    -khtml-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.ie11 #editAttributeTable td.leftAlignCell input[type="text"], #editAttributeTable .merge-panel th.leftAlignCell input[type="text"] {
    -webkit-box-sizing: border-box;
    -khtml-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    height: 30px;
}

#downloadAssocAndAttachmentBody {
    max-height: 350px;
    overflow-y: auto;
    overflow-x: hidden;
}

#downloadAssocAndAttachmentBody table a {
    color: var(--primary-color, #3569AE);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 640px;
}

.pop-doc-info th {
    font-size: 11px;
    padding: 0 10px 0 0;
}

.table .pop-doc-info th {
    line-height: 16px;
    padding-left: 0;
}

.pop-doc-info td {
    font-size: 11px;
    padding: 0;
}

.pop-doc-info tr {
    background: none;
}

.red-theme textarea, .red-theme input[type="text"], .red-theme .red-theme input[type="password"], .red-theme input[type="datetime"], .red-theme input[type="datetime-local"], .red-theme input[type="date"], .red-theme input[type="month"], .red-theme input[type="time"], .red-theme input[type="week"], .red-theme input[type="number"], .red-theme input[type="email"], .red-theme input[type="url"], .red-theme input[type="search"], .red-theme input[type="tel"], .red-theme input[type="color"], .red-theme .uneditable-input {
    background-color: #a30000;
    border: 1px solid #d60100;
    border-right: none;
    width: 160px;
    font-size: 12px;
    color: var(--primary-white, #FFFFFF);
    height: 16px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    transition: border .2s linear 0s, box-shadow .2s linear 0s;
}

.red-theme-btn {
    background-color: #a30000;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    border-left: none;
    background-image: none;
    background-repeat: repeat-x;
    border-color: #d60100;
    border-image: none;
    border-radius: 0 4px 4px 0;
    border-style: solid;
    border-width: 1px;
    color: #333;
    cursor: pointer;
    height: 30px;
    display: inline-block;
    font-size: 14px;
    line-height: 14px;
    margin-bottom: 0;
    padding: 5px 12px 3px 12px;
    text-align: center;
    text-shadow: none;
    vertical-align: middle;
    border-left: none;
}

.grey-theme textarea, .grey-theme input[type="text"], .grey-theme .grey-theme input[type="password"], .grey-theme input[type="datetime"], .grey-theme input[type="datetime-local"], .grey-theme input[type="date"], .grey-theme input[type="month"], .grey-theme input[type="time"], .grey-theme input[type="week"], .grey-theme input[type="number"], .grey-theme input[type="email"], .grey-theme input[type="url"], .grey-theme input[type="search"], .grey-theme input[type="tel"], .grey-theme input[type="color"], .grey-theme .uneditable-input {
    background-color: #f3f3f3;
    border: 1px solid #cfcfcf;
    border-right: none;
    color: var(--primary-black, #000000);
    height: 30px;
    transition: border .2s linear 0s, box-shadow .2s linear 0s;
}

#top-search .grey-theme-btn,  #top-search .black-theme-btn{
    background-color: transparent;
    border-left: none;
    background-image: none;
    background-repeat: repeat-x;
    border-image: none;
    border-radius: 0 4px 4px 0;
    border-style: solid;
    border-width: 1px;
    color: #333;
    cursor: pointer;
    display: inline-block;
    font-size: 0;
    line-height: 0;
    margin-bottom: 0;
    padding: 0;
    text-align: center;
    text-shadow: none;
    vertical-align: middle;
    border-left: none;
    border: none;
}

#top-search {
    float: left;
    height: 30px;
    padding: 5px 5px 0 0;
    width: 200px;
}

#top-search .fa-search-btn {
    font-size: 17px;
    color: var(--primary-white, #FFFFFF);
}

.search-query {
    color: #555;
}

#top-percent {
    float: right;
    height: 39px;
    margin: 8px 10px 0 10px;
    padding: 14px 5px 0 0;
}

#top-percent strong {
    float: left;
    padding: 4px 0 0 0;
}

#percentbox {
    background: url(../images/icons/percent_blank.png);
    float: left;
    height: 30px;
    margin: 0 0 0 10px;
    position: relative;
    width: 17px;
}

#percentbox.percent_0_to_10 {
    background: url(../images/icons/percent_meter_10.png);
}

#percentbox.percent_11_to_20 {
    background: url(../images/icons/percent_meter_20.png);
}

#percentbox.percent_21_to_30 {
    background: url(../images/icons/percent_meter_30.png);
}

#percentbox.percent_31_to_40 {
    background: url(../images/icons/percent_meter_40.png);
}

#percentbox.percent_41_to_50 {
    background: url(../images/icons/percent_meter_50.png);
}

#percentbox.percent_51_to_60 {
    background: url(../images/icons/percent_meter_60.png);
}

#percentbox.percent_61_to_70 {
    background: url(../images/icons/percent_meter_70.png);
}

#percentbox.percent_71_to_80 {
    background: url(../images/icons/percent_meter_80.png);
}

#percentbox.percent_81_to_90 {
    background: url(../images/icons/percent_meter_90.png);
}

#percentbox.percent_91_100 {
    background: url(../images/icons/percent_meter_90.png);
}

#top-rss {
    border-left: 1px dotted #d4d4d4;
    float: right;
    height: 39px;
    margin: 18px 0 0 0;
    padding: 14px 10px 0 10px;
}

#calendar-drop {
    float: right;
    height: 31px;
    margin: 8px 0 0;
    padding: 10px 10px 0;
    margin-right: 10px;
}

#calendar-drop .dropdown-menu a:hover {
    background: var(--primary-color, #3569AE);
}

#calendar-drop .selectedLanguage {
    background: none repeat scroll 0 0 var(--primary-color, #3569AE);
    text-decoration: none;
}

#calendar-drop .selectedLanguage span {
    color: var(--primary-white, #FFFFFF)!important;
}

#user-loggedin .caret, #calendar-drop .caret {
    background: url(../images/icons/icon_black_down.png) right no-repeat;
    border: none;
    height: 6px;
    margin: 12px 0 0 0;
    width: 9px;
}

#calendar-drop ul {
    padding: 0;
}

#calendar-drop .dropdown-toggle {
    background: none;
    background-color: none;
    border: none;
    box-shadow: none;
    text-decoration: none;
}

#user-loggedin .dropdown-menu>li>a, #calendar-drop .dropdown-menu>li>a {
    padding: 3px 10px;
}

#myModal-moreoptions #left-nav-blocks .overflow {
    float: left;
    margin-right: 20px;
}

#left-nav-blocks a, .left-nav-blocks a {
    color: var(--primary-black, #000000);
    display: block;
    font-size: 11px;
    min-height: 55px;
    line-height: 13px;
    margin-bottom: 10px;
    padding: 20px 0 0 0;
    text-align: center;
    width: 67px;
    cursor: pointer;
    box-shadow: 0 2px 1px -1px rgba(0, 0, 0, .2), 0 1px 1px 0 rgba(0, 0, 0, .14), 0 1px 3px 0 rgba(0, 0, 0, .12);
}

.dashboard-container .sidebar-nav {
    overflow: visible;
}

.nohovereffect:hover {
    background-position: 0 0!important;
    color: var(--primary-black, #000000)!important;
}

#left-nav-blocks a.active, #left-nav-blocks a:hover, .left-nav-blocks a.active, .left-nav-blocks a:hover {
    color: var(--primary-white, #FFFFFF);
    text-decoration: none;
}

#left-nav-blocks a:focus-visible {
    outline: auto;
    outline-color: var(--primary-black, #000000);
}

#left-nav-blocks ul, .left-nav-blocks ul {
    list-style-type: none;
    margin: 0;
}

#left-nav-blocks ul a div, .left-nav-blocks ul a div {
    overflow: hidden;
}

#myModal-user-preference .fullwidth-box-top.add-minwidth h2 {
    font-size: 16px;
}

#myModal-user-preference .modal-footer {
    padding: 0;
    float: left;
    width: 99%;
    padding-top: 10px;
    border: none;
}

#myModal-user-preference .modal-footer #cancel-upload-pref {
    margin-right: 10px;
}

#myModal-user-preference .divider {
    float: left;
    width: 100%;
    border-top: 1px solid #dcdcdc;
    margin-top: 5px;
}

#myModal-user-preference .documents {
    border-bottom: none;
}

#myModal-user-preference .documentsControls {
    float: left;
    margin-left: 50px;
}

#myModal-user-preference .form-dotted-line {
    margin: 10px 0;
    width: 100%;
}

#myModal-user-preference .commentsControls {
    float: left;
    margin-left: 50px;
}

#welcome-div {
    height: inherit;
}

#welcome-div span {
    position: relative;
    top: 50%;
    left: 50%;
    font-size: 14px;
    font-weight: bold;
    transform: translateX(-50%) translateY(-50%);
    float: left;
}

div#upload-preference {
    padding: 0 4%;
    float: left;
}

div#upload-preference .element label, div#download-preference .element label {
    font-size: 12px;
}

div#download-preference {
    padding: 0 15px;
    float: left;
}

#emailNotificationsContent {
    overflow: hidden;
}

#emailNotificationsContent .divtd {
    padding-left: 10px;
    font-size: 12px;
    border-left: 1px solid #ededed;
    overflow: hidden;
    text-overflow: ellipsis;
}

.minWidth.divtd.listingImage.col-hasMarkup-fixed-width.filelistimg img {
    padding-top: 0;
    vertical-align: middle;
}

a#sidenav-admin:hover, a#sidenav-admin {
    background: url(../images/icons/sidenav_admin_on.png) no-repeat;
    color: var(--primary-white, #FFFFFF);
}

a#sidenav-filter {
    background: url(../images/icons/sidenav_filter_on.png) no-repeat;
    color: var(--primary-white, #FFFFFF);
}

a#sidenav-filter:hover {
    background: url(../images/icons/sidenav_filter_on.png) no-repeat;
    color: var(--primary-white, #FFFFFF);
}

a.sidenav-company {
    background: url(../images/icons/sidenav_companies.png) no-repeat;
    color: var(--primary-white, #FFFFFF);
}

a.sidenav-mysupp {
    background: url(../images/icons/sidenav_my_suppliers.png) no-repeat;
    color: var(--primary-white, #FFFFFF);
}

a.sidenav-admin-all {
    background: url(../images/icons/sidenav_admin_all.png) no-repeat;
    color: var(--primary-black, #000000);
}

a.sidenav-admin-autofetch-attributes {
    background: url(../images/icons/sidenav_admin_fetch_attribute.png) no-repeat;
    color: var(--primary-black, #000000);
}

#left-nav-blocks a.sidenav-number, .left-nav-blocks a.sidenav-number {
    font-size: 12px;
    height: 57px;
    line-height: 13px;
    margin-bottom: 10px;
    padding: 13px 5px 0 0;
    text-align: center;
    width: 73px;
}

#left-nav-blocks .sidenav-number:hover span, #left-nav-blocks .sidenav-number:hover, .left-nav-blocks .sidenav-number:hover span, .left-nav-blocks .sidenav-number:hover, #left-nav-blocks .sidenav-number.active span, #left-nav-blocks .sidenav-number.active, .left-nav-blocks .sidenav-number.active span, .left-nav-blocks .sidenav-number.active {
    color: var(--primary-white, #FFFFFF);
}

#left-nav-blocks a.sidenav-number span, .left-nav-blocks a.sidenav-number span {
    color: var(--primary-black, #000000);
    float: left;
    font-size: 14px;
    font-weight: bold;
    line-height: 39px;
    margin-right: 2px;
    margin-top: -11px;
    width: 93%;
}

#left-nav-blocks a.sidenav-number span img, .left-nav-blocks a.sidenav-number span img {
    vertical-align: middle;
    text-align: center;
}

a#sidenav-projectadd:hover {
    background: url(../images/icons/sidenav_projectadd_on.png) no-repeat;
    color: var(--primary-white, #FFFFFF);
}

a#sidenav-files-upload {
    background: url(../images/icons/sidenav_files_upload.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-files-addfolder {
    background: url(../images/icons/sidenav_files_upload.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-files-invite {
    background: url(../images/icons/sidenav_files_invite.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-files-create {
    background: url(../images/icons/sidenav_files_create.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-files-unread {
    background: url("../images/icons/sidenav_files_create.png") no-repeat;
    color: var(--primary-black, #000000);
    line-height: 14px;
}

a#sidenav-contact-personal {
    background: url(../images/icons/sidenav_contact_personal.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-contact-import {
    background: url(../images/icons/sidenav_contact_import.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-commu-createNew-model {
    background: url(../images/icons/sidenav_apps_createforms.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-apps-createviews {
    background: url(../images/icons/sidenav_apps_createviews.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-forms-recent {
    background: url(../images/icons/sidenav_files_upload.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-forms-fav {
    background: url(../images/icons/sidenav_files_upload.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-calendar_addevent:hover, a#sidenav-calendar_addevent {
    background: url(../images/icons/sidenav_calendar_addevent.png) no-repeat;
    color: var(--primary-white, #FFFFFF);
}

a#sidenav-exchange-trade {
    background: url(../images/icons/sidenav_exchange_trade.png) no-repeat;
    color: var(--primary-black, #000000);
    letter-spacing: -0.5px;
}

a#sidenav-exchange-setup {
    background: url(../images/icons/sidenav_exchange_setup.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-exchange-purchase {
    background: url(../images/icons/sidenav_exchange_trade.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-exchange-bids {
    background: url(../images/icons/sidenav_exchange_setup.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#filenav-distribution {
    background: url(../images/icons/filenav_distribution.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#filenav-revisions {
    background: url(../images/icons/filenav_revisions.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#filenav-access {
    background: url(../images/icons/filenav_access.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#filenav-discussions {
    background: url(../images/icons/filenav_comments.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#leftNavModelComments {
    background: url(../images/icons/filenav_comments.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#leftNavModelApps {
    background: url(../images/icons/filenav_comms.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#formnav-dist-history {
    background: url(../images/icons/filenav_distribution.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#formnav-acc-history {
    background: url(../images/icons/filenav_access.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#formnav-export {
    background: url(../images/icons/sidenav_contact_import.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#formnav-download {
    background: url(../images/icons/filenav_distribution.png) no-repeat;
    color: var(--primary-black, #000000);
}

.contact-jobtitle {
    background: url(../images/icons/icon_contact_title.png) 0 3px no-repeat;
}

.contact-tel {
    background: url(../images/icons/icon_contact_phone.png) 0 3px no-repeat;
}

.contact-email {
    background: url(../images/icons/icon_contact_email.png) 0 3px no-repeat;
}

.contact-list-info {
    list-style-type: none;
    margin: 0;
}

.contact-list-info li {
    float: left;
    padding: 0 20px 8px 30px;
}

ul.contact-list-info {
    margin: 16px 0 0 0;
}

.contact-list-info span {
    color: #797979;
}

.make-relative {
    margin-bottom: 10px;
    padding: 30px 20px;
    position: relative;
}

.make-relative label {
    font-size: 16px;
    margin-left: 15px;
}

label.center-label {
    text-align: center;
}

.link-peoplecontact {
    float: right;
}

.contact-left {
    float: left;
    width: 65%;
}

.contact-right {
    position: absolute;
    right: 10px;
    top: 20px;
    width: 245px;
}

.well-contact {
    min-height: 20px;
    padding: 15px 19px 19px 19px;
    margin-bottom: 10px;
    background-color: var(--primary-white, #FFFFFF);
    border: 1px solid #e3e3e3;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}

.toggle-header {
    background: url(../img/site/toggle.png) 0 -50px no-repeat;
    border: none;
    color: var(--primary-black, #000000);
    cursor: pointer;
    font-size: 16px;
    height: 36px;
    line-height: 16px;
    padding: 0;
    padding: 14px 0 0 20px;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
    width: 663px;
}

.toggle-header h3 {
    color: var(--primary-black, #000000);
    font-size: 16px;
    line-height: 16px;
    margin: 0;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
}

.toggle-header.collapsed {
    background: url(../img/site/toggle.png) 0 0 no-repeat;
}

.user-toggle {
    font-size: 12px;
}

.user-toggle img {
    margin: 0 0 0 10px;
}

#user-loggedin .dropdown-menu a:hover, .actionsDropdown .dropdown-menu a:hover, #actionsDropdown .dropdown-menu a:hover {
    background: var(--primary-color, #3569AE)!important;
}

.activeMyAccount {
    background: none repeat scroll 0 0 var(--primary-color, #3569AE)!important;
    color: var(--primary-white, #FFFFFF)!important;
}

#user-loggedin .user-toggle .caret, .actionsDropdown .user-toggle .caret, #actionsDropdown .user-toggle .caret {
    margin: 17px 0 0 10px;
}

#user-loggedin .dropdown-toggle, .actionsDropdown .dropdown-toggle, #actionsDropdown .dropdown-toggle {
    background: none;
    background-color: none;
    border: none;
    box-shadow: none;
    color: var(--primary-black, #000000);
    text-decoration: none;
}

#filterprojects {
    font-size: 12px;
    line-height: 14px;
    margin: 12px 3px 0 0;
    width: 135px;
}

#filterprojects ul.dropdown-menu {
    font-size: 11px;
    padding: 10px 10px 5px 10px;
    top: 22px;
    white-space: nowrap;
}

#filterprojects ul li {
    border-bottom: 1px dotted #d4d4d4;
    font-size: 12px;
}

#filterprojects ul li.last {
    border-bottom: none;
    font-size: 12px;
}

#filterprojects label {
    color: var(--primary-black, #000000);
    font-size: 11px;
}

#filtersearch {
    color: var(--primary-black, #000000);
    float: right;
    font-size: 12px;
    line-height: 14px;
    margin: 10px 3px 0 0;
    padding: 0 0 0 10px;
}

#filterSave>i, #role-privilage-info>i {
    font-size: 15px;
}

#savedFilterButton i {
    font-size: 14px;
}

#filtersearch a {
    background: url(../images/icons/icon_smallwhite_drop.png) right no-repeat;
    color: var(--primary-white, #FFFFFF);
    font-size: 11px;
}

#filtersearch ul.dropdown-menu {
    font-size: 11px;
    padding: 5px 5px 5px 5px;
    top: 24px;
    width: auto;
}

#filtersearch td {
    padding: 0 0 10px 4px;
}

#filtersearch ul {
    margin: 0;
}

#filtersearch ul li {
    font-size: 12px;
}

#filtersearch ul li.last {
    border-bottom: none;
    font-size: 12px;
}

#filtersearch label {
    font-size: 11px;
}

#view-toggles {
    float: right;
    background: #DB4D4D;
    border: 1px solid #DB4D4D;
    border-radius: 3px 3px 3px 3px;
    box-shadow: none;
    font-size: 12px;
    padding: 5px 5px 5px 5px;
    text-decoration: none;
    margin: 5px 0 0 5px;
}

a#redview-list {
    background: url(../images/icons/grid_layout_1_white.png);
    display: block;
    float: right;
    height: 15px;
    margin: 0 0 0 2px;
    width: 18px;
}

a#redview-list:hover, a#view-grid:hover {
    background-position: 0 0;
}

a#redview-list.active, a#redview-grid.active {
    background-position: 0 -15px;
}

a#redview-grid {
    background: url(../images/icons/grid_layout_2_white.png);
    display: block;
    float: right;
    height: 15px;
    margin: 0 0 0 2px;
    width: 18px;
}

.calendar-filter span.add-on {
    background: var(--primary-white, #FFFFFF);
    float: left;
    height: 18px;
    padding-top: 6px;
}

.calendar-filter {
    margin-bottom: 0;
}

.calendar-filter th {
    font-weight: 300;
    line-height: 29px;
    padding: 0 5px 0 0;
    text-align: left;
    vertical-align: top;
}

.calendar-filter .input-append {
    border-left: none;
    font-size: 11px;
    width: 100px;
}

#filtersearch .input-smaller {
    background: var(--primary-white, #FFFFFF);
    border-right: none;
    float: left;
    font-size: 11px;
    width: 50px;
}

.calendar-filter table {
    width: 90%;
}

.manage-selects #filtersearch select {
    margin: 0;
}

#filtersearch .dropdown-toggle {
    background: #db4d4d url(../images/icons/icon_smallwhite_drop.png) 86px 10px no-repeat;
    border: 1px solid #db4d4d;
    box-shadow: none;
    padding: 3px 20px 5px 15px;
    font-size: 12px;
    width: 140px;
    text-decoration: none;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

#filtersearch select {
    border: 1px solid #d5d5d5;
    font-size: 12px;
    line-height: 16px;
    float: left;
    margin: 5px 0 0 5px;
    padding: 3px 5px;
    height: auto;
    width: auto;
    background: var(--primary-white, #FFFFFF);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    color: var(--primary-black, #000000);
    border-radius: 4px;
}

.sidebar-nav {
    /* margin: 10px 0 0 0; */
    padding: 0;
}

.sidebar-nav #left-nav-blocks, .sidebar-nav .left-nav-blocks {
    padding: 0 0 0 5px;
}

/* .dashboard-container .sidebar-nav {
    margin-top: 10px;
} */

#moreOptions {
    background-color: var(--primary-white, #FFFFFF);
    float: left;
    position: absolute;
    z-index: 100;
    border-radius: 5px;
    display: none;
    margin-left: 5px;
}

#moreOptions .btn-group {
    cursor: pointer;
    text-align: center;
    width: 100%;
}

#moreOptions .btn-group a {
    color: #949293;
    display: block;
    font-size: 12px;
    height: 50px;
    line-height: 13px;
    margin-bottom: 10px;
    padding: 20px 0 0 0;
    text-align: center;
    width: 65px;
}

#moreOptions .btn-group a:hover, #moreOptions .btn-group a.active {
    background: var(--primary-color, #3569AE);
    border: 1px solid var(--primary-color, #3569AE);
    color: var(--primary-white, #FFFFFF);
}

#moreOptions .btn-group a:hover i {
    color: var(--primary-white, #FFFFFF);
}

#moreOptions.active a {
    color: white;
}

#moreOptions:hover a {
    color: white;
}

.row-fluid>.sidebar-nav {
    top: 0;
    left: auto;
    width: 80px;
}

.left {
    float: left;
}

.right {
    float: right;
}

.marginleft0 {
    margin-left: 0;
}

.fixed-fluid {
    margin-left: 110px;
}

.fluid-fixed {
    margin-right: 240px;
    margin-left: auto!important;
}

.fixed-fixed {
    margin: 0 0 0 90px;
    height: 100%;
}

.fixed-fixed .row-fluid {
    height: 100%;
}

#modalprojecttemplateLayout .modal-body select:focus-visible,
#modalprojecttemplateLayout .modal-body input[type="checkbox"]:focus-visible{
    outline: auto;
}

#projectmanageRolesModal .container-fluid, #projectManageMailModal .container-fluid, #inviteUsersModal .container-fluid {
    background-color: #eeeded;
}

#projectManageMailModal .fixed-fixed, #inviteUsersModal .fixed-fixed {
    background-color: var(--primary-white, #FFFFFF);
}

.dashboard-container .content.fixed-fixed.clearfix {
    background-color: inherit;
}

.width-padded {
    background: #eeeced;
    padding: 8px;
}

.width-padded .icon-remove-circle {
    cursor: pointer;
}

#contactExportControls.width-padded {
    padding: 6px;
}

.nopadding-box-lower>.width-padded>.width-padded {
    background: none;
}

.width-padded>a {
    font-size: 12px!important;
}

.main-contacts .width-padded>.activeExport {
    height: 25px;
    margin-left: 4px;
    cursor: pointer;
    width: 23px;
}

.width-padded-last {
    background: url(../images/icons/tr_repeat_last.png) bottom repeat-x;
    padding: 8px;
    clear: both;
}

.width-padded-dottedtop {
    background: url(../images/icons/tr_repeat_last.png) bottom repeat-x;
    padding: 8px;
    border-top: 1px dotted #d7d7d7;
}

.manage-selects {
    color: #727372;
    font-size: 12px;
    line-height: 32px;
    margin: 0;
}

.manage-selects span {
    color: #727372;
    display: block;
    float: left;
    font-size: 12px;
    line-height: 19px;
    padding: 2px 2px 0 0;
}

.maximized .maximise-minimise {
    background: url("/images/icons/restore_window.png") repeat scroll 0 0 rgba(0, 0, 0, 0);
    height: 18px;
    width: 18px;
    display: inline-block;
}

.width-padded-table {
    background: url(../images/icons/tr_repeat.png) bottom repeat-x;
    padding: 8px 8px 8px 18px;
}

.width-padded-catalogs {
    background: url(../images/icons/tr_repeat_last.png) bottom repeat-x;
    padding: 8px 8px 7px 18px;
}

.dashboard-drop-button:hover {
    border: 1px solid var(--primary-white, #FFFFFF);
}

.manage-selects select.optshow:hover, .manage-selects select.optshow:focus {
    border: 1px solid var(--primary-white, #FFFFFF);
}

.open .dashboard-drop-button {
    background-color: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.25);
}

#fileActiveListType {
    display: none;
    width: auto;
    border-color: var(--primary-white, #FFFFFF);
    height: 22px;
}

.optshow {
    width: auto;
    border-color: var(--primary-white, #FFFFFF);
    height: 22px;
}

.dashboard-drop-button {
    font-size: 12px;
    color: var(--primary-white, #FFFFFF);
    border-color: var(--primary-white, #FFFFFF);
}

.dashboard-nav a {
    color: var(--primary-white, #FFFFFF);
}

.redselect {
    border: 1px solid var(--primary-white, #FFFFFF);
    font-size: 12px;
    line-height: 16px;
    float: left;
    margin: 0 0 0 5px;
    padding: 1px 5px;
    height: auto;
    background: var(--primary-color, #3569AE);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    color: var(--primary-white, #FFFFFF);
    border-radius: 4px;
    width: auto;
}

.modal #admin-blocks, .viewer #admin-blocks {
    padding: 20px 0 20px 0;
}

.dropdown-menu #admin-blocks {
    padding: 0;
}

.dropdown-menu #admin-blocks .check-list-item a {
    color: var(--primary-black, #000000);
    padding: 0;
    height: auto;
    margin: 0;
    text-align: left;
    width: auto;
}

.admin-blocks i.visibility-info-icon {
    margin-left: 4px;
    font-size: 14px!important;
    min-width: 14px;
}

.admin-blocks {
    list-style: none;
    margin: 5px 0;
}

.tabMoreOptions+.dropdown-menu .admin-blocks {
    list-style: none;
    margin: 5px 0;
    display: table-cell;
}

.tabMoreOptions+.dropdown-menu .admin-blocks.hide {
    display: none;
}

#admin-blocks a, .admin-blocks a, .option-container a {
    color: var(--primary-black, #000000);
    display: block;
    margin: 0 auto 0 auto;
    padding: 0;
}

.modal #admin-blocks a, .viewer #admin-blocks a {
    color: #6c6c6c;
    display: block;
    height: 95px;
    margin: 0 auto 10px auto;
    padding: 0;
    text-align: center;
    width: 95px;
}

.circle-block-outer>a.btn-disabled, .circle-block-outer>a.btn-disabled+strong {
    opacity: .5;
}

.circle-block-outer, .option-container .circle-block-outer {
    color: #6c6c6c;
    font-size: 12px;
}

.modal .circle-block-outer, .viewer .circle-block-outer {
    color: #6c6c6c;
    float: left;
    font-size: 14px;
    height: 170px;
    text-align: center;
    width: 170px;
}

#admin-blocks strong, .admin-blocks strong, .option-container strong {
    color: var(--primary-black, #000000);
    font-size: 14px;
    font-weight: 300;
}

a#adminblock-assign {
    background: url(../images/admin/admin_apps.png) 0 0 no-repeat;
}

#admin-blocks a:hover, #admin-blocks-files a:hover, #admin-blocks-discussions a:hover, #admin-blocks-transmittals a:hover, #workflow-options-block a:hover, .option-container a:hover {
    background-position: 0 -95px;
}

#admin-blocks a:focus{ 
    outline: none;
}

#admin-blocks a:focus-visible {
    background-position: 0 -95px;
}

a#more-create-discussion:hover i #workflow-options-block a.btn-disabled:hover {
    background-position: 0 0;
}

#admin-editworkplace {
    background: url(../images/admin/admin_editworkplace_on.png) 0 0 no-repeat;
}

#admin-apps-workplace {
    background: url(../images/admin/admin_apps.png) 0 0 no-repeat;
}

#admin-apps-settings {
    background: url(../images/admin/admin_apps_2.png) 0 0 no-repeat;
}

#admin-managerole {
    background: url(../images/admin/admin_manage.png) 0 0 no-repeat;
}

#admin-inviteusers {
    background: url(../images/admin/admin_invite.png) 0 0 no-repeat;
}

#admin-distributiuon {
    background: url(../images/admin/admin_disctribution.png) 0 0 no-repeat;
}

#admin-managestatus {
    background: url(../images/admin/admin_managestatus.png) 0 0 no-repeat;
}

#admin-dwgseries {
    background: url(../images/admin/admin_drawingseries.png) 0 0 no-repeat;
}

#admin-managenotice {
    background: url(../images/admin/admin_managenotices.png) 0 0 no-repeat;
}

#admin-managepurpose {
    background: url(../images/admin/admin_managepurposeissue.png) 0 0 no-repeat;
}

#admin-managemailbox {
    background: url(../images/admin/admin_manage_mail.png) 0 0 no-repeat;
}

#admin-managedocnumb {
    background: url(../images/admin/admin_manage_doc.png) 0 0 no-repeat;
}

#manage-workflowrules {
    background: url(../images/admin/admin_rules.png) 0 0 no-repeat;
}

#options-viewattribute {
    background: url(../images/admin/option_viewattributes.png) 0 0 no-repeat;
}

#options-amend {
    background: url(../images/admin/option_amenddocdescr.png) 0 0 no-repeat;
}

#options-clickclear {
    background: url(../images/admin/option_clicktoclear.png) 0 0 no-repeat;
}

#options-delegate {
    background: url(../images/admin/option_clickdelegateactions.png) 0 0 no-repeat;
}

#options-deactivate {
    background: url(../images/admin/option_deactivateactions.png) 0 0 no-repeat;
}

#options-reactivate {
    background: url(../images/admin/option_reactivateactions.png) 0 0 no-repeat;
}

#options-download {
    background: url(../images/admin/option_download.png) 0 0 no-repeat;
}

#options-printversion {
    background: url(../images/admin/option_printversionfile.png) 0 0 no-repeat;
}

#optionsmore-createcomment {
    background: url(../images/admin/more_options_publishstandarddoc.png) 0 0 no-repeat;
}

.viewer #optionsmore-associateToApp {
    background: url(../images/admin/more_options_associate_to_app.png) 0 0 no-repeat;
}

.viewer .more-options #optionsmore-associateToApp {
    background-image: none;
}

.viewer #optionsmore-create-comment {
    border-radius: 50%;
    background: #fdfdfd;
    line-height: 135px;
    border: 1px solid #dcd5d5;
    cursor: pointer;
}

.viewer #optionsmore-create-comment .i-icon-create-comment {
    font-size: 48px;
    color: #191717;
}

.viewer #optionsmore-create-comment:hover {
    background: var(--primary-black, #000000);
}

.viewer #optionsmore-create-comment:hover i {
    color: var(--primary-white, #FFFFFF);
}

#optionsmore-create-form, #option-createApp {
    background: url(../images/admin/create-form.png) 0 0 no-repeat;
}

#option-createInvoice {
    background: url(../images/admin/admin_manage_doc.png) 0 0 no-repeat;
}

#optionsmore-publishpaper {
    background: url(../images/admin/more_options_publishpaperdoc.png) 0 0 no-repeat;
}

#optionsmore-comparedoc {
    background: url(../images/admin/more_options_comparedocs.png) 0 0 no-repeat;
}

#optionsmore-associateform {
    background: url(../images/admin/more_options_associateform.png) 0 0 no-repeat;
}

#optionsmore-editfolder {
    background: url(../images/admin/more_options_editfolder.png) 0 0 no-repeat;
}

#optionsmore-copyWorkflowDefination {
    background: url(../images/admin/more_options_copyWorkflowDefination.png) 0 0 no-repeat;
}

#optionsmore-addsub {
    background: url(../images/admin/more_options_addsubfolder.png) 0 0 no-repeat;
}

#optionsmore-copy {
    background: url(../images/admin/more_options_copyfolderstructure.png) 0 0 no-repeat;
}

#optionsmore-permissions {
    background: url(../images/admin/more_options_viewfolderpermissions.png) 0 0 no-repeat;
}

#optionsmore-assocLists {
    background: url(../images/admin/more_options_associeatelists.png) 0 0 no-repeat;
}

#optionsmore-edit-project {
    background: url(../images/admin/admin_rules.png) 0 0 no-repeat;
}

#optionsmore-edit-template {
    background: url(../images/admin/admin_rules.png) 0 0 no-repeat;
}

#optionsmore-edit-access-to-template {
    background: url(../images/admin/edit_acceess_to_template.png) 0 0 no-repeat;
}

#optionsmore-save-workspace-as-template {
    background: url(../images/admin/save_workspace_to_template.png) 0 0 no-repeat;
}

#optionsmore-user-roles-access {
    background: url(../images/admin/options-user-roles-access.png) 0 0 no-repeat;
}
/* manage attribute css starts */

#optionsmore-manage-role-priv {
    background: url(../images/admin/options-manage-role-priv.png) 0 0 no-repeat;
}

#optionsmore-manage-form-priv {
    background: url(../images/admin/options-manage-form-priv.png) 0 0 no-repeat;
}

#optionsmore-manage-user-attribute{
    background: url(../images/admin/more_options_associateform.png) 0 0 no-repeat;
}

#optionsmore-manage-form-status {
    background: url(../images/admin/admin_manage_doc.png) 0 0 no-repeat;
}

#optionsmore-manage-doc-status {
    background: url(../images/admin/more_options_managedocstatus.png) 0 0 no-repeat;
}

#optionsmore-manage-poi {
    background: url(../images/admin/manage_purpose_issue.png) 0 0 no-repeat;
}

#optionsmore-manage-distribution-group {
    background: url(../images/admin/manage_distribution_group.png) 0 0 no-repeat;
}

#optionsmore-add-proxy-user {
    background: url(../images/admin/add-proxy-user-thumb-white.png) 0 0 no-repeat;
}

#optionsmore-add-proxy-user:hover {
    background: url(../images/admin/add-proxy-user-thumb-dark.png) 0 0 no-repeat !important;
}

.itemstatus-approved {
    background: url(../images/icons/icon_approved_green.png) 0 3px no-repeat;
    padding: 0 0 0 20px;
}

.itemstatus-pending {
    background: url(../images/icons/icon_pending_yellow.png) 0 3px no-repeat;
    padding: 0 0 0 20px;
}

.itemstatus-rejected {
    background: url(../images/icons/icon_reject_red.png) 0 3px no-repeat;
    padding: 0 0 0 20px;
}

a.btn-mini {
    display: block;
    float: left;
    margin-left: 3px;
    padding: 2px 10px;
}

#catalogModal.modal {
    width: 670px;
}

#myModal-options-icons.modal {
    width: auto;
    max-width: 730px;
}

#myModal-moreoptions-icons.modal {
    width: auto;
    max-width: 880px;
}

#myModal-chooseupload.modal, #myModal-projectinvite.modal, #myModal-contact.modal, #myModal-delete.modal, #myModal-deletefolder.modal {
    font-size: 13px;
    width: 420px;
}

#myModal-download {
    width: 660px!important;
}

#myModal-contact .projectform .controls {
    float: left;
    width: 240px;
}

#myModal-contact label.control-label {
    float: left;
    width: 120px;
}

#myModal-contact .form-horizontal .controls {
    margin-left: 0;
    margin-left: 0;
    padding-left: 20px;
}

#myModal-contact .modal-body {
    padding: 15px 15px 1px 15px;
}

.uploads-files-table {
    font-size: 14px;
}

.uploads-files-text {
    text-align: center;
    font-size: 12px;
    padding: 20px 0 0 0;
    color: #6c6c6c;
}

.uploads-files-text strong {
    text-align: center;
    color: var(--primary-black, #000000);
    font-size: 16px;
}

.uploads-files-table th {
    border-bottom: 1px dotted #dcdcdc;
    color: #6c6c6c;
    font-weight: 300;
    padding: 4px 0 5px 0;
    text-align: left;
}

.uploads-files-table td {
    border-bottom: 1px dotted #dcdcdc;
    padding: 4px 0 5px 0;
}

.uploads-files-table th div {
    background: url(../images/icons/icon_grey_down.png) right no-repeat;
    float: left;
    padding: 0 10px 0 0;
}

.uploads-files-table td a {
    background: url(../images/icons/close_red_delete.png) right no-repeat;
    color: var(--primary-black, #000000);
    padding: 4px 15px 2px 0;
}

.uploads-files-table th.span1 {
    width: 40px;
}

.uploads-files-table th.span2 {
    width: 280px;
}

.uploads-files-table th.span3 {
    width: 80px;
}

.move-files-table a {
    background: url(../images/icons/close_red_delete.png) right no-repeat;
    color: var(--primary-black, #000000);
    float: left;
    font-size: 14px;
    display: block;
    padding: 4px 15px 2px 0;
}

.move-files-table {
    padding: 0 0 15px 0;
}

input.overlay-gray {
    width: 360px;
    background-color: #F5F5F5;
    border: 1px solid #D3D3D3;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    color: var(--primary-black, #000000);
    margin-bottom: 0;
    padding: 10px;
    transition: border .2s linear 0s, box-shadow .2s linear 0s;
}

input.overlay-move {
    width: 330px;
    background-color: #F5F5F5;
    border: 1px solid #D3D3D3;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    color: var(--primary-black, #000000);
    margin-bottom: 0;
    padding: 10px;
    transition: border .2s linear 0s, box-shadow .2s linear 0s;
}

#invite-users td {
    padding-bottom: 10px;
}

#invite-users select {
    width: 100px;
}

#invite-users a {
    color: var(--primary-black, #000000);
}

#invite-users a:hover {
    color: #e20908;
}

#invite-users span {
    padding-bottom: 10px;
    color: #6c6c6c;
}

#sitelist td {
    width: 300px;
    padding: 0 0 5px 0;
}

#sitelist td a {
    color: var(--primary-black, #000000);
}

#sitelist th a {
    color: var(--primary-black, #000000);
}

#sitelist {
    padding: 20px 0 0 20px;
}

.editProjectNavDiv #imageUpload+.controls>a {
    margin-top: 4px;
}

.editProjectNavDiv input[name="inputproname"] {
    width: 99%;
}

#create-cloneprojects input[name="inputproname"] {
    width: 96%;
}

.edit-project-logo-info {
    background: url("../images/icons/information.png");
    height: 18px;
    width: 18px;
    background-size: cover;
    cursor: pointer;
    position: absolute;
    top: 5px;
}

div#projectTabThumbListCont {
    padding: 20px 0 0 0;
}

#project-blocks, #companies-blocks {
    padding: 20px 0 0 0;
    float: left;
}

#project-blocks {
    padding: 0;
}

.boxgrid {
    border: 1px solid #E2E2E2;
    border-radius: 4px;
    float: left;
    margin: 0 0 20px 30px;
    overflow: hidden;
    padding: 0;
    position: relative;
    text-align: center;
    background-color: white;
}

.boxGridImageWrapper {
    font-size: 0;
    line-height: normal;
}

table {
    border-collapse: collapse;
}

#no-files-selected>table, #no-files-attached>table {
    text-align: center;
    width: 100%;
}

.boxcaption {
    display: none;
    float: left;
    height: 150px;
    left: 0;
    position: absolute;
    text-align: left;
    top: 0;
    width: 220px;
    z-index: 1;
}

.cover {
    width: 220px;
}

.boxcaption-inner {
    height: 150px;
    left: 0;
    position: relative;
    width: 220px;
    z-index: 777;
}

.project-pop-number {
    background: url(../images/icons/project_number_bg.png);
    color: var(--primary-white, #FFFFFF);
    height: 37px;
    left: -10px;
    padding: 10px 4px 0 0;
    position: absolute;
    text-align: center;
    top: -10px;
    width: 43px;
    z-index: 999;
}

.project-pop-links {
    position: absolute;
    right: 0;
    top: 5px;
}

.btn.btn-view {
    background-image: url("/images/icons/view.png");
    height: 24px;
    width: 46px;
}

.btn.btn-view:hover, .btn.btn-view:focus {
    background-position: 0 0;
    color: #333;
    text-decoration: none;
    transition: none;
}

.project-pop-links a {
    margin-right: 5px;
    font-size: 15px;
    background: var(--primary-white, #FFFFFF);
    padding: 4px 5px;
    border-radius: 2px;
    border: 1px solid #dedede;
}

.project-pop-links a:hover {
    background-color: var(--primary-color, #3569AE);
    color: var(--primary-white, #FFFFFF);
}

.box-projectname {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 210px;
    line-height: 30px;
    font-size: 12px;
    border-top: 1px solid rgba(136, 136, 136, 0.3);
    padding: 0px 5px;
}

.box-projectname.active {
    background: none repeat scroll 0 0 #E2E2E2;
}

.boxmodel-inner {
    height: 28px;
    padding: 120px 0 0 0;
    text-align: center;
    width: 220px;
}

.main-contacts #contactThumbnailContainer {
    padding-top: 15px;
}

.contact-block {
    float: left;
    width: 280px;
    height: 240px;
    margin: 0 0 15px 15px;
    /* padding: 10px; */
    box-shadow: 0 13px 21px -15px rgba(0, 0, 0, .3);
    position: relative;
    box-sizing: border-box;
    background-color: var(--primary-white, #FFFFFF);
    border-radius: 2px;
    -webkit-font-smoothing: antialiased;
    border: none;
    border-radius: 0;
    border: none;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -khtml-border-radius: 4px;
    border-radius: 0px;
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, .125);
    box-sizing: border-box;
}

.contact-block .img-wrapper {
    padding: 10px 20px 0;
    background-color: #f6f6f6;
    position: relative;
}

.contact-block .text-detail {
    overflow: hidden;
    padding: 6px 10px 6px 10px;
    font-size: 12px;
}

.contact-block .text-detail h4, .contact-block .text-detail p {
    margin: 0;
    font-size: 14px;
    overflow: hidden;
    white-space: pre;
    text-overflow: ellipsis;
    line-height: 20px;
}

.contact-block .text-detail .contact-email-id, .contact-block .text-detail .contact-no {
    font-size: 13px;
    margin-top: 5px;
}

.contact-block .action-container {
    background: var(--primary-white, #FFFFFF);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -khtml-border-radius: 4px;
    border-radius: 4px;
    padding: 4px 5px;
    height: 30px;
    clear: both;
    margin-top: 6px;
    float: left;
    width: 97%;
}

.contact-block .action-container a {
    height: 30px;
    margin: 0 5px;
}

.contact-block .action-container a img {
    float: left;
}

.contact-block .img-wrapper img {
    width: 100px;
    height: 100px;
}

.card-block {
    padding: 5px 0;
}

.card-block h4 {
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 700;
}

.card-text {
    color: #707070;
    font-size: 12px;
    margin-bottom: 0px;
}

.edit-detail {
    position: absolute;
    top: 77px;
    left: 65px;
    padding: 7px;
    background: var(--primary-white, #FFFFFF);
    border-radius: 50%;
    font-size: 16px;
    background-color: var(--primary-color, #3569AE);
    color: var(--primary-white, #FFFFFF);
    text-align: center;
}

.edit-detail:hover {
    color: var(--primary-white, #FFFFFF);
}

.contact-block .text-detail .fa {
    margin-right: 5px;
}

#contactDetailModal {
    width: auto;
    padding: 0;
    max-width: 40%;
}

#contactDetailModal .modal-header {
    padding: 0 10px;
}

#contactDetailModal .modal-header h3,
#contactDetailModal .modal-header .myModalLabel {
    font-size: 13px;
}

#contactDetailModal .modal-body {
    padding: 0 20px 0 0;
    -webkit-border-radius: 0 0 3px 3px;
    -moz-border-radius: 0 0 3px 3px;
    -khtml-border-radius: 0 0 3px 3px;
    border-radius: 0 0 3px 3px;
    float: left;
}

#contactDetailModal .modal-body .contact-block {
    margin: 0;
    border: 0;
    cursor: default;
    width: 100%;
    line-height: 170px;
    border-radius: 0;
    box-shadow: none;
}

#modalEditProxy {
    width: 84%;
}

#modalEditProxy .userNameWrapper {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 20px;
}

#modalEditProxy #userName {
    font-weight: normal;
    display: inline-block;
    vertical-align: middle;
    border: 1px solid #ccc;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -khtml-border-radius: 2px;
    border-radius: 2px;
    min-width: 150px;
    padding: 3px 8px;
    margin-left: 2px;
}

#modalEditProxy fieldset, #editAppsModal fieldset, #myModal-EditFolder fieldset, #myModal-addfolder fieldset, #batchChangeStatusModal fieldset {
    border: 2px solid #ccc;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -khtml-border-radius: 3px;
    border-radius: 3px;
    padding: 5px;
    clear: both;
    margin-bottom: 10px;
}

#myModal-EditFolder fieldset, #myModal-addfolder fieldset {
    border: 1px solid #ccc;
}

#modalEditProxy legend {
    border: none;
    width: auto;
    font-size: 12px;
    font-weight: bold;
    line-height: normal;
    margin: 0;
    padding: 0 3px;
}

#modalEditProxy #addProxyUser {
    margin: 5px 0;
}

#modalEditProxy fieldset #editProxyTableContainer, #modalEditProxy fieldset #editProxyTableContainer .file-attr-table-container {
    clear: both;
}

#modalEditProxy fieldset #editProxyTableContainer .bulk-apply-trigger-container {
    min-height: 34px;
    margin-top: -34px;
}

#modalEditProxy fieldset #editProxyTableContainer table {
    min-width: 100%;
    height: auto;
}

#modalEditProxy fieldset #editProxyTableContainer table input[type="text"] {
    margin-bottom: 0;
}

#modalEditProxy fieldset #editProxyTableContainer table input[type="checkbox"] {
    margin-top: 10px;
}

.modal {
    left: 50%;
    margin-left: -425px;
    top: 10%;
    width: 850px;
    padding: 0;
    z-index: 1050;
}

#uploadIframeModal.modal {
    width: auto;
}

#uploadIframeModal.modal .modal-body {
    min-height: 80px;
}

.modal-header {
    background: none repeat scroll 0 0 var(--primary-color, #3569AE);
    -webkit-border-radius: 4px 4px 0 0;
    -moz-border-radius: 4px 4px 0 0;
    border-radius: 4px 4px 0 0;
    font-size: 16px;
    font-weight: 300;
}

.window-maximize .modal-header, .window-maximize-navigator .modal-header {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}

.modal-header h3,.modal-header .myModalLabel, .modal-header h2.myModalLabel, .modal-header h1#myModalLabel-customattr {
    color: var(--primary-white, #FFFFFF);
    font-size: 14px;
    font-weight: 300;
}

.modal-header h2.unobserved{
    color: var(--primary-black, #000);
}

.modal-body {
    padding-bottom: 5px;
    background: var(--primary-white, #FFFFFF);
}

#admin-settings-modal{
    width: 100%;
    height: calc(100% - 10px);
    background: #eeeded;
    box-sizing: border-box;
    height: 100%;
    border-bottom: 5px solid #fff;
    box-sizing: border-box;
    padding: 55px 0 0 10px;
}

#files-trees .toggle-header {
    background: url(../images/files/trees_toggle_1b.png) top left no-repeat;
    border: none;
    color: var(--primary-black, #000000);
    cursor: pointer;
    font-size: 16px;
    height: auto;
    text-align: left;
    line-height: 16px;
    padding: 0;
    padding: 10px 0 9px 0;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
    width: 100%;
}

#files-trees .toggle-header h3 {
    color: var(--primary-black, #000000);
    font-weight: 300;
    margin: 0;
    font-size: 12px;
    margin-left: 50px;
    line-height: 16px;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
}

#files-trees .toggle-header.collapsed {
    background: url(../images/files/trees_toggle_1.png) top left no-repeat;
}

#files-trees .toggle-header-lower {
    background: url(../images/files/trees_toggle_2b.png) top left no-repeat;
    border: none;
    color: var(--primary-black, #000000);
    cursor: pointer;
    font-size: 16px;
    height: auto;
    text-align: left;
    line-height: 16px;
    padding: 0;
    padding: 10px 0 9px 0;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
    width: 100%;
}

#files-trees .toggle-header-lower h3 {
    color: var(--primary-black, #000000);
    font-weight: 300;
    margin: 0;
    font-size: 12px;
    margin-left: 70px;
    line-height: 16px;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
}

#files-trees .toggle-header-lower.collapsed {
    background: url(../images/files/trees_toggle_2.png) top left no-repeat;
}

.toggle-tree-links a {
    background: url(../images/files/trees_toggle_item.png) top left no-repeat;
    border: none;
    color: var(--primary-black, #000000);
    cursor: pointer;
    display: block;
    float: left;
    font-size: 12px;
    height: auto;
    line-height: 16px;
    padding: 0;
    padding: 8px 0 11px 65px;
    text-align: left;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
    width: 100%;
}

.holder-for-checkbox {
    position: relative;
}

.fixedspot {
    position: absolute;
    right: 10px;
    top: 8px;
    z-index: 999;
}

#scroll-overflow {
    height: auto;
    max-height: 550px;
    overflow-x: hidden;
    overflow-y: auto;
}

#scroll-Y-overflow {
    height: 550px;
    overflow-x: hidden;
    overflow-y: auto;
}

#assocCommsList, #formThreadTree {
    overflow: auto;
}

.formDetailTable {
    background: url(../images/files/filler_shadow.png) bottom repeat-x!important;
    padding: 10px 10px;
}

.fileDetailHeader {
    background: url(../images/files/filler_shadow.png) bottom repeat-x!important;
    padding: 5px 5px;
}

.fileDetailHeader table {
    max-width: 100%;
    border: 0;
}

.fileDetailHeader td {
    color: #6c6c6c;
    font-weight: 300;
    line-height: 14px;
    text-align: left;
    vertical-align: middle;
}

.fileDetailHeader table table td {
    min-width: 250px;
    max-width: 500px;
}

.fileDetailHeader td label {
    display: inline;
    font-weight: bold;
    vertical-align: middle;
}

.fileDetailHeader td div {
    display: inline-block;
    max-width: 80%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: middle;
    padding-right: 20px;
    padding-bottom: 1px;
}

#fileViewerRightIcon {
    width: 5%;
    max-width: 5%;
    min-width: 80px;
}

.table-distribution-red {
    background: #c00;
    margin: 0;
    width: 100%;
}

.table-distribution-red td {
    background: var(--primary-white, #FFFFFF) url(../images/icons/tr_repeat.png) bottom repeat-x;
    font-size: 14px;
    line-height: 14px;
    padding: 14px 0 15px 5px;
    text-align: left;
    vertical-align: top;
}

.table-distribution-red th {
    color: var(--primary-white, #FFFFFF);
    font-size: 14px;
    font-weight: 300;
    line-height: 14px;
    padding: 14px 0 15px 5px;
    text-align: left;
    vertical-align: top;
}

.table-distribution-red th a {
    background: url(../images/icons/icon_smallwhite_drop.png) right no-repeat;
    color: var(--primary-white, #FFFFFF);
    font-size: 14px;
    line-height: 14px;
    margin: 5px 0 0 0;
    padding: 0 15px 0 0;
}

.table-distribution-red .checkbox, .table-distribution-grey .checkbox {
    margin-left: 15px;
}

.table-distribution-grey {
    margin: 0;
    width: 100%;
}

.table-distribution-grey tr {
    background: url(../images/icons/tr_repeat.png) bottom repeat-x;
}

.table-distribution-grey td {
    font-size: 14px;
    height: auto;
    line-height: 14px;
    padding: 12px 0 16px 10px;
    text-align: left;
    vertical-align: top;
}

.table-distribution-grey th {
    color: var(--primary-black, #000000);
    font-size: 14px;
    font-weight: 300;
    line-height: 14px;
    padding: 12px 0 16px 10px;
    text-align: left;
    vertical-align: top;
}

.table-distribution-grey th a {
    background: url(../images/icons/icon_smallgrey_drop.png) right no-repeat;
    color: #6c6c6c;
    font-size: 14px;
    line-height: 14px;
    padding: 0 15px 0 0;
}

.table-revisions tr.light-scheme {
    background: url(../images/files/filler_shadow.png) bottom repeat-x;
}

.table-revisions tr.light-scheme td {
    font-size: 12px;
    padding: 22px 0 22px 18px;
}

.table-revisions tr.dark-scheme {
    background: #e9e9e9 url(../images/files/repeat_grey.png) bottom repeat-x;
}

.dark-scheme-header {
    background: #e9e9e9 url(../images/files/repeat_grey.png) bottom repeat-x!important;
}

#viewFormHeaderTable, #viewAssocCommsHeaderTable {
    margin: 0;
}

.table-revisions-column {
    padding: 12px 0 4px 19px;
    line-height: 14px;
}

.table-distribution-column {
    padding: 8px 8px 8px 20px;
    line-height: 14px;
}

.table-revisions-column .dragdiv-drag-handle span {
    background: url("../images/icons/icon_file_down.png") no-repeat scroll right center transparent;
    padding: 0 20px 0 0;
}

.table-distribution-column .dragdiv-drag-handle span {
    background: url("../images/icons/icon_file_down.png") no-repeat scroll right bottom transparent;
    padding: 0 20px 0 0;
}

.table-revisions-data {
    padding: 14px 0 14px 19px;
    line-height: 14px;
}

.table-distribution-data {
    padding: 0 8px 0 20px;
    line-height: 14px;
}

.light-scheme-header {
    background: url(../images/files/filler_shadow.png) bottom repeat-x!important;
}

.table-revisions tr.intro-scheme {
    background: #fafafa;
}

.table-revisions tr.intro-scheme td, .table-revisions tr.intro-scheme th {
    padding: 5px 10px 5px 18px;
}

.table-revisions tr.light-scheme th a, .table-revisions tr.dark-scheme th a {
    background: url(../images/icons/icon_file_down.png) right no-repeat;
    color: #6c6c6c;
    font-size: 12px;
    padding: 0 20px 0 0;
}

#headerCreateFormDiv {
    background: none repeat scroll 0 0 #EEE;
    width: 100%;
    display: table;
}

.table-createForm tr.intro-scheme td, .table-createForm tr.intro-scheme th {
    padding: 5px 10px 5px 18px;
}

a.btn-mini.pull-right {
    float: right;
    padding: 0 90px;
}

#help-toggles-top {
    background: var(--primary-color, #3569AE);
    padding: 3px 0;
    -webkit-border-radius: 4px 4px 0 0;
    -moz-border-radius: 4px 4px 0 0;
    border-radius: 4px 4px 0 0;
}

#help-toggles-lower {
    border: 1px solid #d1d1d1;
    border-top: none;
    -webkit-border-radius: 0 0 4px 4px;
    -moz-border-radius: 0 0 4px 4px;
    border-radius: 0 0 4px 4px;
}

#help-toggles-top h2 {
    color: var(--primary-white, #FFFFFF);
    float: left;
    font-size: 16px;
    font-weight: 300;
    margin: 0 0 0 20px;
}

.toggle-help {
    background: url(../images/icons/help_toggle_open.png) right bottom no-repeat;
    border: none;
    color: var(--primary-black, #000000);
    cursor: pointer;
    font-size: 16px;
    height: 36px;
    line-height: 16px;
    padding: 0;
    padding: 5px 0 0 0;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
    width: 100%;
}

.toggle-help h3 {
    color: var(--primary-black, #000000);
    float: left;
    font-size: 16px;
    font-weight: 300;
    line-height: 16px;
    margin: 0;
    padding: 5px 0 0 0;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
}

.toggle-help.collapsed {
    background: url(../images/icons/help_toggle_closed.png) right bottom no-repeat;
}

.toggle-help-answer ul {
    list-style-type: none;
    margin: 0;
}

.toggle-help-answer ul li {
    background: url(../images/icons/help_toggle_blank.png) right bottom no-repeat;
    border: none;
    color: var(--primary-black, #000000);
    cursor: pointer;
    font-size: 16px;
    height: 28px;
    line-height: 16px;
    padding: 0;
    padding: 14px 0 0 55px;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
    width: 100%;
}

.toggle-help-answer ul li span {
    color: #5e5e5e;
}

.toggle-help-answer h3 {
    color: var(--primary-black, #000000);
    float: left;
    font-size: 16px;
    font-weight: 300;
    line-height: 16px;
    margin: 0;
    padding: 5px 0 0 0;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
}

.toggle-help-answer a {
    color: var(--primary-black, #000000);
    font-size: 16px;
    font-weight: 300;
    line-height: 16px;
    padding: 0;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
}

.toggle-help-answer a:hover {
    color: var(--primary-color, #3569AE);
}

.badge-inverse {
    padding: 8px 12px;
    -webkit-border-radius: 14px;
    -moz-border-radius: 14px;
    border-radius: 14px;
    margin: 0 10px 0 15px;
}

.toggle-help-answer {
    padding: 0;
    background: url(../images/icons/tr_repeat.png) bottom repeat-x;
}

.projectform .control-label {
    padding: 5px 0 0 0;
    text-align: left;
}

.projectform .control-group {
    margin-bottom: 0;
}

#dataModel .control-group {
    margin-bottom: 10px;
}

.projectform .controls {
    margin-bottom: 0;
    padding: 0;
    position: relative;
}

.projectform .fileupload {
    margin-bottom: 0;
}

.projectform .RhControlsDiv {
    margin-left: 20px;
}

.projectform #netherlandsDCNotes,
.projectform #cnetherlandsDCNotes{
    margin-left: 180px;
    margin-bottom: 10px;
}

.projectform .selectGeograpyNote{    
    max-width: 53%;    
}

.btn-mini {
    font-size: 12px!important;
    padding: 0 10px;
}

.table th {
    color: #595959;
    font-weight: 300;
}

.manage-catalog-select {
    float: left;
    margin-left: 7px;
}

.manage-modal-notes {
    border-top: 1px dotted #dcdcdc;
    font-size: 12px;
    padding: 15px 0 0 0;
}

.manage-modal-notes th {
    padding: 0 10px 0 0;
    text-align: left;
    vertical-align: top;
}

.manage-modal-notes td {
    text-align: left;
    vertical-align: top;
}

.table th a {
    background: url(../images/icons/icon_smallgrey_drop.png) right no-repeat;
    color: #949494;
    font-weight: 300;
    padding: 0 10px 0 0;
}

.import-circle {
    background: url(../images/icons/contact_import_rounded.png) top no-repeat;
    color: var(--primary-black, #000000);
    float: left;
    font-size: 16px;
    height: 95px;
    margin: 0 0 50px 80px;
    padding: 32px 0 0 0;
    text-align: center;
    width: 95px;
}

#contact-import-outer {
    padding: 50px 0 0 0;
}

.import-img-height {
    height: 80px;
}

.import-circle a {
    color: var(--primary-black, #000000);
    font-size: 16px;
}

ul.tree, ul.tree ul {
    background: url(../images/vline.png) repeat-y;
    list-style-type: none;
    margin: 0 0 0 7px;
    padding: 0;
}

ul.tree ul {
    margin-left: 10px;
}

ul.tree li {
    background: url(../images/node.png) no-repeat;
    font-size: 12px;
    font-weight: 300;
    line-height: 20px;
    margin: 0;
    padding: 0 12px;
}

ul.tree li.last {
    background: var(--primary-white, #FFFFFF) url(../images/lastnode.png) no-repeat;
}

.activity-timestamp {
    font-size: 11px;
    color: #6c6c6c;
}

h4.media-heading {
    margin: 0;
    font-weight: 300;
    font-size: 16px;
    color: var(--primary-black, #000000);
}

.treeheading {
    background: url(../images/icons/icon_folder_open.png) 0 0 no-repeat;
    color: #e20908;
    font-size: 14px;
    margin: 10px 0 0 0;
    padding: 0 0 0 25px;
}

.contact-email a, .tree a {
    color: var(--primary-black, #000000);
}

.contact-email a:hover, .tree a:hover {
    color: #e20908;
}

.tree {
    margin-top: 5px;
    color: var(--primary-black, #000000);
}

.toggle-setup {
    background: url(../images/togglewidth_open.png) top right no-repeat;
    border: none;
    color: var(--primary-black, #000000);
    cursor: pointer;
    font-size: 16px;
    height: 26px;
    line-height: 16px;
    padding: 0;
    padding: 14px 0 0 0;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
    width: 100%;
}

.toggle-setup h3 {
    color: var(--primary-black, #000000);
    font-size: 16px;
    font-weight: 300;
    line-height: 16px;
    margin: 0;
    margin-left: 18px;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
}

.toggle-setup.collapsed {
    background: url(../images/togglewidth_closed.png) top right no-repeat;
}

.toggle-setup-inner {
    border-bottom: 1px solid #eee;
    padding: 10px 18px;
}

.toggle-cal-inner {
    background: url(../images/icons/cal_toggle_shadow.png) bottom repeat-x;
    border-bottom: 1px solid #eee;
    padding: 10px 18px;
}

.calendar-left-side {
    float: left;
    padding: 20px 0 20px 0;
}

.calendar-left-side td {
    border: 1px solid #e0e0e0;
    height: 40px;
    text-align: center;
    width: 75px;
}

.calendar-left-side a {
    background: #c00100;
    color: var(--primary-white, #FFFFFF);
    display: block;
    height: 30px;
    padding: 10px 0 0 0;
    text-align: center;
    width: 75px;
}

.calendar-viewswitch {
    float: right;
    position: absolute;
    right: 10px;
    top: 0;
    margin: 10px 0 0 10px;
}

.viewswitch-cal {
    background: url(../images/icons/btn_cal_viewcalendar.png) no-repeat;
    color: var(--primary-white, #FFFFFF);
    display: block;
    float: right;
    font-size: 11px;
    height: 30px;
    line-height: 20px;
    margin: 10px 0 0 4px;
    padding: 0;
    cursor: pointer;
    width: 140px;
}

.viewswitch-list {
    background: url(../images/icons/btn_cal_listview.png) no-repeat;
    color: var(--primary-white, #FFFFFF);
    display: block;
    float: right;
    font-size: 11px;
    height: 30px;
    cursor: pointer;
    line-height: 20px;
    margin: 10px 0 0 4px;
    padding: 0;
    width: 115px;
}

.table th {
    line-height: 16px;
    padding-left: 20px;
}

#manageDashboardTable .table th {
    white-space: pre;
    overflow: hidden;
    text-overflow: ellipsis;
    color:var(--primary-black,#000000);
    font-weight: 700;
}

#manageDashboardTable table.table {
    table-layout: fixed;
    height: auto;
}

#manageDashboardTable table.table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #212529;
}
#manageDashboardTable .fullwidth-box-top .add-minwidth h2{
    color:var(--primary-black);
}

#manageDashboardTable table.table td a {
    color: #212529;
}

#manageDashboardTable table.table td a:focus {
    outline: auto;
}

#formDistDelegateTableContainer .table th {
    padding-left: 4px;
    padding-right: 4px;
}

#deReactivateActionTable .modal-body {
    padding: 15px 0;
}

#deReactivateActionTable .table {
    width: 100%;
}

#deReactivateActionTable .table th, #deReactivateActionTable .table td {
    padding: 8px 4px;
}

input[type="radio"], input[type="checkbox"] {
    margin: 4px 0 0;
}

.loading {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    text-align: center;
    z-index: 4;
}

.loading:before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.loading.loading-no-record-msg:before, .loading.loading-no-record-msg:after {
    content: initial;
    display: none;
}

.loading>span.progress-span {
    background: url("/images/adoddle.gif") no-repeat scroll 0 0 / cover rgba(0, 0, 0, 0);
    display: inline-block;
    height: 67px;
    width: 118px;
    border: medium none;
    box-shadow: none;
    position: relative;
    left: 50%;
    margin-left: -59px;
}

#tableProductContainer .table th {
    line-height: 16px;
    padding-left: 20px;
    font-size: 14px;
}

.table th a {
    color: #505050;
}

.table td {
    color: var(--primary-black, #000000);
    padding-left: 20px;
}

.table td.text-center {
    text-align: center;
}

.table td a:hover {
    color: var(--primary-color, #3569AE);
}

.reduceheight {
    line-height: 12px;
    padding: 40px 0;
}

.calendar-module span.add-on {
    height: 27px;
    padding-top: 8px;
}

.calendar-module {
    margin-bottom: 10px;
}

.calendar-module th {
    font-weight: 300;
    padding: 0;
    line-height: 30px;
    text-align: left;
    width: 45px;
}

.calendar-module .input-append {
    width: 130px;
}

.nav-editactDetails h2, .nav-subscription h2 {
    padding-left: 12px;
    padding-right: 12px;
    cursor: pointer;
}
.nav-editactDetails, .nav-subscription {
    border-bottom: 3px solid transparent;
}

.nav-editactDetails:hover, .nav-editactDetails:focus, .nav-editactDetails.active, .nav-subscription:hover, .nav-subscription:focus, .nav-subscription.active {
    border-bottom: 3px solid var(--primary-color, #3569AE) !important;
}

.myaccount-left {
    float: left;
}

#formMyAccount .myaccount-left {
    margin-right: 65px;
}

.myaccount-right {
    float: left;
    line-height: 20px;
    margin-left: 40px;
    width: 400px;
}

#formMyAccount .myaccount-right {
    width: 600px;
    margin-left: 0;
}

#formMyAccount .myaccount-right .tipText {
    float: left;
    margin-left: 0;
    padding-top: 24px;
    width: 450px;
}

#formMyAccount .passContainer {
    position: relative;
}

#formMyAccount .myaccount-right .fileupload .btn-secondary:has(input#userPhoto:focus){
    background-color: var(--secondary-btn-focus-bg, #D4EAFF);
    color: var(--secondary-btn-focus-text, #244674);
    border: var(--secondary-btn-focus-border);
    outline: 0;
    box-shadow: none;
}

.myaccount-right h3 {
    margin: 0;
}

.input-smaller {
    width: 80px;
}

.input-xlarge {
    width: 330px;
}

select.input-xlarge {
    cursor: default;
    height: auto;
    padding: 5px 10px;
    width: 344px;
}

#formMyAccount select.input-xlarge {
    width: 352px;
}

#dataModel select.input-xlarge {
    cursor: default;
    height: auto;
    padding: 5px 10px;
    width: 252px;
}

#myModal-modeledit select.input-xlarge {
    cursor: default;
    height: auto;
    padding: 5px 10px;
    width: 345px;
}

select.input-large {
    height: auto;
    padding: 7px 10px 4px 10px;
    width: 225px;
}

select.createforms {
    color: var(--primary-black, #000000);
    height: auto;
    padding: 9px 10px 9px 10px;
    width: 225px;
}

.input-contacts {
    height: auto;
    margin: 13px 10px 0 0;
    padding: 7px 10px 4px 10px;
    width: 170px;
}

select.drop-access {
    position: absolute;
    right: 10px;
    top: 10px;
    font-size: 12px;
    color: var(--primary-black, #000000);
    height: auto;
    padding: 5px 10px 5px 10px;
    width: 125px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.form-dotted-line {
    border-bottom: 1px dotted #dcdcdc;
    font-size: 0;
    line-height: 0;
    margin: 30px 0;
    width: 99%;
}

#formMyAccount .form-dotted-line {
    margin: 11px 0;
}

.calendar-form .form-horizontal .control-group {
    margin-bottom: 0;
    padding: 0;
}

.calendar-form .form-horizontal .control-group.myAccount-controls {
    padding: 10px;
}

.calendar-form .form-horizontal .control-label {
    cursor: default;
    margin-bottom: 0;
    padding: 6px 0 0;
}

.formitem-group {
    margin-bottom: 10px;
    min-height: 40px;
}

.formitem-group strong {
    padding: 6px 0 0 0;
    display: block;
}

input.styled {
    margin: 0;
    padding: 0;
}

.pad-checks {
    padding: 0;
}

.pad-checks td {
    vertical-align: top;
}

.alarm-cal-table label {
    float: left;
    padding: 10px 0 0 4px;
}

.alarm-cal-table th {
    padding: 0 14px 0 0;
    vertical-align: top;
}

.alarm-cal-table td {
    padding: 0 4px 0 0;
}

.alarm-cal-table .input-mini {
    width: 20px;
}

.select-cal {
    height: auto;
    margin: 0 10px 0 0;
    padding: 9px 10px 9px 10px;
    width: 170px;
}

.view-switch {
    float: left;
    position: relative;
    line-height: 20px;
    margin: 0 0 20px 20px;
    width: 200px;
    height: 164px;
}

#fourCol .view-switch label {
    line-height: 20px;
    padding: 0 5px 0 0;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 87%;
}

#createForm #fourCol .view-switch label {
    float: left;
}

.view-switch .private-file {
    position: absolute;
    bottom: 7px;
    right: 0;
}

.view-switch label input {
    margin: 0 4px 0 0;
}

.pad-checks label {
    padding: 0 0 0 5px;
}

.view-switch-top {
    float: left;
    width: 100%;
    padding: 0;
    height: 140px;
    margin: 0 0 5px 0;
    border: 1px solid #e2e2e2;
    overflow: hidden;
    text-align: center;
    -webkit-border-radius: 13px;
    -moz-border-radius: 13px;
    border-radius: 13px;
}

.view-switch-top img {
    width: 100%;
}

.view-switch-outer {
    padding: 20px 0 0 0;
}

#contactListingSection .view-switch-outer {
    padding: 0px;
    display: flex;
    height: 100%;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding-right: 15px;
    align-content: flex-start;
}

.view-switch-outer .rowSelected {
    background: none!important;
}

.toggle-calendar {
    background: url(../images/togglewidth_open.png) top right no-repeat;
    border: none;
    color: var(--primary-black, #000000);
    cursor: pointer;
    font-size: 16px;
    height: 40px;
    line-height: 16px;
    padding: 0;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
    width: 100%;
}

.toggle-calendar h3 {
    color: var(--primary-black, #000000);
    font-size: 16px;
    font-weight: 300;
    line-height: 16px;
    margin: 0;
    margin-left: 18px;
    padding: 12px 0 0 0;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
    width: 10%;
}

.toggle-calendar.collapsed {
    background: url(../images/togglewidth_closed.png) top right no-repeat;
}

.cal-event-count {
    float: left;
    line-height: 40px;
    margin-left: 45%;
}

.link-cal-left:hover, .link-cal-left {
    background: #ff3433 url(../images/icons/cal_left.png) 8px 7px no-repeat;
    color: var(--primary-white, #FFFFFF);
    width: 40px;
    font-size: 11px;
    line-height: 20px;
    float: right;
    display: block;
    height: 20px;
    margin: 10px 0 0 4px;
    padding: 0 0 0 20px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.link-cal-right:hover, .link-cal-right {
    margin: 10px 10px 0 4px;
    background: #ff3433 url(../images/icons/cal_right.png) 45px 7px no-repeat;
    color: var(--primary-white, #FFFFFF);
    float: right;
    width: 45px;
    font-size: 11px;
    line-height: 20px;
    height: 20px;
    padding: 0 0 0 15px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.toggle-calendar-blank {
    background: url(../images/togglewidth_blank.png) top right no-repeat;
    border: none;
    color: var(--primary-black, #000000);
    cursor: pointer;
    font-size: 16px;
    height: 40px;
    line-height: 16px;
    padding: 0;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
    width: 100%;
}

.toggle-calendar-blank h3 {
    color: var(--primary-black, #000000);
    font-size: 16px;
    font-weight: 300;
    line-height: 16px;
    margin: 0;
    margin-left: 18px;
    padding: 12px 0 0 0;
    text-shadow: .1em .1em .05em var(--primary-white, #FFFFFF);
    width: 10%;
}

.toggle-calendar-blank.collapsed {
    background: url(../images/togglewidth_blank.png) top right no-repeat;
}

.badge-red {
    background: #d80100;
    float: left;
    line-height: 14px;
    margin: 8px 10px 0 15px;
    padding: 6px 8px;
}

.badge-grey {
    background: #b5b5b5;
    float: left;
    line-height: 14px;
    margin: 8px 10px 0 15px;
    padding: 6px 8px;
}

.calendar-form {
    line-height: 16px;
    padding: 30px 0 0 18px;
}
.subscription-form{
    line-height: 16px;
    padding: 15px 18px 0px 18px;
}

#plan {
    padding: 5px;
    background-color: var(--primary-white, #FFFFFF);
    border: 1px solid #ccc;
    border-radius: .25rem;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    margin-left: 10px;
    margin-top: 10px;
    margin-right: 10px;
}
.group-sub{
    padding:  10px;
}
.group-sub-last{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
}
.sub-div-last{
    display: flex;
}
.labelTextMain{
    font-size: 16px;
    margin-left: 10px;
    color: var(--primary-color, #3569AE);
    font-weight: bold;
}
.sub-div-last .labelText{
    font-size: 13px;
    margin-left: 15px;
    font-weight: bold;
}
.labelValue{
    font-size: 14px;
    margin-left: 3px;
}
#addModel .calendar-form {
    line-height: 16px;
    padding: 10px 0 0 18px;
}

.calendar-form .control-label {
    font-size: 14px;
    text-align: left;
}

.calendar-form h4 {
    font-size: 16px;
}

.calendar-form .btn {
    margin: 0 5px 0 0;
}

.calendar-form .control-group {
    margin-bottom: 0;
    padding: 0;
}

.calendar-form textarea, .calendar-form input[type="text"], .calendar-form input[type="password"], .calendar-form input[type="datetime"], .calendar-form input[type="datetime-local"], .calendar-form input[type="date"], .calendar-form input[type="month"], .calendar-form input[type="time"], .calendar-form input[type="week"], .calendar-form input[type="number"], .calendar-form input[type="email"], .calendar-form input[type="url"], .calendar-form input[type="search"], .calendar-form input[type="tel"], .calendar-form input[type="color"], .calendar-form .uneditable-input {
    border: 1px solid #d3d3d3;
    padding: 5px 10px;
    color: var(--primary-black, #000000);
    margin-bottom: 0;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    transition: border .2s linear 0s, box-shadow .2s linear 0s;
}

.calendar-list {
    padding: 0 0 20px 0;
}

.calendar-dotted-line {
    border-bottom: 1px dotted #dcdcdc;
    font-size: 0;
    line-height: 0;
    margin: 0 0 20px 0;
    width: 99%;
}

#january-list, #february-list, #march-list, #april-list, #may-list, #june-list, #july-list, #august-list, #september-list, #october-list, #november-list, #december-list {
    display: none;
}

table.myfluid-outer {
    width: 100%;
}

div.myfluid-outer {
    width: 100%;
    height: 100%;
    border-top: 1px solid #eeeded;
    box-sizing: border-box;
}

td.myfluid-td {
    width: 100%;
    vertical-align: top;
}

table.myfluid-outer th {
    vertical-align: top;
}

.calendar-form input.input-selectdrop {
    height: auto;
    background: var(--primary-white, #FFFFFF);
    padding: 7px 10px 4px 10px;
    width: 300px;
}

.input-select-drop ul.dropdown-menu {
    background-clip: padding-box;
    background-color: var(--primary-white, #FFFFFF);
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px 6px 6px 6px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    border-top: none;
    list-style: none outside none;
    margin: 2px 0 0;
    width: 348px;
    padding: 5px 0;
    position: absolute;
    top: 39px;
    z-index: 1000;
}

.btn-selectdrop {
    display: inline-block;
    *display: inline;
    padding: 14px 10px 0 10px;
    margin-bottom: 0;
    *margin-left: .3em;
    height: 41px;
    font-size: 14px;
    line-height: 20px;
    color: #333;
    text-align: center;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
    vertical-align: middle;
    cursor: pointer;
    background-color: #f5f5f5;
    *background-color: #e6e6e6;
    background-image: -moz-linear-gradient(top, var(--primary-white, #FFFFFF), #e6e6e6);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(var(--primary-white, #FFFFFF)), to(#e6e6e6));
    background-image: -webkit-linear-gradient(top, var(--primary-white, #FFFFFF), #e6e6e6);
    background-image: -o-linear-gradient(top, var(--primary-white, #FFFFFF), #e6e6e6);
    background-image: linear-gradient(to bottom, var(--primary-white, #FFFFFF), #e6e6e6);
    background-repeat: repeat-x;
    border: 1px solid #ccc;
    *border: 0;
}

.select-drop-content {
    padding: 10px;
    position: relative;
}

.select-drop-content {
    padding: 10px;
    position: relative;
}

#publicApps .boxgrid {
    margin: 0 0 10px 30px!important;
}

.model-summary-body ul {
    max-height: 200px;
    min-width: 180px;
    max-width: 200px;
    width: auto;
}

.model-summary-body li.active > a {
    color: #333333;
}

.model-summary-body li > a {
    padding: 3px 5px;
}

.model-summary-body li > a > div {
    text-overflow: ellipsis;
    overflow: hidden;
}

@media(max-width: 767px) {
    #footer {
        margin-left: -20px;
        margin-right: -20px;
        padding-left: 20px;
        padding-right: 20px;
    }
}

@media(max-width: 1400px) {
    #my-nav .nav>li {
        padding: 0;
        margin: 0;
    }
    .boxgrid {
        margin: 0 0 20px 10px;
    }
    #publicApp .boxgrid {
        margin: 0 0 20px 30px!important;
    }
}

/* @media(max-width: 1500px) {
    #my-nav .nav>li>a {
        width: 120px;
    }
} */

@media(max-width: 1300px) {
    /* #my-nav .nav>li>a {
        width: 120px;
    } */
    #my-nav .nav>li {
        padding: 0 2px;
        margin: 0;
    }
    .boxgrid {
        margin: 0 0 20px 10px;
    }
    #publicApp .boxgrid {
        margin: 0 0 20px 30px!important;
    }
}

a#sidenav-search-apps {
    background: url(../images/icons/leftnav_off.png) no-repeat;
    color: var(--primary-black, #000000);
}

@media(max-width: 1200px) {
    /* #my-nav .nav>li>a {
        width: 95px;
    } */
    #my-nav .nav>li {
        padding: 0;
        margin: 0;
    }
}

.projects-settings-table td {
    border-bottom: 1px dotted #DCDCDC;
    padding: 4px 15px 5px;
    cursor: pointer;
}

.projects-settings-table td.last {
    border-bottom: none;
    padding: 4px 15px 5px;
    cursor: pointer;
}

.projects-settings-table td:hover {
    background: #F8F8F8;
}

.icons {
    display: inline-block;
    width: 14px;
    height: 14px;
    margin-top: 1px;
    *margin-right: .3em;
    line-height: 14px;
    vertical-align: text-top;
    background-image: url("../images/glyphicons-halflings.png");
    background-repeat: no-repeat;
}

.projects-settings-table span {
    padding-left: 5px;
}

.brand {
    padding: 3px;
    margin-left: 0;
}

.brand-logo {
    margin: 2px 10px 0px 5px;
}

.brand-logo img {
    margin: 0 0 0 4px;
}

.brand-logo .activeEditionName {
    margin-bottom: -4px;    
}

#calendar-drop, #calendar-drop-test {
    float: right;
    height: 39px;
    margin: 0;
}

#user-loggedin {
    float: right;
    font-size: 12px;
    height: 39px;
    margin: 0;
    padding: 0;
}

#user-loggedin button {
    height: 40px;
}

#adoddlecorporate button {
    font-size: 14px;
    border: 1px solid #d3d3d3;
    margin-top: 4px;
    padding: 5px 30px 5px 15px;
}

#user-loggedin .check-list-item a:hover, .actionsDropdown .check-list-item:not(.disabled) a:hover, #actionsDropdown .check-list-item:not(.disabled) a:hover {
    color: var(--dark-gray, #333333);
    background: none!important;
}

#user-loggedin .check-list-item a:hover {
    color: var(--dark-gray, #333333);
    background: none!important;
}

.actionsDropdown .check-list-item.disabled a:hover, #actionsDropdown .check-list-item.disabled a:hover {
    background: none!important;
}

.actionsDropdown .check-list-item.disabled img, #actionsDropdown .check-list-item.disabled img {
    opacity: .5;
}

.actionsDropdown .check-list-item.disabled, #actionsDropdown .check-list-item.disabled {
    cursor: default;
}

#user-loggedin .check-list-item a.activeMyAccount:hover {
    color: var(--primary-white, #FFFFFF);
    background: var(--primary-color, #3569AE)!important;
}

.filterui-list-section .check-list-item a {
    font-size: 12px;
}

#my-nav .filterui-list-section .check-list-item a {
    font-size: 14px;
    text-align: left;
}

.filterui-list-section .check-list-item a:hover, .filterui-list-section .check-list-item a:focus {
    color: #212121;
}

.filterui-group:first-child {
    margin-top: 0;
}

#user-loggedin-test {
    float: right;
    font-size: 12px;
    height: 34px;
    margin: 0;
    border-left: 1px dotted #D4D4D4;
}

#header-branding-info-link, #header-branding-link1, #header-branding-link2, #header-branding-link3, #header-branding-vimeo-link {
    float: right;
    font-size: 12px;
    height: 39px;
    margin-right: 10px;
}

#header-apps-link {
    float: right;
}

#header #header-branding-vimeo-link div {
    background: url(/images/icons/ttt/vimeo.png) no-repeat scroll 0 0 transparent;
    background-size: 100% 100%;
    height: 32px;
    width: 36px;
    display: inline-block;
}

#header-apps-link i.fa.adoddle-icon.i-cloud {
    font-size: 26px;
}

#header-apps-link a, #header-branding-info-link a, #header-branding-vimeo-link a, #header-branding-link1 a, #header-branding-link2 a, #header-branding-link3 a {
    display: inline-block;
    padding: 5px;
}

.upgrade-account a, #header-apps-link a {
    color: #cdcdeb;
    font-size: 14px;
    height: 23px;
    padding: 9px 10px;
    border-radius: 0;
}

#user-loggedin-test button {
    height: 40px;
}

#calendar-drop-test button {
    padding: 9px;
    height: 41px;
    border-radius: 0;
}

#adoddlecorporate {
    float: left;
    font-size: 12px;
    height: 36px;
    margin-right: 10px;
    padding-top: 5px;
}
.bidwinnerText {
    padding-top: 4px !important;
    margin-right: 0px !important;
}
.bidwinnerText > div > a{
    font-size: 18px !important;
}
#header .bidwinnerText .dropdown-toggle {
    border: none !important;
}

#help {
    float: right;
    font-size: 12px;
    height: 40px;
    margin: 0;
}

#adoddle {
    float: right;
    height: 30px;
    margin: 8px 0 0;
    padding: 0 5px 10px;
    display: none;
}

.meeting-group>.btn {
    border-radius: 0 !important;
    border: 0 !important;
}

.meeting-group ul {
    min-width: 140px;
}

.meeting-group ul li {
    padding: 0 !important;
}

.meeting-group ul li a {
    padding: 4px 10px !important;
}

.meeting-group ul li a img {
    vertical-align: middle;
    margin-right: 5px;
    margin-top: -1px;
    width: 20px;
}

.video-meeting {
    float: right;
    padding: 7px;
    border-radius: 0px;
    height: 41px;
    box-sizing: border-box;
}

.video-meeting i.fa {
    color: var(--primary-white, #FFFFFF);
    font-size: 13px;
    padding: 5px;
    border-radius: 50%;
    border: 2px solid var(--primary-white, #FFFFFF);
}

.video-meeting:hover {
    background-color: rgba(255, 255, 255, 0.45);
}

.video-meeting:hover i.fa {
    color: var(--primary-white, #FFFFFF);
    border-color: var(--primary-white, #FFFFFF);
}

#help .helpbrand {
    float: right;
    padding: 6px 7px;
    border-color: transparent;
    height: 27px;
    border-radius: 0;
}

#help .helpbrand i {
    font-size: 27px;
}

.helpbrand.inner-download-modal {
    position: relative;
    top: -3px;
}

#user-loggedin .user-toggle .caret, #adoddlecorporate .user-toggle .caret, .actionsDropdown .user-toggle .caret, #actionsDropdown .user-toggle .caret {
    margin: 17px 0 0 0;
}

#user-loggedin .caret, #calendar-drop .caret {
    background: url("/images/icons/icon_black_down.png") no-repeat scroll right center transparent;
    border: medium none;
    height: 6px;
    margin: 12px 0 0;
    width: 9px;
}

#adoddlecorporate ul {
    width: 150px;
}

.actionsDropdown .caret, #actionsDropdown .caret {
    margin: 2px 0 0;
    width: 20px;
    border: medium none;
    height: 15px;
    background: url("/images/icons/icon_black_down.png") no-repeat scroll center center transparent;
}

#header .dropdown-toggle {
    background: none repeat scroll 0 0 transparent;
    box-shadow: none;
    color: var(--primary-white, #FFFFFF);
    text-decoration: none;
    text-shadow: none;
    font-size: 14px;
}

#header #adoddlecorporate .dropdown-toggle {
    border: 1px solid var(--primary-white, #FFFFFF);
    z-index: 0;
    color: var(--primary-white, #FFFFFF);
}

#header .notification.dropdown .dropdown-toggle {
    height: 23px;
}

#header #adoddlecorporate .dropdown-toggle:focus, #header #adoddlecorporate .dropdown-toggle:hover, #header .btn.dropdown-toggle:hover, #header .notification.dropdown .dropdown-toggle:hover, #header .filterui-button.filterui-button-subtle:hover, #header .filterui-button.filterui-button-subtle:focus, #header #icon3dRepo .dropdown-toggle:hover, #header #icon3dRepo .dropdown-toggle:focus-visible {
    color: var(--primary-white, #FFFFFF);
    background-color:var(--primary-bg-hover);
    border-color: var(--primary-white, #FFFFFF);
}
#header #icon3dRepo {
    min-width: 41px;
    min-height: 41px;
    float: right;
}
#header #icon3dRepo .dropdown-toggle {
    display: inline-block;
    padding: 7px 4px 0 7px;
}

.portlet-header:focus.ui-adoddle-widget-header.dashboard-box-top:focus-visible {
    background:var(--light-hover-color);
}

#header #adoddlecorporate .dropdown-toggle:focus .caret, #header #adoddlecorporate .dropdown-toggle:hover .caret {
    border-top-color: var(--primary-white, #FFFFFF);
}

#header .filterui-button.filterui-button-subtle:hover, #header #help .helpbrand:hover {
    color: var(--primary-white, #FFFFFF);
    background-color: var(--primary-bg-hover);
    border-color: transparent;
}

#header .open .btn.dropdown-toggle, #header .notification.dropdown.open .dropdown-toggle, #header .filterui-button.active {
    color: var(--primary-white, #FFFFFF);
    background-color: var(--primary-bg-hover);
    border-color: var(--primary-white, #FFFFFF);
}

#header #user-loggedin .dropdown-toggle {
    z-index: 0;
    padding: 0 10px;
    border-radius: 0;
    height: 41px;
    display: inline-block;
    max-width: 41px;
}

#header #user-loggedin .dropdown-menu {
    padding: 0;
    margin: 0;
}

#header #user-loggedin .dropdown-menu>li {
    padding: 0;
}

#header #user-loggedin .dropdown-menu>li>a {
    padding: 7px 15px;
    font-size: 14px;
}

#header .caret {
    border-top-color: var(--primary-white, #FFFFFF);
}

.export-dropdown .btn-group>.btn, .export-dropdown .btn-group>.dropdown-menu, .export-dropdown .btn-group>.popover {
    font-size: 12px;
}

.export-dropdown .dropdown-menu>li>a {
    padding: 3px 5px;
}

.export-dropdown button {
    font-size: 12px;
    margin-top: 0;
}

#viewFormHeaderTable .col3.divth span.form-action-li-span {
    position: relative;
    left: 5px;
    float: left;
    width: 78%;
    margin-top: -2px;
    padding-left: 0;
    min-height: 20px;
    height: 20px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
}

#viewFormHeaderTable .export-dropdown button {
    top: 10px;
}

#viewFormHeaderTable .export-dropdown .dropdown-menu>li>a {
    padding: 3px 0;
}

#pws_container .btn-pink {
    background: none repeat scroll 0 0 var(--primary-color, #3569AE);
    border: 1px solid var(--primary-color, #3569AE);
    color: white;
    margin-right: 5px;
}

#calendar-drop .dropdown-toggle {
    background: none repeat scroll 0 0 transparent;
    border: medium none;
    box-shadow: none;
    text-decoration: none;
}

.user-toggle img {
    margin: 0 0 0 10px;
}

.adoddle {
    border: 0 none;
    height: auto;
    max-width: 100%;
    vertical-align: middle;
}

#adoddle-in {
    background: none repeat scroll 0 0 #F3F3F3;
}

#adoddlecorporate .grey-out {
    color: #C0C0C0;
}

#myModal-upload.modal {
    width: 677px;
    max-height: 84%;
}

#assocViewsContent .modal-body {
    padding: 0;
}

#assocViewsContent .modal-body .nopadding-box-lower, #assocViewsContent .modal-body .fullwidth-box-top {
    border: none;
    border-radius: 0;
}

#assocViewsContent .modal-body .project-blocks-container {
    max-height: 550px;
    overflow: auto;
    margin-left: 0;
    text-align: center;
}

.myModal-upload-attrib {
    width: 84%!important;
}

#myModal-upload.modal.myModal-upload-attrib {
    max-height: none;
}

.select2-container {
    margin: 0;
    position: relative;
    display: inline-block;
    zoom: 1;
    *display: inline;
    vertical-align: middle;
    margin: 0 0 10px;
    min-height: 32px;
    max-height: 64px;
    overflow: auto;
    line-height: 30px;
}

.select2-container, .select2-drop, .select2-search, .select2-search input {
    -webkit-box-sizing: border-box;
    -khtml-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 14px;
}

.select2-container .select2-choice {
    display: block;
    height: 26px;
    padding: 0 0 0 8px;
    overflow: hidden;
    position: relative;
    border: 1px solid #aaa;
    white-space: nowrap;
    line-height: 26px;
    color: #444;
    text-decoration: none;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: var(--primary-white, #FFFFFF);
    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.5, white));
    background-image: -webkit-linear-gradient(center bottom, #eee 0, white 50%);
    background-image: -moz-linear-gradient(center bottom, #eee 0, white 50%);
    background-image: -o-linear-gradient(bottom, #eee 0, var(--primary-white, #FFFFFF) 50%);
    background-image: -ms-linear-gradient(top, var(--primary-white, #FFFFFF) 0, #eee 50%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='var(--primary-white, #FFFFFF)', endColorstr='#eeeeee', GradientType=0);
    background-image: linear-gradient(top, var(--primary-white, #FFFFFF) 0, #eee 50%);
}

.select2-container.select2-drop-above .select2-choice {
    border-bottom-color: #aaa;
    -webkit-border-radius: 0 0 4px 4px;
    -moz-border-radius: 0 0 4px 4px;
    border-radius: 0 0 4px 4px;
    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.9, white));
    background-image: -webkit-linear-gradient(center bottom, #eee 0, white 90%);
    background-image: -moz-linear-gradient(center bottom, #eee 0, white 90%);
    background-image: -o-linear-gradient(bottom, #eee 0, white 90%);
    background-image: -ms-linear-gradient(top, #eee 0, var(--primary-white, #FFFFFF) 90%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='var(--primary-white, #FFFFFF)', endColorstr='#eeeeee', GradientType=0);
    background-image: linear-gradient(top, #eee 0, var(--primary-white, #FFFFFF) 90%);
}

.select2-container.select2-allowclear .select2-choice span {
    margin-right: 42px;
}

.select2-container .select2-choice span {
    margin-right: 26px;
    display: block;
    overflow: hidden;
    white-space: nowrap;
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
}

.select2-container .select2-choice abbr {
    display: none;
    width: 12px;
    height: 12px;
    position: absolute;
    right: 24px;
    top: 8px;
    font-size: 1px;
    text-decoration: none;
    border: 0;
    background: url('select2.png') right top no-repeat;
    cursor: pointer;
    outline: 0;
}

.select2-container.select2-allowclear .select2-choice abbr {
    display: inline-block;
}

.select2-container .select2-choice abbr:hover {
    background-position: right -11px;
    cursor: pointer;
}

.select2-drop-mask {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 100000;
    opacity: 0;
    filter: "alpha(opacity=0)";
    filter: alpha(opacity=0);
    background-color: var(--primary-white, #FFFFFF);
}

.select2-drop {
    width: 100%;
    margin-top: -1px;
    position: absolute;
    z-index: 100001;
    top: 1px;
    background: var(--primary-white, #FFFFFF);
    color: var(--primary-black, #000000);
    border: 1px solid #aaa;
    border-top: 0;
    -webkit-box-shadow: 0 4px 5px rgba(0, 0, 0, .15);
    -moz-box-shadow: 0 4px 5px rgba(0, 0, 0, .15);
    box-shadow: 0 4px 5px rgba(0, 0, 0, .15);
}

.select2-drop-auto-width {
    border-top: 1px solid #aaa;
    width: auto;
}

.select2-drop-auto-width .select2-search {
    padding-top: 4px;
}

.select2-drop.select2-drop-above {
    margin-top: 1px;
    border-top: 1px solid #aaa;
    border-bottom: 0;
    -webkit-border-radius: 4px 4px 0 0;
    -moz-border-radius: 4px 4px 0 0;
    border-radius: 4px 4px 0 0;
    -webkit-box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);
    -moz-box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);
    box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);
}

.select2-container .select2-choice div {
    display: inline-block;
    width: 18px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    border-left: 1px solid #aaa;
    -webkit-border-radius: 0 4px 4px 0;
    -moz-border-radius: 0 4px 4px 0;
    border-radius: 0 4px 4px 0;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    background: #ccc;
    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #ccc), color-stop(0.6, #eee));
    background-image: -webkit-linear-gradient(center bottom, #ccc 0, #eee 60%);
    background-image: -moz-linear-gradient(center bottom, #ccc 0, #eee 60%);
    background-image: -o-linear-gradient(bottom, #ccc 0, #eee 60%);
    background-image: -ms-linear-gradient(top, #ccc 0, #eee 60%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#cccccc', GradientType=0);
    background-image: linear-gradient(top, #ccc 0, #eee 60%);
}

.select2-container .select2-choice div b {
    display: block;
    width: 100%;
    height: 100%;
    background: url('select2.png') no-repeat 0 1px;
}

.select2-search {
    display: inline-block;
    width: 100%;
    min-height: 26px;
    margin: 0;
    padding-left: 4px;
    padding-right: 4px;
    position: relative;
    z-index: 10000;
    white-space: nowrap;
}

.select2-search input {
    width: 100%;
    height: auto!important;
    min-height: 26px;
    padding: 4px 20px 4px 5px;
    margin: 0;
    outline: 0;
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
    font-size: 1em;
    border: 1px solid #aaa;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    background: var(--primary-white, #FFFFFF) url('select2.png') no-repeat 100% -22px;
    background: url('select2.png') no-repeat 100% -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, white), color-stop(0.99, #eee));
    background: url('select2.png') no-repeat 100% -22px, -webkit-linear-gradient(center bottom, white 85%, #eee 99%);
    background: url('select2.png') no-repeat 100% -22px, -moz-linear-gradient(center bottom, white 85%, #eee 99%);
    background: url('select2.png') no-repeat 100% -22px, -o-linear-gradient(bottom, white 85%, #eee 99%);
    background: url('select2.png') no-repeat 100% -22px, -ms-linear-gradient(top, var(--primary-white, #FFFFFF) 85%, #eee 99%);
    background: url('select2.png') no-repeat 100% -22px, linear-gradient(top, var(--primary-white, #FFFFFF) 85%, #eee 99%);
}

.ja_JP .select2-search input {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

.select2-drop.select2-drop-above .select2-search input {
    margin-top: 4px;
}

.select2-search input.select2-active {
    background: var(--primary-white, #FFFFFF) url('select2-spinner.gif') no-repeat 100%;
    background: url('select2-spinner.gif') no-repeat 100%, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, white), color-stop(0.99, #eee));
    background: url('select2-spinner.gif') no-repeat 100%, -webkit-linear-gradient(center bottom, white 85%, #eee 99%);
    background: url('select2-spinner.gif') no-repeat 100%, -moz-linear-gradient(center bottom, white 85%, #eee 99%);
    background: url('select2-spinner.gif') no-repeat 100%, -o-linear-gradient(bottom, white 85%, #eee 99%);
    background: url('select2-spinner.gif') no-repeat 100%, -ms-linear-gradient(top, var(--primary-white, #FFFFFF) 85%, #eee 99%);
    background: url('select2-spinner.gif') no-repeat 100%, linear-gradient(top, var(--primary-white, #FFFFFF) 85%, #eee 99%);
}

.select2-container-active .select2-choice, .select2-container-active .select2-choices {
    border: 1px solid var(--primary-color, #3569AE);
    outline: none;
    -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3);
    -moz-box-shadow: 0 0 5px rgba(0, 0, 0, .3);
    box-shadow: 0 0 5px rgba(0, 0, 0, .3);
}

.select2-dropdown-open .select2-choice {
    border-bottom-color: transparent;
    -webkit-box-shadow: 0 1px 0 var(--primary-white, #FFFFFF) inset;
    -moz-box-shadow: 0 1px 0 var(--primary-white, #FFFFFF) inset;
    box-shadow: 0 1px 0 var(--primary-white, #FFFFFF) inset;
    -webkit-border-bottom-left-radius: 0;
    -moz-border-radius-bottomleft: 0;
    border-bottom-left-radius: 0;
    -webkit-border-bottom-right-radius: 0;
    -moz-border-radius-bottomright: 0;
    border-bottom-right-radius: 0;
    background-color: #eee;
    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, white), color-stop(0.5, #eee));
    background-image: -webkit-linear-gradient(center bottom, white 0, #eee 50%);
    background-image: -moz-linear-gradient(center bottom, white 0, #eee 50%);
    background-image: -o-linear-gradient(bottom, white 0, #eee 50%);
    background-image: -ms-linear-gradient(top, var(--primary-white, #FFFFFF) 0, #eee 50%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='var(--primary-white, #FFFFFF)', GradientType=0);
    background-image: linear-gradient(top, var(--primary-white, #FFFFFF) 0, #eee 50%);
}

.select2-dropdown-open.select2-drop-above .select2-choice, .select2-dropdown-open.select2-drop-above .select2-choices {
    border: 1px solid var(--primary-color, #3569AE);
    border-top-color: transparent;
}

.select2-dropdown-open .select2-choice div {
    background: transparent;
    border-left: none;
    filter: none;
}

.select2-dropdown-open .select2-choice div b {
    background-position: -18px 1px;
}

.select2-results {
    max-height: 200px;
    padding: 4px;
    margin: 0;
    overflow: auto;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.select2-results ul.select2-result-sub {
    margin: 0;
    padding-left: 0;
}

.select2-results ul.select2-result-sub>li .select2-result-label {
    padding-left: 20px;
}

.select2-results ul.select2-result-sub ul.select2-result-sub>li .select2-result-label {
    padding-left: 40px;
}

.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub>li .select2-result-label {
    padding-left: 60px;
}

.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub>li .select2-result-label {
    padding-left: 80px;
}

.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub>li .select2-result-label {
    padding-left: 100px;
}

.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub>li .select2-result-label {
    padding-left: 110px;
}

.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub>li .select2-result-label {
    padding-left: 120px;
}

.select2-results li {
    list-style: none;
    display: list-item;
    background-image: none;
}

.select2-results li.select2-result-with-children>.select2-result-label {
    font-weight: bold;
}

.select2-results .select2-result-label {
    padding: 3px 7px 4px;
    margin: 0;
    cursor: pointer;
    min-height: 1em;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.select2-results .select2-highlighted {
    background: var(--primary-color, #3569AE);
    color: var(--primary-white, #FFFFFF);
}

.select2-results li em {
    background: #feffde;
    font-style: normal;
}

.select2-results .select2-highlighted em {
    background: transparent;
}

.select2-results .select2-highlighted ul {
    background: white;
    color: var(--primary-black, #000000);
}

.select2-results .select2-no-results, .select2-results .select2-searching, .select2-results .select2-selection-limit {
    background: #f4f4f4;
    font-size: .8em;
    color: red;
    display: list-item;
}

.select2-results .select2-disabled.select2-highlighted {
    color: #666;
    background: #f4f4f4;
    display: list-item;
    cursor: default;
}

.select2-results .select2-disabled {
    background: #f4f4f4;
    display: list-item;
    cursor: default;
    opacity: 0.5;
    pointer-events: none;
}

.select2-results .select2-selected {
    display: none;
}

.select2-more-results.select2-active {
    background: #f4f4f4 url('select2-spinner.gif') no-repeat 100%;
}

.select2-more-results {
    background: #f4f4f4;
    display: list-item;
}

.select2-container.select2-container-disabled .select2-choice {
    background-color: #f4f4f4;
    background-image: none;
    border: 1px solid #ddd;
    cursor: default;
}

.select2-container.select2-container-disabled .select2-choice div {
    background-color: #f4f4f4;
    background-image: none;
    border-left: 0;
}

.select2-container.select2-container-disabled .select2-choice abbr {
    display: none;
}

.select2-container-multi .select2-choices {
    margin: 0;
    padding: 0;
    cursor: text;
    min-height: 32px;
    height: auto !important;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    background-color: var(--primary-white, #FFFFFF);
    border: 1px solid #ccc;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    text-align: left;
}

.manageRolesTable .select2-container-multi .select2-choices {
    border-color: transparent;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.manageRolesTable .select2-container-multi .select2-choices:hover {
    border-color: #ccc;
}

.manageRolesTable .select2-dropdown-open.select2-drop-above .select2-choices, .select2-container-multi.select2-container-active .select2-choices {
    border: 1px solid var(--primary-color, #3569AE);
    outline: none;
    -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3);
    -moz-box-shadow: 0 0 5px rgba(0, 0, 0, .3);
    box-shadow: 0 0 5px rgba(0, 0, 0, .3);
}

.select2-container-multi .select2-choices .select2-search-field {
    margin: 0;
    padding: 0;
    white-space: nowrap;
    display: inline-block;
}

.select2-container-multi .select2-choices .select2-search-field input {
    padding: 5px;
    margin: 1px 0;
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
    font-size: 100%;
    color: #666;
    outline: 0;
    border: 0;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    background: transparent !important;
    width: 10px;
}

.ja_JP .select2-container-multi .select2-choices .select2-search-field input {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

.select2-container-multi .select2-choices .select2-search-field input.select2-active {
    background: var(--primary-white, #FFFFFF) url('select2-spinner.gif') no-repeat 100%!important;
}

.select2-default {
    color: #999!important;
}

.select2-container-multi .select2-choices .select2-search-choice {
    padding: 4px 6px;
    margin-left: 3px;
    line-height: 13px;
    color: #333;
    cursor: pointer;
    border: 1px solid #aaa;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border-radius: 20px;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: #f8f8f8;
    float: none;
    display: inline-block;
    vertical-align: middle;
    list-style: none;
}

.select2-container-multi .select2-choices .select2-search-choice.user-with-checkbox:hover {
    background-color: var(--secondary-btn-focus-bg, #D4EAFF);
    color: var(--secondary-btn-focus-text, #244674);
    border: var(--secondary-btn-focus-border);
}

.select2-container-multi .select2-choices .select2-search-choice>div {
    display: inline;
    margin-left: 4px;
    font-size: 13px;
}

.select2-container-multi .select2-choices .select2-search-choice.user-with-checkbox:has(.userCheckbox:checked) {
    background-color: var(--secondary-btn-focus-bg, #D4EAFF);
    color: var(--secondary-btn-focus-text, #244674);
    border: var(--secondary-btn-focus-border);  
}

.select2-container-multi .select2-choices .select2-search-choice.user-with-checkbox .userCheckbox {
    margin-left: 2px;
    margin-right: 2px;
    margin-bottom: 1px;
    margin-top: 0;
}

.select2-container-multi .select2-choices .select2-search-choice.user-with-checkbox .userCheckbox + div  {
    margin-right: 6px;
}

.select2-locked {
    cursor: default;
}

.select2-container-multi .select2-choices .select2-search-choice.matched>div {
    color: #c00;
}

.select2-container-multi .select2-choices .select2-search-choice span {
    cursor: default;
}

.select2-container-multi .select2-choices .select2-search-choice-focus {
    background: #d4d4d4;
}

.select2-search-choice-close {
    font-size: 13px !important;
    outline: none;
    color: #888;
    text-align: center;
}

.select2-container-multi.select2-container-disabled .select2-choices {
    background-color: #f4f4f4;
    background-image: none;
    border: 1px solid #ddd;
    cursor: default;
}

.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice {
    padding: 3px 5px 3px 5px;
    border: 1px solid #ddd;
    background-image: none;
    background-color: #f4f4f4;
}

.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close {
    display: none;
    background: none;
}

.select2-result-selectable .select2-match, .select2-result-unselectable .select2-match {
    text-decoration: underline;
    font-weight: bold;
}

.select2-offscreen, .select2-offscreen:focus {
    clip: rect(0 0 0 0);
    width: 1px;
    height: 1px;
    border: 0;
    margin: 0;
    padding: 0;
    overflow: hidden;
    position: absolute;
    outline: 0;
    left: 0;
    visibility: hidden;
}

.select2-display-none {
    display: none;
}

.select2-measure-scrollbar {
    position: absolute;
    top: -10000px;
    left: -10000px;
    width: 100px;
    height: 100px;
    overflow: scroll;
}

@media only screen and
/*!YUI Compressor */

(-webkit-min-device-pixel-ratio: 1.5), only screen and
/*!YUI Compressor */

(min-resolution:144dpi) {
    .select2-search input, .select2-search-choice-close, .select2-container .select2-choice abbr, .select2-container .select2-choice div b {
        background-image: url('select2x2.png')!important;
        background-repeat: no-repeat!important;
        background-size: 60px 40px!important;
    }
    .select2-search input {
        background-position: 100% -21px!important;
    }
}

.commentListchkbox {
    width: 13px;
    padding-left: 15px;
    padding-right: 8px;
}

.commentlisttype {
    width: 60px;
    padding-left: 20px;
    padding-right: 8px;
}

.commentId {
    width: 100px;
    padding-left: 20px;
    padding-right: 8px;
}

.commentlistoriginator {
    width: 130px;
    padding-left: 20px;
    padding-right: 8px;
}

.commentlistUpdated {
    width: 100px;
    padding-left: 20px;
    padding-right: 8px;
}

.commentlistmyact {
    width: 113px;
    padding-left: 20px;
    padding-right: 8px;
}

.commentlistTime {
    width: 100px;
    padding-left: 20px;
    padding-right: 8px;
}

.commentlistattach {
    width: 25px;
    padding-left: 20px;
    padding-right: 4px;
}

.procurListchkbox {
    width: 13px;
    padding-left: 15px;
    padding-right: 8px;
}

.procurlistattach {
    width: 25px;
    padding-left: 20px;
    padding-right: 4px;
}

.procurlistID {
    width: 150px;
    padding-left: 20px;
    padding-right: 8px;
}

.procurlistOrg {
    width: 150px;
    padding-left: 20px;
    padding-right: 8px;
}

.appslistoriginator {
    width: 150px;
    padding-left: 20px;
    padding-right: 8px;
}

.procurlistActions {
    width: 150px;
    padding-left: 20px;
    padding-right: 8px;
}

.procurlistActionTime {
    width: 80px;
    padding-left: 20px;
    padding-right: 8px;
}

.procurlistActionUpdate {
    width: 100px;
    padding-left: 20px;
    padding-right: 8px;
}

.appsListchkbox {
    width: 13px;
    padding-left: 15px;
    padding-right: 8px;
}

.appslistid {
    width: 100px;
    padding-left: 20px;
    padding-right: 8px;
}

.appsliststatus {
    width: 100px;
    padding-left: 20px;
    padding-right: 8px;
}

.appslistoriginator {
    width: 130px;
    padding-left: 20px;
    padding-right: 8px;
}

.appslistmsgid {
    width: 100px;
    padding-left: 20px;
    padding-right: 8px;
}

.appslistattach {
    width: 25px;
    padding-left: 20px;
    padding-right: 4px;
}

.appslistclosedue {
    width: 100px;
    padding-left: 20px;
    padding-right: 8px;
}

.lt-ie9 #search {
    padding: 0;
    height: 30px!important;
}

.lt-ie9 #search_btn {
    padding-top: 0;
    padding-bottom: 0;
    height: 32px!important;
    border-left: 1px solid #CFCFCF;
}

#search_btn span {
    background: url("/images/icons/icon_grey_search.png");
    height: 17px;
    width: 17px;
    display: block;
    float: left;
    margin-top: -2px;
    font-size: 12px;
}

.widgetHeader {
    color: white;
    float: left;
    margin-left: 10px;
    margin-top: 4px;
}

.username {
    display: block;
    float: left;
    margin-top: 8px;
    overflow: hidden;
    text-align: right;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 70px;
}

#adoddlecorporate .value {
    display: block;
    float: left;
    margin-left: 10px;
    overflow: hidden;
    white-space: nowrap;
    width: 140px;
}

#flag img {
    margin-left: 0;
}

#flag-ul {
    left: -187px;
    right: 0;
}

#flag-ul img {
    height: 20px;
    width: 20px;
    float: left;
}

#flag-ul span {
    margin-left: 10px;
}

#my-nav .dropdown-menu li a:focus,
#my-nav .dropdown-menu li a:hover {
    color: var(--primary-black, #000000);
}

#adoddlecorporate .dropdown-menu li a:focus {
    color: var(--gray, #808080);
}

#adoddlecorporate .dropdown-menu li a:hover {
    color: var(--primary-black, #000000);
}

#adoddlecorporate .dropdown-menu li a {
    padding: 0;
}

#my-nav .dropdown-menu li.active a:focus,
#my-nav .dropdown-menu li.active a:hover {
    color: var(--primary-white, #FFFFFF);
}

.text-wrap div span {
    white-space: normal;
}

#distIssuesButton ul.dropdown-menu {
    font-size: 12px;
    padding: 10px 10px 5px 10px;
    top: 22px;
}

#distIssuesButton {
    float: right;
}

#viewformheaderimages {
    text-align: right;
    padding: 5px 10px 5px 0;
    float: right!important;
}

#viewformheaderimages a {
    padding: 0 5px;
}

.checkall {
    padding-top: 6px;
}

.view-switch-procurement {
    float: left;
    line-height: 20px;
    margin: 0 0 20px 20px;
    width: 225px;
}

.view-switch-procurement>label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    cursor: default;
    line-height: .5;
}

.view-switch-procurement input {
    float: left;
    margin-right: 10px;
}

.view-switch-top-procurement {
    float: left;
    width: 220px;
    padding: 0;
    margin: 0 0 10px 0;
    border: 1px solid #e2e2e2;
    overflow: hidden;
    text-align: center;
    -webkit-border-radius: 13px;
    -moz-border-radius: 13px;
    border-radius: 13px;
}

.pagination {
    float: left;
    margin: 0;
    height: 26px;
}

.pagination.pagination-centered {
    position: static;
    margin: 3px 0 0;
}

.pagination.pagination-left {
    height: 30px;
    padding: 4px 10px;
}

#viewFormHeaderTable>.intro-scheme, #viewAssocCommsHeaderTable>.intro-scheme {
    margin: 5px 0;
    background: none repeat scroll 0 0 #E6E6E6;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    font-size: 14px;
    padding: 5px 0;
}

#viewFormHeaderTable .nopadding-box-lower, #viewAssocCommsHeaderTable .nopadding-box-lower {
    border: 0;
}

#viewAssocCommsHeaderTable .nopadding-box-lower {
    overflow: auto;
}

#viewFormHeaderTable .intro-scheme .firstcol {
    width: 28%;
    overflow: hidden;
    display: block;
    float: left;
    padding-left: 15px;
    padding-right: 15px;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#viewFormHeaderTable .intro-scheme .secondcol {
    width: 27%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    float: left;
    padding-left: 10px;
}

#viewFormHeaderTable .intro-scheme .thirdcol {
    width: 35%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    float: left;
    padding-left: 1%;
}

#viewFormHeaderTable .intro-scheme .forthcol {
    width: 5%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    float: left;
}

#viewAssocCommsHeaderTable .intro-scheme .firstcol {
    width: 35%;
    overflow: hidden;
    display: block;
    float: left;
    padding-left: 15px;
    padding-right: 15px;
}

#viewAssocCommsHeaderTable .intro-scheme .secondcol {
    width: 22%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    float: left;
}

#viewAssocCommsHeaderTable .intro-scheme .thirdcol {
    width: 30%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    float: left;
    padding-left: 2%;
}

#viewAssocCommsHeaderTable .intro-scheme .forthcol {
    width: 7%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    float: left;
}

#viewFormHeaderTable .accordion-header, #viewAssocCommsHeaderTable .accordion-header {
    height: 35px;
    line-height: 2.3!important;
    font-size: 12px!important;
    color: #222;
    border: 1px dotted var(--primary-black, #000000)!important;
    margin-top: 5px!important;
    font-style: normal;
}

#viewAssocCommentHeaderTable .accordion-header {
    height: 63px;
    line-height: 1.3!important;
    font-size: 12px!important;
    color: #222;
    border: 1px dotted var(--primary-black, #000000)!important;
}

#viewAssocCommentHeaderTable .ui-state-default {
    background: #e6e6e6 url(/images/ui-bg_glass_75_e6e6e6_1x400_75h.png) 50% 50% repeat-x;
}

#viewAssocCommentHeaderTable .ui-state-active {
    background: none repeat scroll 0 0 transparent!important;
}

#viewFormHeaderTable .ui-state-active, #viewAssocCommsHeaderTable .ui-state-active {
    height: 100px;
    border: 1px dotted var(--primary-black, #000000)!important;
    border-bottom: 0!important;
    -webkit-border-radius: 20px 20px 0 0;
    -moz-border-radius: 20px 20px 0 0;
    border-radius: 20px 20px 0 0;
    padding-top: 0!important;
}

#viewAssocCommentHeaderTable .ui-state-active {
    border: 1px dotted var(--primary-black, #000000)!important;
    border-bottom: 0!important;
    -webkit-border-radius: 20px 20px 0 0;
    -moz-border-radius: 20px 20px 0 0;
    border-radius: 20px 20px 0 0;
    padding-top: 5px!important;
}

#viewFormHeaderTable .ui-accordion-content-active, #viewAssocCommsHeaderTable .ui-accordion-content-active, #viewAssocCommentHeaderTable .ui-accordion-content-active, #viewFormHeaderTable .accordion-content-active {
    -webkit-border-radius: 0 0 20px 20px;
    -moz-border-radius: 0 0 20px 20px;
    border-radius: 0 0 20px 20px;
    border: 1px dotted var(--primary-black, #000000)!important;
    border-top: 0!important;
}

#viewAssocCommentHeaderTable .ui-accordion-content-active {
    font-size: 12px;
    padding-left: 60px;
    padding-top: 0;
}

#viewAssocCommsHeaderTable .accordion-header .divth, #viewAssocCommentHeaderTable .accordion-header .divth {
    padding: 5px 0 0 12px;
    background: none;
}

#viewFormHeaderTable .accordion-header .divth {
    padding: 5px 0 0 0;
    background: none;
}

#viewAssocCommsHeaderTable .accordion-header .divth {
    float: left;
}

#viewFormHeaderTable .userImage {
    width: 4%;
    padding-left: 5px!important;
}

#viewFormHeaderTable .form_title {
    width: 32%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    float: left;
}

#viewFormHeaderTable .orignator_name {
    width: 10%;
    font-style: italic;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #4C4C4B;
}

#viewFormHeaderTable .action_name {
    width: 35%;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#viewAssocCommsHeaderTable .userImage, #viewAssocCommentHeaderTable .userImage {
    padding-left: 5px!important;
}

#viewAssocCommsHeaderTable .form_title, #viewAssocCommentHeaderTable .form_title {
    width: 32%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    float: left;
}

#viewAssocCommsHeaderTable .orignator_name, #viewAssocCommentHeaderTable .orignator_name {
    width: 10%;
    font-style: italic;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #4C4C4B;
}

#viewAssocCommsHeaderTable .comm_title, #viewAssocCommentHeaderTable .comm_title {
    font-size: 14px;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #4C4C4B;
}

#viewAssocCommsHeaderTable .comm_desc {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    float: left;
}

#viewAssocCommentHeaderTable .comm_desc {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    float: left;
    clear: left;
    width: 100%;
    height: 15px;
}

#viewAssocCommsHeaderTable .action_name, #viewAssocCommentHeaderTable .action_name {
    width: 30%;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#viewFormHeaderTable .blankDiv {
    width: 0;
}

#viewAssocCommsHeaderTable .blankDiv, #viewAssocCommentHeaderTable .blankDiv {
    width: 0;
}

#viewFormHeaderTable .actioncomplated, #viewAssocCommsHeaderTable .actioncomplated, #viewAssocCommentHeaderTable .actioncomplated {
    width: 1%;
    padding-left: 3px!important;
}

#viewFormHeaderTable .assoc, #viewAssocCommsHeaderTable .assoc, #viewAssocCommentHeaderTable .assoc {
    padding-left: 3%!important;
}

#viewAssocCommentHeaderTable .assoc img {
    vertical-align: middle;
    margin-right: 5px;
}

#viewAssocCommentHeaderTable .assoc span {
    padding-left: 5px;
}

#viewFormHeaderTable .time {
    width: 7%;
    text-align: right;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    overflow: hidden;
}

#viewAssocCommsHeaderTable .time {
    text-align: right;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    overflow: hidden;
    float: right;
}

#viewAssocCommentHeaderTable .time {
    text-align: right;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    overflow: hidden;
    float: right;
    padding-left: 0!important;
    margin-left: 0!important;
}

div#viewFormHeaderTable .col2 span.inOneLine {
    float: left;
}

div#viewFormHeaderTable .col2.divth span img {
    float: left;
    padding: 5px 10px 0 10px;
}

div#viewFormHeaderTable .col2.divth span a {
    padding: 0;
    float: left;
}

#viewAssocCommsHeaderTable .col2.divth img {
    padding: 0 10px;
}

#viewFormHeaderTable .col0 {
    width: auto;
    padding-top: 10px!important;
    padding-left: 5px!important;
}

#viewFormHeaderTable .col1 {
    padding-left: 12px!important;
    line-height: 1.5;
}

#viewFormHeaderTable .col2 {
    width: 30%;
    padding-left: 7.5%!important;
}

#viewFormHeaderTable .col3 {
    width: auto;
    text-align: right;
    padding-top: 5px!important;
    padding-left: 0!important;
    float: right;
    -webkit-box-sizing: border-box;
    -khtml-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
}

#viewAssocCommsHeaderTable .col0 {
    width: 4%;
    padding-top: 10px!important;
    padding-left: 5px!important;
}

#viewAssocCommsHeaderTable .col1 {
    width: 30%;
    padding-left: 12px!important;
}

#viewAssocCommsHeaderTable .col2 {
    width: 30%;
    padding-left: 5px!important;
}

#viewAssocCommsHeaderTable .col3 {
    width: 32%;
    text-align: right;
    padding-top: 5px!important;
}

#viewFormHeaderTable .divth:hover, #viewAssocCommsHeaderTable .divth:hover {
    background: none!important;
}

#formMsgHeaderLoading, #formMsgContentLoading {
    display: block;
    text-align: center;
}

#viewFormHeaderTable .col3.divth span, #viewAssocCommsHeaderTable .col3.divth span {
    padding-left: 10px;
}

#viewFormHeaderTable #formMsgSetting, #viewAssocCommsHeaderTable #formMsgSetting {
    padding-left: 15px;
}

#viewFormHeaderTable #moreActionOption img, #viewAssocCommsHeaderTable #moreActionOption img {
    vertical-align: text-top;
}

#formMsgHeaderLoading>img {
    vertical-align: bottom;
}

#moreActionOption .dropdown-menu.pull-right span, #formMsgSetting .dropdown-menu.pull-right span {
    padding-left: 19px;
    color: var(--primary-black, #000000);
}

.nopaddingLeft {
    padding-left: 5px!important;
}

#moreActionOption .dropdown-menu a:hover, #formMsgSetting .dropdown-menu a:hover {
    background: #f1f1f1!important;
}

#moreActionOption .dropdown-menu.pull-right {
    text-align: left;
}

#moreActionOption .dropdown-menu>li>a {
    padding: 3px 10px!important;
}

#moreActionOption .user-toggle img {
    margin: 0!important;
}

#moreActionOption .btn-group.open .dropdown-toggle, #formMsgSetting .btn-group.open .dropdown-toggle {
    box-shadow: none!important;
}

#editProfileModal {
    width: 85%;
}

#editProfileModal #edit-profile-container {
    overflow: auto;
    height: 595px;
}

#editProfileModal .sidebar-nav {
    display: none;
}

#editProfileModal .content.fixed-fixed {
    margin: 0;
}

#editProfileModal .modal-body {
    padding: 0;
}

#editProfileModal .modal-body .fullwidth-box-top {
    display: none;
}

#editProfileModal .modal-body .nopadding-box-lower {
    border: none;
}
#editProfileModal #formMyAccount .myaccount-left input[type="checkbox"]:focus-visible{
    outline: auto;
}

#password-strength-container {
    background: url("../images/icons/gray_arrow_separator.gif") no-repeat scroll left top transparent;
    float: left;
    margin-top: 150px;
    padding-left: 20px;
}

#formMyAccount #password-strength-container {
    position: absolute;
    left: 100%;
    margin: 0;
    top: 7px;
    width: 276px;
    float: none;
    border-left: 55px solid transparent;
}

#password-strength {
    background-color: #EEE;
    border: 1px solid #666;
    font-size: 11px;
    height: 15px;
    overflow: hidden;
    padding: 0;
    width: 120px;
    position: relative;
}

#password-strength .password-strength-text {
    height: 12px;
    margin: -1px 0 0 2px;
    position: absolute;
    z-index: 9;
}

#password-strength .password-strength-bar {
    height: 15px;
}

.tipText {
    color: var(--neutral-gray, #616161);
    float: left;
}

.clear-action-for-infoIcon {
    height: 25px;
    vertical-align: middle;
    margin-top: -12px;
    cursor: pointer;
}

.clickable {
    cursor: pointer;
    padding: 2px 3px;
}

.selection span {
    padding: 0 5px;
    font-weight: bold;
}

.selection input {
    margin: -3px 0 0;
}

#myModal-new .modal-footer {
    padding-left: 90px;
}

#opennewitem {
    width: 86px;
}

#commentButtons {
    display: block;
    float: right;
}

#FormMoreOption {
    display: block;
    padding-right: 20px;
    display: table-cell;
    text-align: right;
}

.divCommentFields, .appsDistributionActionFields, .divAppsAckActionFields, .divCommentAttachFields, #appsDistributionContainer .appsDistributionActionSection {
    display: block;
    float: none;
    width: auto;
}

.divAppsForActionFields {
    display: block;
    float: left;
    width: 100%;
}

#appsDistributionAction .modal-body {
    padding-bottom: 15px;
}

#appsDistributionContainer>.nopadding-box-lower {
    border: 1px solid #d1d1d1;
    border-radius: 4px;
}

#appsDistributionContainer .tblSelectedFiles {
    min-height: 100px;
    float: none;
    width: auto;
    padding: 0 13px 13px;
}

#appsDistributionContainer .appsDistributionActionSection {
    margin: 0;
    padding: 20px 16px 0;
}

#appsDistributionContainer .appsDistributionActionSection .select2-container {
    width: auto;
    display: block;
}

#appsDistributionContainer .appsDistributionActionFields>label {
    width: 60px;
    margin-top: 6px;
    margin-left: 0;
}

#appsDistributionContainer #divAppsDistributionSub>input {
    width: 90.8%;
}

#appsDistributionContainer .tblSelectedFiles table {
    min-width: 100%;
}

#appsDistributionContainer .tblSelectedFiles th {
    cursor: pointer;
    font-weight: bold;
    font-size: 13px;
}

#appsDistributionContainer .tblSelectedFiles th, #appsDistributionContainer .tblSelectedFiles td {
    padding: 7px 10px!important;
}

#appsDistributionContainer form+h4 {
    padding: 0 16px;
    font-size: 15px;
    clear: both;
}

#filterFormContainer table th a {
    background: none;
}

#filterFormContainer .table th {
    padding-left: 15px;
}

#formselectbutton {
    clear: both;
    display: block;
    margin: 5px;
    padding: 0;
}

#ChangeStatusModal.modal {
    width: 70%;
    font-size: 13px;
    height: auto;
}

#batchChangeStatusModal.modal {
    width: 850px;
    font-size: 13px;
    height: auto;
}

#batchChangeStatusModal.modal td.doc-ref-container {
    position: relative;
}

#batchChangeStatusModal.modal td.doc-ref-container i {
    display: block !important;
    color: #FFA500;
    height: 14px;
    width: 16px;
    position: absolute;
    top: 0px;
    left: -24px;
    padding: 4px;
}

#changeStatusForm {
    width: 100%;
}

#batchchangeStatusForm span {
    float: left;
    width: 40%;
}

#batchchangeStatusForm span input {
    margin-top: 0;
}

#batchChangeStatusModal label {
    float: left;
    width: 58%;
}

#batchChangeStatusModal fieldset {
    border: 1px solid #ccc!important;
}

#batchChangeStatusModal .batchStatusSettings, #batchChangeStatusModal .batchStatusSettings label {
    width: 77%;
}

#batchChangeStatusModal .batchStatusSettings span {
    width: 20%;
}

#batchChangeStatusModal .margin10 {
    margin: 10px;
}

#changeStatusForm select, #batchChangeStatusModal select {
    width: 130px;
    width: auto;
}

#statusdropdownspan {
    width: auto!important;
}

#batchstatuschangedrodown {
    width: auto!important;
}

#statusChangeDrodown {
    width: auto!important;
}

#statusChangeDrodown:focus-visible {
    outline: auto;
}

#changeStatusForm input[type="checkbox"]:focus-visible{
    outline: auto;
}

#createForm {
    margin: 0;
}

#viewAssocCommsMainSection {
    width: 100%;
}

#viewModels {
    float: left;
}

#addModel {
    float: left;
    width: 100%;
}

#modelCoordinate {
    float: left;
    margin-left: 123px;
}

.resizeForModel {
    width: 340px;
    height: 200px;
    padding: 5px;
    margin-right: 0;
    border: 1px solid #D1D1D1;
}

#mapFolderPath {
    display: block;
    white-space: nowrap;
    width: 65%;
}

.folderPath {
    float: left;
    font-size: 16px;
}

.worksetEditMode {
    clear: both;
    float: left;
    margin-top: 10px;
}

.firstInput {
    margin-bottom: 10px;
}

.secondInput .controls {
    padding: 10px 0;
}

.thirdInput {
    clear: both;
    float: left;
}

.thirdInput .controls {
    padding: 10px 0;
}

.btn.btn-inverse.properties>img {
    float: left;
    height: 20px;
    vertical-align: middle;
    width: 20px;
}

#myModal-download .add-folder-button span, #myModal-user-preference .add-folder-button span {
    float: left;
    font-size: 14px;
    width: 100%;
}

#myModal-download .documentsContainer {
    width: 100%;
}

#myModal-download .commentsContainer {
    clear: both;
    float: left;
    width: 80%;
}

#myModal-download.new-download-file-css .commentsContainer {
    width: 100%;
}

#myModal-download .commentsContainer .show-hide-unsupported-metadata-files {
    text-decoration: underline;
    margin-left: 5px;
}

#myModal-download .commentsContainer .unsupported-metadata-files-table {
    margin-top: 12px;
}

#myModal-download .commentsContainer .unsupported-metadata-files-table th {
    color: #263238;
    font-weight: 400;
    text-align: left;
    border-bottom: 1px solid #C5C5C5;
    padding-top: 8px;
    padding-bottom: 8px;
}

#myModal-download .commentsContainer .unsupported-metadata-files-table .tblbody td {
    border-bottom: none;
    color: #263238;
    font-weight: 400;
    white-space: nowrap;
    padding-right: 8px;
    padding-top: 8px;
}

#myModal-download .comments {
    padding-left: 100px;
}

#myModal-download .documents {
    border: medium none;
}

#myModal-download .element, #myModal-user-preference .element {
    display: inline-block;
    width: 100%;
    float: left;
}

#myModal-download .element>input, #myModal-user-preference .element>input {
    float: left;
    margin-left: 100px;
}

#myModal-user-preference .element>input {
    margin-left: 0;
}

#myModal-download .element>label, #myModal-user-preference .element>label {
    float: left;
    margin-left: 20px;
}

#myModal-download .helpbrand {
    cursor: pointer;
    height: 24px;
    margin-left: 5px;
    margin-top: 0;
    padding: 0;
    width: 24px;
}

#myModal-download .helpbrand>img {
    height: 24px;
    width: 24px;
}

#myModal-download .form-dotted-line {
    margin: 10px 0;
}

#myModal-download .adoddleNote, #myModal-download .note-message{
    margin-left: 100px;
}

#addModel .form-dotted-line {
    margin: 10px 0;
}

#commentfooterButtons {
    clear: both;
    display: block;
    padding-top: 15px;
    margin-left: 10px;
}

#fileCommsSection, #formMsgThreadSection, #formAllHistorySection, #workflowStatusSection, #formAllAssociationSection, #allAssociationSection {
    float: left;
    padding-right: 0;
    margin-right: 8px;
    margin-left: 0;
    background-color: var(--primary-white, #FFFFFF);
}

#fileRevSelect .assoc-rev-deactivate {
    color: var(--primary-color, #3569AE);
}

#fileRevSelect .assoc-rev-deactivate:hover {
    color: var(--primary-color, #3569AE);
}

#myModal-upload #modal-body {
    content: "";
    display: table;
}

#myModal-upload .footer {
    clear: both;
    float: left;
    padding: 15px;
    width: 647px;
}

#ChangeStatusModal .modal-body {
    padding-bottom: 20px!important;
}

#ChangeStatusModal span {
    display: inline;
    font-size: 13px;
}

#ChangeStatusModal span input {
    margin: 0!important;
}

#btnchangestatus, #btnbatchchangestatus {
    margin-left: 0;
}

#batchChangeStatusModal .loading-msg, #ChangeStatusModal .loading-msg {
    margin: -8px 0px 7px;
    font-size: 15px;
}

#statusFrom {
    width: 50px;
    padding: 0 10px 0 5px;
    color: #F00;
    font-weight: bold;
}

#newstatus_reason {
    height: 30px;
    width: 64%;
}

.statusChangeLabel {
    padding-right: 20px;
}

#ChangeStatusModal label {
    display: inline;
    padding-right: 5px;
    font-size: 13px;
}

#markAllActionclear {
    padding-left: 5px;
}

#mark_allAction_completed {
    margin: 0;
}

.status-label {
    float: left;
    width: 58%;
}

.status-span {
    float: left;
    width: 25%;
}

#statusdropdownspan {
    vertical-align: middle;
    width: auto!important;
}

#changeStatusForm>div {
    line-height: 20px;
    margin-bottom: 7px;
}

#searchcontent_model .cover {
    clear: both;
    float: left;
    height: auto;
    margin-top: 110px;
}

#searchcontent_model .boxmodel-inner {
    padding: 0;
}

.calendar .title {
    float: none;
}

#showFormSection {
    display: block;
    float: left;
}

#formTitleDiv {
    display: block;
}

.divformAttachFields>label {
    color: #CF0600;
    float: left;
    margin-right: 10px;
}

#printView {
    margin-top: 0;
    padding-top: 0;
}

.btnsaveCancelForm button, .btnsaveCancelInvoiceForm button {
    margin-left: 10px;
}

.btnsaveCancelForm button {
    background-color: #363636;
    color: var(--primary-white, #FFFFFF);
    border: none;
    padding: 6px 17px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    font-size: 14px;
    float: left;
    cursor: pointer;
}

.divformAttachFields {
    display: none;
    float: left;
    width: 99%;
    padding-left: 1%;
    height: 32px;
}

.btnsaveCancelForm {
    display: block;
    padding-top: 10px;
    float: left;
    clear: both;
}

#displayPrintViewAfterSaving {
    margin-bottom: 5px;
    padding-left: 21px;
}

.formHR {
    width: 98%;
    height: 1px;
    border: dashed 1px;
    border-top: 1px dotted #dcdcdc;
    color: var(--primary-white, #FFFFFF);
    background-color: var(--primary-white, #FFFFFF);
}

#myModal-modeledit .form-dotted-line {
    margin: 20px 0;
}

#batchstatuslisting, #commStatuslisting {
    width: 98%;
    height: 200px;
    overflow-y: auto;
    margin-bottom: 20px;
}

#batchstatuslisting th, #commStatuslisting th {
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    padding-right: 20px;
    text-align: left;
}

#batchchangeStatusForm {
    margin-bottom: 45px;
}

#batchstatuslisting>table, #commStatuslisting>table {
    border: 1px solid #ddd;
    width: 97%;
}

#fileBatchNameList td {
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    vertical-align: top;
}

#batch_newstatus_reason {
    width: 75%!important;
}

#reaseon_note_label {
    width: 100%!important;
}

.divformAttachFields a {
    color: #CF0600;
}

.removeAttachment {
    padding-bottom: 0;
    padding-left: 10px;
}

#divViewFolderPermission label {
    cursor: default;
}

#divViewFolderPermission .control-label {
    width: auto;
    font-size: 12px;
}

#divViewFolderPermission .nowrap {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
}

.overFlowHide {
    height: auto;
    max-height: 200px;
    min-height: 50px;
    overflow: auto;
}

#divViewFolderPermission .img-rounded {
    float: left;
    margin-right: 10px;
}

#createdByValue>strong {
    float: left;
    margin-left: 10px;
    margin-top: 10px;
}

#folderAccessList span {
    float: left;
    margin-left: 10px;
}

#rrbDiv {
    margin-left: 21px;
}

#rrbDiv label {
    float: left;
    margin-right: 10px;
}

#rrbDiv #respondBy {
    padding: 3px;
    width: 100px;
    margin-right: 5px;
}

#respondBy {
    font-size: 12px;
}

#viewModels #project-blocks .addRemoveFav {
    display: none;
}

.viewModelFav {
    background: url("/images/icons/fav_star_icon.png") repeat scroll 0 0 transparent;
    cursor: pointer;
    height: 24px;
    overflow: hidden;
    position: absolute;
    width: 23px;
    z-index: 1000;
    right: 5px;
    top: 5px;
}

.viewModelFav.fav {
    background: url("/images/icons/fav_star_icon.png") repeat scroll 0 24px transparent;
}

#searchcontent_model .addRemoveFav {
    display: none;
}

a.addRemoveFav {
    background: url("/images/icons/fav_star_icon.png") repeat scroll 0 0 transparent;
    cursor: pointer;
    float: left;
    height: 24px;
    overflow: hidden;
    width: 23px;
}

a.addRemoveFav:focus {
	outline: auto;
}

#myDash3 div.favModel .fav {
    background: url("/images/icons/fav_star_icon.png") repeat scroll 0 24px transparent;
}

#customRightClick a {
    background: url("/images/icons/fav_star_icon.png") repeat scroll 0 -32px transparent;
    cursor: pointer;
    height: 32px;
    margin-left: 5px;
    position: absolute;
    width: 32px;
    z-index: 1000;
}

#customRightClick a.fav {
    background: url("/images/icons/fav_star_icon.png") repeat scroll 0 0 transparent;
}

#customRightClick span {
    margin-left: 40px;
}

#myDash4 .favComm>a {
    background: url("/images/icons/fav_star_icon.png") repeat scroll 0 0 transparent;
    cursor: pointer;
    height: 24px;
    overflow: hidden;
    float: left;
    width: 23px;
    z-index: 1000;
}

#myDash4 .favCommsRows {
    border-bottom: 1px solid #D1D1D1;
    padding: 8px 0;
    height: 53px;
    cursor: pointer;
}

#myDash4 .favComm {
    padding: 0 3px;
    margin-top: 5px;
}

.total-widget-action-count {
    border-radius: 50%;
    color: #222;
    font-size: 12px;
    font-weight: bold;
    float: right;
    text-align: center;
    width: 22px;
    cursor: pointer;
    padding: 3px 3px;
    line-height: 20px;
}

.total-widget-action-count.highlight-latest-rev {
    position: relative;
    display: inline-block;
    top: 0;
    left: 0;
    width: 17px;
    height: 16px;
    line-height: 16px;
}

#myDash4 .favDocType {
    margin-left: 2px;
    margin-top: 4px;
    position: relative;
}

#myDash4 .favDocType span.form-32 {
    height: 32px;
    width: 32px;
    background: url("/images/icons/form_32.png");
    display: inline-block;
}

.favName a, .favName span {
    color: var(--primary-black, #000000);
    display: block;
    float: left;
    font-size: 12px;
    margin-left: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
}

.favName a:hover {
    color: var(--primary-color, #3569AE);
}

.favName span {
    color: var(--primary-black, #000000);
    float: left;
    font-size: 12px;
    margin-left: 10px;
}

#myDash4 .favComm>a.fav {
    background: url("/images/icons/fav_star_icon.png") repeat scroll 0 24px transparent;
}

#myDash4 .favDocType img {
    width: 32px;
    height: 32px;
}

#myDash4 .favCommAction>a {
    float: left;
    margin: 4px 1px 0;
    position: relative;
    text-align: center;
    vertical-align: middle;
    width: 22px;
}

#myDash4 .favCommAction a span {
    border-radius: 100%;
    color: var(--primary-white, #FFFFFF);
    float: left;
    font-size: 12px;
    font-weight: bold;
    height: 22px;
    padding-top: 0;
    width: 100%;
}

.favCommName.favName {
    width: 72%;
}

.red-bg-color {
    background-color: #c00;
}

.green-bg-color {
    background-color: #84B326;
}

.amber-bg-color {
    background-color: #E85D00;
}

.red-border-color {
    border: 1px solid #c00;
}

.green-border-color {
    border: 1px solid #84B326;
}

.amber-border-color {
    border: 1px solid #E85D00;
}

.favCommAction img {
    height: 22px;
    width: 22px;
}

#customRightClick li {
    line-height: 30px;
    padding-left: 0;
    padding-right: 10px;
}

@media(max-width: 1600px) {
    .favCommName.favName {
        width: 68%;
    }
}

@media(max-width: 1400px) {
    .favCommName.favName {
        width: 64%;
    }
}

@media(max-width: 1200px) {
    .favCommName.favName {
        width: 54%;
    }
}

#myDash6 .favFolderRows {
    border-bottom: 1px solid #D1D1D1;
    height: 53px;
    padding: 8px 0;
    position: relative;
    cursor: pointer;
}

.favFolderRows .folder-icon {
    background: url("/images/icons/folder-closed32x32.png");
    height: 48px;
    width: 48px;
    float: left;
    background-size: contain;
    margin-top: -5px;
}

#myDash6 .favFolder {
    padding: 0 3px;
    margin-top: 5px;
}

#myDash6 .favDocType {
    margin-left: 5px;
}

#myDash6 .favFolder>a.fav {
    background: url("/images/icons/fav_star_icon.png") repeat scroll 0 24px transparent;
}

#myDash6 .favDocType img {
    width: 32px;
    height: 32px;
}

#myDash6 .favFolderAction>a {
    margin: 0 2px;
    position: relative;
    text-align: center;
    width: 100%;
}

#myDash6 .favFolderAction a span {
    color: white;
    font-size: 14px;
    font-weight: bold;
    left: 0;
    padding-top: 4px;
    position: absolute;
    width: 32px;
    top: -16px;
}

.favFolderAction img {
    height: 32px;
    width: 32px;
}

.favModelRow {
    border-bottom: 1px solid #D1D1D1;
    height: 53px;
    padding: 8px 0;
    cursor: pointer;
}

.favModel {
    float: left;
    height: 24px;
    margin-left: 0;
    margin-top: 10px;
    overflow: hidden;
    padding: 0 3px;
    width: 23px;
    padding: 0 5px;
}

.favModelImage {
    float: left;
    height: 45px;
    margin-left: 10px;
    position: relative;
    width: 70px;
    margin-top: 0px;
    margin-right: 10px;
    margin-left: 6px;
    text-align: center; 
}

.favModelImage img {
    height: 45px;
}

.favModelName {
    float: left;
    font-size: 14px;
    margin-left: 10px;
}

.showMoreContainer {
    background-color: white;
    border-bottom: 1px solid var(--primary-white, #FFFFFF);
    border-radius: 5px;
    bottom: 0;
    padding-top: 5px;
    position: absolute;
    text-align: center;
}

#commstatuslisting th {
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    padding-right: 20px;
    text-align: left;
}

#CommChangeStatusModal.modal {
    width: 60%;
    font-size: 13px;
    height: auto;
}

#CommChangeStatusModal label {
    display: inline;
    padding-right: 5px;
    font-size: 13px;
}

#CommChangeStatusModal span {
    display: inline;
    font-size: 13px;
    font-weight: bold;
}

#CommChangeStatusModal span input {
    margin: 0!important;
}

#btncommchangestatus {
    margin-left: 30%;
}

#commChangeStatusForm {
    width: 100%;
}

#commChangeStatusForm>div {
    line-height: 20px;
    margin-bottom: 7px;
}

#comm_newstatus_reason {
    height: 30px;
    width: 60%;
}

#commStatusFrom {
    font-weight: bold;
    text-align: left;
    width: 50px;
}

#commChangeStatusForm select {
    width: 130px;
}

#commStatusChangeDropdown {
    vertical-align: middle;
    width: auto!important;
}

#reason_comm_label_comm {
    width: 100%!important;
}

.notify_status_comm {
    float: left;
    width: 58%;
}

#commstatuschangeName {
    vertical-align: middle;
}

#FormChangeStatusModal.modal {
    width: 70%;
    font-size: 13px;
    height: auto;
}

#FormChangeStatusModal label {
    display: inline;
    padding-right: 5px;
    font-size: 13px;
}

#FormChangeStatusModal span {
    display: inline;
    font-size: 13px;
}

#FormChangeStatusModal span input {
    margin: 0!important;
}

#btnFormchangestatus {
    margin-left: 30%;
}

#formChangeStatusForm {
    width: 100%;
}

#formChangeStatusForm>div {
    line-height: 20px;
    margin-bottom: 7px;
}

#form_newstatus_reason {
    height: 30px;
    width: 60%;
}

#formStatusFrom {
    font-weight: bold;
    text-align: left;
    width: 50px;
}

#formChangeStatusForm select {
    width: 130px;
}

#formStatusChangeDropdown {
    vertical-align: middle;
    width: auto!important;
}

#reason_form_label_form {
    width: 100%!important;
}

.notify_status_form {
    float: left;
    width: 58%;
}

#formstatuschangeName {
    vertical-align: middle;
}

#formNameList td {
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    vertical-align: top;
}

#viewFormMainSection {
    width: 100%;
    height: inherit;
}

.redAsterix {
    color: #B30000;
}

.addModelForm .help-block {
    margin-bottom: 10px;
}

.percentage>span {
    float: left;
    height: 30px;
    margin-top: -10px;
    text-align: center;
    width: 100%;
    font-size: 12px;
}

.discipline-Container {
    float: left;
    margin-left: 0;
    margin-top: 13px;
}

.discipline-Container .span12 {
    line-height: 0;
    margin-left: 0;
    text-align: center;
}

.discipline-Container .span12 h3 {
    color: #666;
    line-height: 0;
}

#chartdiscipline {
    border: none;
    width: 100%;
    float: left;
    margin-top: -5px;
}

#myDash5 span.labelText {
    clear: both;
    color: #666;
    float: left;
    font-size: 14px;
    margin-left: 10px;
    width: 98%;
}

.recent-container {
    background-color: #D1D1D1;
    border-left: 1px solid #D1D1D1;
    border-top: 2px solid var(--primary-white, #FFFFFF);
    border-top-left-radius: 15px;
    bottom: 0;
    float: right;
    height: 302px;
    padding-left: 10px;
    padding-top: 10px;
    right: 0;
    width: 225px;
}

.recent-wrapper {
    border-left: 2px solid var(--primary-white, #FFFFFF);
    border-top: 2px solid var(--primary-white, #FFFFFF);
    border-top-left-radius: 15px;
    padding-top: 5px;
    height: 100%;
    position: relative;
}

.dash-recent-more {
    bottom: 0;
    position: absolute;
    background: var(--primary-white, #FFFFFF);
}

.dash-recent-models-title {
    padding-bottom: 5px;
    padding-left: 10px;
}

.dash-recent-models-title>strong {
    color: #666;
    font-weight: normal;
    font-size: 14px;
}

.dash-recentAccessed-row {
    background-color: var(--primary-white, #FFFFFF);
    border: 1px solid var(--primary-white, #FFFFFF);
    border-radius: 5px;
    margin: 0 5px 5px;
}

.fixed-fixed .row-fluid.dash-recentAccessed-row {
    height: auto;
}

.dash-image {
    line-height: 0;
    margin-left: 5px;
    padding-bottom: 2px;
    padding-top: 2px;
}

.dash-image img {
    height: 50px;
    width: 100%;
}

.dash-modelName {
    float: left;
    margin-left: 10px;
}

.dash-modelName>a {
    float: left;
    padding-left: 5px;
    width: 97%;
}

.dash-modelName {
    margin-left: 10px;
    margin-top: 5px;
    width: 130px;
}

.dash-modelName .labelName {
    color: #666;
    display: block;
    float: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 88%;
    font-size: 14px;
}

.dash-modelName .labelMore {
    color: #666;
    float: left;
    width: 9%;
}

.dash-recent-more>a {
    color: #666;
    float: left;
    margin-bottom: 5px;
    text-align: center;
    width: 100%;
}

#myDash5 .thisWeek {
    color: #666;
    font-size: 16px;
}

#myDash5 .chartContainer {
    float: left;
    margin-left: 5px;
    margin-top: 5px;
}

#myDash5>canvas {
    bottom: 0;
    left: -189px;
    position: absolute;
    top: 148px;
}

.posAbsolute {
    z-index: 0;
}

.outerCircle {
    border: 1px solid #D3D3D3;
    border-radius: 100% 100% 100% 100%;
    bottom: 5px;
    height: 75px;
    left: 4px;
    position: absolute;
    width: 75px;
    cursor: pointer;
}

.innerCircle {
    border: 1px solid lightgray;
    border-radius: 100% 100% 100% 100%;
    height: 59px;
    margin-left: 7px;
    margin-top: 7px;
    position: absolute;
    width: 60px;
}

#myDash5 .newFiles {
    cursor: pointer;
    margin-left: 5px;
}

.showMoreContainer a {
    background: none repeat scroll 0 0 transparent!important;
    border: medium none!important;
    padding: 0!important;
}

.showMoreContainer span.prev-img {
    height: 24px;
    width: 24px;
    background: url("/images/icons/previous.png");
    display: inline-block;
}

.showMoreContainer span.next-img {
    height: 24px;
    width: 24px;
    background: url("/images/icons/next.png");
    display: inline-block;
}

.showMoreContainer a.disabled {
    opacity: .45;
}

.showMoreContainer .showMore {
    bottom: 8px;
    font-size: 14px;
    left: 44%;
    position: absolute;
}

.showMoreContainer .showMore a {
    background: none!important;
    border: none!important;
}

.margin-right {
    margin-right: 5px!important;
}

.margin-left {
    margin-left: 5px!important;
}

.assocCommList {
    height: 80px;
    width: 100%;
    background: url("/images/icons/tr_repeat.png") repeat-x scroll center bottom transparent;
}

.assocCommList:hover {
    background: #E5E5E5;
}

.assocListlhs {
    height: 45px;
    width: 69%;
    float: left;
}

.assocListrhs {
    height: 45px;
    width: 30%;
    float: left;
}

.assocListImgHolder {
    height: 40px;
    width: 40px;
    padding: 10px 5px 0 0;
    float: right;
}

.assocListLines {
    height: 22px;
    width: 100%;
    float: left;
}

.assocImgType {
    height: 25px;
    width: 30px;
    float: left;
    line-height: 25px;
    padding-left: 5px;
}

.assocImgType>img {
    vertical-align: text-top;
}

.assocCode {
    height: 25px;
    width: 80%;
    float: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.assocCode a {
    color: var(--primary-black, #000000);
    text-decoration: none;
    float: left;
    height: 25px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 130px;
    line-height: 25px;
}

.assocTitle {
    height: 20px;
    width: 95%;
    float: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 20px;
    margin-left: 35px;
    font-size: 13px;
}

.assocmsgContent {
    width: 220px;
    float: left;
    line-height: 15px;
    font-size: 12px;
    color: #9F9F9F;
}

#assocCommsList>a {
    color: var(--primary-black, #000000);
}

.assocListLines.multilineTextContainer {
    width: 85%;
    padding-left: 35px;
    max-height: 30px;
}

.badge.AsiteBadge {
    border-radius: 0;
    font-size: 12px;
    line-height: 12px;
    height: 12px;
    padding: 1px 4px;
}

.assocBadge {
    float: left;
    width: 100%;
    line-height: 20px;
    text-align: right;
}

.assocTime {
    font-size: 12px;
    float: left;
    height: 25px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    text-align: right;
}

.multilineTextContainer {
    max-height: 40px;
    min-height: 30px;
    display: block;
    line-height: 1.3;
}

.multilineEllipseText {
    max-height: 40px;
}

.requireJava {
    position: absolute;
    margin-top: -9px;
    padding-bottom: 24px;
    width: 200px;
    right: 30px;
    height: 40px;
}

.phpdcannotdownload {
    float: left;
    left: 540px;
    margin-top: -20px;
    padding-bottom: 24px;
    position: absolute;
    right: 55px;
    width: 300px;
}

.associatedDocuments {
    float: left;
    left: 405px;
    margin-top: -40px;
    padding-bottom: 24px;
    position: absolute;
    right: 55px;
    width: 330px;
}

.associatedDocuments .popover-content {
    word-break: break-all;
}

.attachedDocuments {
    float: left;
    left: 395px;
    margin-top: -20px;
    padding-bottom: 24px;
    position: absolute;
    right: 55px;
    width: 300px;
}

.attachedDocuments .popover-content {
    word-break: break-all;
}

.reportingLabel-dashboard {
    color: #686868;
    display: block;
    float: left;
    font-size: 2.6em;
    height: 40px;
    margin: 10px 0;
    width: 100%;
}

.report-help {
    color: #ADADAD;
    float: left;
    font-size: 18px;
    margin-top: 15px;
    width: 100%;
}

.report-help:hover {
    color: var(--primary-color, #3569AE);
}

.report-start-button {
    font-size: 38px;
    height: 40px;
    margin-left: 10px;
    margin-top: -5px;
    border-radius: 5px 5px 5px 5px;
}

.col2 .actionName {
    display: block;
    line-height: 1.3;
    padding-left: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

a#sidenav-help-featured {
    background: url(../images/icons/sidenav_files_manage.png) no-repeat;
    color: var(--primary-black, #000000);
}

.openFormStatus.disabled {
    color: #666;
}

#elearningContent {
    overflow: hidden;
    height: 450px;
}

#frame1 {
    border: 0 none;
    min-height: 497px;
    overflow: auto;
}

#framehelpfile {
    border: 0;
    height: inherit;
    overflow: auto;
}

#framevideo {
    border: 0;
    height: 454px;
    overflow: auto;
}

#span_onlinehelp {
    left: 9px;
    top: 53px;
    text-align: center;
    position: absolute;
}

#frmHelpFile {
    margin: 0;
}

#createFormIframe {
    width: 100%;
    border: 0;
}

#creatFormModal #createFormIframe, .modal #createFormIframe {
    height: 100%!important;
    z-index: 9999999 !important;
    position: relative;
}

#appsListSection .filelistchkbox {
    display: none;
}

.IframeSection #createFormIframe {
    overflow: hidden;
    min-height: 625px;
}

.sizeZero {
    width: 0!important;
    height: 0!important;
    position: absolute!important;
    opacity: .0!important;
    left: 0!important;
    z-index: -10!important;
}

.assocOpenComms {
    background: #E5E5E5;
}

.replyOption #moreActionOption .user-toggle img {
    vertical-align: top;
    padding-left: 10px;
}

.listing-records-details {
    color: var(--neutral-gray, #616161);
    display: inline-block;
    font-size: 12px;
    font-weight: 300;
    font-style: italic;
    margin: 0 0 0 10px;
    line-height: 26px;
}

#pws_container .listing-records-details, .modal .listing-records-details {
    color: var(--primary-white, #FFFFFF);
    line-height: 34px;
}

#customValidFolderMap {
    clear: both;
    float: left;
    width: 343px;
    opacity: 0;
    z-index: -1;
}

#customValidWorkset {
    width: 0!important;
    opacity: 0!important;
    z-index: -1!important;
}

#appsSelection .nopadding-box-lower {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
}

#appsSelection .margin-in, #createForm .margin-in, #printForm .margin-in {
    box-shadow: none;
}

#appsSelection, #createForm, #printForm {
    background: var(--primary-white, #FFFFFF);
}

#helpDash {
    height: inherit;
    float: left;
    overflow: auto;
    width: 100%;
}

#elearningContent .dashboard-adoddle {
    height: inherit;
    float: left;
    width: 100%;
}

#filterFormContainerTextbox input {
    padding: 3px!important;
}

#block-videos {
    float: left;
    margin-left: 470px;
    margin-top: 30px;
    position: absolute;
}

#recent-videos {
    border: 1px solid;
    height: 150px;
    margin-top: 5px;
    border-radius: 6px 6px 6px 6px;
    overflow: hidden;
    position: relative;
}

#block-elearning {
    float: left;
    margin-left: 21px;
    margin-top: 233px;
    position: absolute;
}

#elearning-content {
    border: 1px solid;
    height: 183px;
    margin-left: 5px;
    width: 476px;
    margin-top: 5px;
    border-radius: 6px 6px 6px 6px;
    position: relative;
}

#labelelearning {
    margin-left: 5px;
}

#block-onlinehelp {
    float: right;
    margin-left: 594px;
    margin-top: 233px;
    position: absolute;
}

#onlinehelp-content {
    border: 1px solid;
    height: 183px;
    width: 476px;
    margin-top: 5px;
    border-radius: 6px 6px 6px 6px;
}

#main-block {
    border-left: 2px solid var(--primary-white, #FFFFFF);
    border-top: 2px solid var(--primary-white, #FFFFFF);
    border-top-left-radius: 15px;
    height: 100%;
    padding-top: 5px;
    position: relative;
    width: 60px;
}

.status-disabled {
    cursor: default;
    pointer-events: none;
}

.status-disabled span {
    color: gray;
}

.elearning-icon1, .elearning-icon4 {
    padding: 0 66px 0 59px;
}

.elearning-icon2, .elearning-icon5 {
    padding: 0 0 0 60px;
}

.elearning-icon3, .elearning-icon6 {
    padding: 0 0 0 104px;
}

#block-dashvideos {
    float: left;
    margin-left: 426px;
    margin-top: 30px;
    position: absolute;
    width: 645px;
}

#framedashvideo {
    border: 0 none;
    height: 150px;
    margin-left: 2px;
    margin-top: -35px;
    overflow: hidden;
}

.elearning-title1, .elearning-title4 {
    color: var(--primary-black, #000000);
    padding-left: 50px;
    padding-right: 34px;
}

.elearning-title3 {
    color: var(--primary-black, #000000);
    padding-left: 84px;
}

.elearning-title2, .elearning-title5 {
    color: var(--primary-black, #000000);
    padding-left: 55px;
}

.elearning-title6 {
    color: var(--primary-black, #000000);
    padding-left: 68px;
}

#elearning-seemore, #onlinehelp-seemore {
    position: absolute;
    right: 5px;
    bottom: 0;
}

#video-seemore {
    position: absolute;
    right: 5px;
    bottom: 0;
}

#additionalmenu .dropdown-toggle {
    background: none repeat scroll 0 0 rgba(0, 0, 0, 0);
    border: medium none;
    box-shadow: none;
    color: var(--primary-black, #000000);
    text-decoration: none;
}

div#appsAckActionDiv {
    float: left;
    margin-left: 0;
    border-top: 1px solid #D1D1D1;
    margin-bottom: 5px;
    border-radius: 4px;
    min-height: 480px;
}

#createCommentFormDiv {
    min-height: 0;
    margin-left: 0;
    float: none;
    padding: 0;
    margin-bottom: 10px;
    width: 100%;
}

#createCommentFormContainer #createCommentFormDiv {
    overflow: auto;
}

#createCommentFormDiv #oriCommentInfo {
    border-radius: 4px 4px 0 0;
}

#createCommentFormDiv #oriCommentInfo #oriComment {
    padding-left: 5px;
}

#tableForBatchComment {
    display: none;
    border: 1px solid #d1d1d1;
    margin: 0 5px 5px 5px;
    border-radius: 4px;
}

#fileContentForBatchComment {
    height: auto;
    min-height: 200px;
}

div#createCommentFormDiv input, div#createCommentFormDiv button, div#createCommentFormDiv select, div#createCommentFormDivtextarea {
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
}

.ja_JP div#createCommentFormDiv input, .ja_JP div#createCommentFormDiv button, .ja_JP div#createCommentFormDiv select, .ja_JP div#createCommentFormDivtextarea {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

.normalbtn-navigator {
    background: none repeat scroll 0 0 var(--primary-color, #3569AE)!important;
    text-shadow: none!important;
}

.no-records-msg-text {
    background-color: var(--primary-color, #3569AE);
    border: 1px solid var(--primary-color, #3569AE);
    color: var(--primary-white, #FFFFFF);
    font-size: 14px;
    margin: -63px auto 0;
    padding: 5px 5px 5px 80px;
    text-align: left;
    width: 300px;
}

.changePassword-left {
    float: left;
}

.changePassword-right {
    float: left;
    line-height: 20px;
    margin-left: 40px;
    width: 400px;
}

div#myModal-actionforaction {
    min-height: 300px;
    width: 85%;
}

.modal-container {
    display: block;
}

#actionforactionForm label {
    width: 250px;
    margin: 0;
    margin-bottom: 10px;
}

#actionforactionForm .redAsterix {
    margin-left: 5px;
}

#actionforactionForm textarea {
    clear: both;
    margin-left: 0;
    width: 98.8%;
}

#actionforactionForm span {
    margin-right: 0;
    margin-left: 0;
}

#parentdivModalrenderActionforAction {
    border: 1px solid #d1d1d1;
    overflow: auto;
    top: 10px;
    border-radius: 4px;
}

#parentdivModalrenderActionforAction .divth-data img {
    margin-left: 28%;
}

#myModal-actionforcommentincorporation label#labelmarkaspriv {
    width: 630px;
    display: inline-block;
}

#for-action-Chkinc {
    margin: 0;
}

#for-action-Chkcoo {
    margin-left: 20px;
    margin-top: 0;
}

#date {
    width: auto;
}

.changePassword-right #password-strength-container {
    background: url("../images/icons/gray_arrow_separator.gif") no-repeat scroll left top transparent;
    float: left;
    margin-top: 18px;
    padding-left: 17px;
}

#formChangePassword .form-horizontal .control-group:before, .form-horizontal .control-group:after {
    margin-bottom: 10px;
}

#divAsiteTerms {
    font-size: 11px;
}

a#sidenav-cont-createNew {
    background: url(../images/icons/sidenav_apps_createforms.png) no-repeat;
    color: var(--primary-black, #000000);
}

a#sidenav-cont-messages {
    background: url(../images/icons/sidenav_exchange_messages.png) no-repeat;
    color: var(--primary-black, #000000);
}

.customPanel {
    min-height: 200px;
}

.customPanelHead {
    color: #6C6C6C;
    background-color: white;
    padding: 5px 10px;
    border: none;
    border-top-style: solid;
    border-top-width: medium;
    border-top-color: #D1D1D1;
}

.customPanelHead table {
    max-width: 100%;
}

.customPanelHead table table td {
    vertical-align: baseline;
    padding-right: 15px;
}

.customPanelHead label {
    display: inline;
    font-weight: bold;
}

.customPanelHead img {
    vertical-align: text-bottom;
}

.customPanelOpen {
    background-color: white;
}

.customPanelActive {
    background-color: #6C6C6C;
    color: white;
}

.customPanelHead .userimg {
    height: 50px!important;
    width: 48px!important;
    margin-right: 5px;
}

.issueAccessLinkSpan a {
    padding-right: 5px;
}

.customPanelHead span.wrapTextField {
    display: inline-block;
    max-width: 200px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: middle;
    padding-bottom: 1px;
}

#layerscreen {
    position: fixed;
    _position: absolute;
    top: 150px;
    z-index: 99999;
    margin-left: 15px;
}

#layerhelp {
    border-left: 1px dotted #d4d4d4;
    float: right;
    font-size: 12px;
    height: 39px;
    margin: 8px 0 0 0;
    padding: 5px 5px 5px 15px;
    width: 30px;
}

.helpcontent:not(.logicalClass) {
    color: #777;
    display: block;
    float: right;
    font-size: 20px;
    font-weight: 200;
    margin-left: -20px;
    margin-right: 0;
    padding: 5px 5px 0 15px;
    text-shadow: 0 1px 0 var(--primary-white, #FFFFFF);
    width: 30px;
}

.modal-header .helpcontent, .modal-scrollable .modal-header .helpcontent {
    margin-left: 0!important;
    padding: 0!important;
    width: 16px!important;
    cursor: pointer!important;
}

.modal-scrollable .modal-header .helpcontent:focus{
    outline: auto;
}

#project-setting input[type="radio"]:focus,
#project-inheritance input[type="radio"]:focus,
#project-emailNotification input[type="radio"]:focus,
#project_calender input[type="radio"]:focus,
#edit-projects input[type="radio"]:focus,
#project-setting input[type="checkbox"]:focus,
#project-inheritance input[type="checkbox"]:focus, 
#project-emailNotification input[type="checkbox"]:focus, 
#project_calender input[type="checkbox"]:focus,
#edit-projects input[type="checkbox"]:focus {
    outline: auto;
}

#edit-projects #editselectGeograpy,
#edit-projects #selectViewer,
#edit-projects #editselectStatus {
    font-size: 14px;
}

#project-additionalinfo #edittextareadesc,
#project-additionalinfo #countryID,
#project-additionalinfo #stateID {
    font-size: 14px;
}

#project-setting #qrCodeDisplacementBottom,
#project-setting #qrCodeDisplacementRight,
#project-setting #qrCodeDisplacementTop,
#project-setting #qrCodeDisplacementLeft,
#project-setting #resultantMessageSuperseded,
#project-setting #resultantMessageLatest {
    font-size: 12px;
}

.fullwidth-box-top .helpcontent {
    margin-top: 7px;
    margin-left: 0;
    padding: 0;
    width: 16px;
}

.fullwidth-box-top button.close {
    margin-left: 6px!important;
}

#apps .fullwidth-box-top button.close {
    margin-left: 0px!important;
    padding-left: 6px;
}

.helpcontent img {
    height: 16px!important;
    width: 16px!important;
}

.modal-header .helpcontent:focus, .modal-scrollable .modal-header .helpcontent:focus,
.modal-header .maximized:focus, .modal-header .restored:focus, .modal-scrollable .modal-header .maximized:focus, .modal-scrollable .modal-header .restored:focus, .fullwidth-box-top button.close:focus-visible {
    outline: auto;
}

.modal-header .helpcontent img, .modal-scrollable .modal-header .helpcontent img {
    margin-top: 6px!important;
}

.modal-header .maximized, .modal-header .restored, .modal-scrollable .modal-header .maximized, .modal-scrollable .modal-header .restored {
    margin-right: 3px!important;
    cursor: pointer;
}

.modal-header .maximized img, .modal-header .restored img, .modal-scrollable .modal-header .maximized img, .modal-scrollable .modal-header .restored img {
    margin-top: 6px;
    height: 16px;
    width: 16px;
    cursor: pointer;
}

.icon-chevron-down {
    background-image: url("/images/dropdown-arrow.png");
    background-position: 0 0;
}

#divAddItemCatalogue {
    padding: 0;
    text-align: right;
    clear: both;
}

#buttnShowCatalogue {
    margin-left: 10px;
}

.box-templateName, .box-companyname, .box-type {
    overflow: hidden;
    padding: 0 0 0 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 215px;
}

.box-companyname, .box-type {
    color: #808080;
    font-size: 14px;
}

#modalInstallWorkspaceTemplate.modal {
    width: 400px;
}

#modalViewWorkspaceTemplate.modal {
    width: 950px;
}

#installWorkspaceTemplate .control-label {
    text-align: left;
    width: 120px!important;
}

#modalInstallWorkspaceTemplate form {
    margin: 0;
}

.after_insatllation {
    display: block;
}

#installationProgressbar {
    text-align: center;
}

#selClassification {
    width: 100%;
    margin: 0;
}

#modalViewWorkspaceTemplate>.modal-body>div {
    display: block;
}

#basketListingSection {
    margin-left: 0;
    overflow: hidden;
}

#first_row {
    height: 150px;
    width: 100%;
    border-radius: 5px;
}

#first_row>div {
    float: left;
}

#first_row #col1 {
    text-align: center;
    width: 220px;
    height: 150px;
}

#first_row #col1 button {
    margin: 110px 5px 0 0;
}

#first_row #col2 {
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 76%;
}

#publicApps #first_row #col2 {
    width: 66%;
}

#publicApps #col2 {
    height: 80px;
}

#first_row .WTdetails_name {
    font-size: 1.3em;
    margin-bottom: 3px;
    margin-top: 0;
    text-indent: 15px;
    font-weight: bold;
}

#first_row .WTdetails_cname {
    font-size: .9em;
    margin-top: 0;
    text-indent: 15px;
    font-weight: bold;
}

#first_row .desc-details {
    font-size: .9em;
    font-weight: bold;
    padding-left: 15px;
    padding-right: 15px;
    text-align: justify;
    margin-top: 10px;
    margin-right: 2px;
}

#first_row #col2>button {
    margin-right: 10px;
}

#second_row {
    margin-top: 3px;
    padding: 5px 0 0;
    width: 100%;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

#second_row .span6 {
    margin-left: 0!important;
    margin-right: 1%!important;
    width: 49%;
}

#second_row>div {
    float: left;
}

#second_row #_col1 {
    height: 100%;
}

.brochures-details {
    text-indent: 10px;
}

#modalViewWorkspaceTemplate .head {
    font-size: 1.3em;
    margin-bottom: 20px;
}

#s2_col {
    height: 170px;
}

#s3_col {
    height: 100px;
    margin-top: 10px;
    width: 100%;
}

#publicApps #s3_col {
    margin-top: 30px;
}

.appslegend {
    background: none repeat scroll 0 0 var(--primary-white, #FFFFFF);
    font-weight: bold;
    margin: -12px 0 0 17px;
    padding-left: 5px;
    width: 95px;
}

.appslegend.br {
    width: 75px;
}

.appslegend.vt {
    width: 50px;
}

#appVideo {
    padding: 10px;
}

#appVideo>video {
    background-color: var(--primary-black, #000000);
    width: 100%;
}

#publicApps #s1_col1 .desc-details {
    height: 120px;
    max-height: 120px;
}

#videosPagination {
    margin-top: -7px;
    float: none;
}

.screenShots {
    display: block;
    float: left;
    height: 125px;
    margin-bottom: 5px;
    padding: 5px 10px;
}

.screenShots a>img {
    border: 1px solid var(--primary-black, #000000);
    border-radius: 4px;
    height: 120px;
    width: 200px;
}

#publicApps .screenShots a>img {
    border: 1px solid var(--primary-black, #000000);
    border-radius: 4px;
    height: 120px;
    width: 138px;
}

#screenShotsSection {
    float: left;
    height: 100%;
    margin: 0;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    width: 100%;
}

#s1_col3 {
    margin-left: 15px;
}

#installWorkspaceTemplate #inputproname {
    width: 230px!important;
}

.fancybox-custom .fancybox-skin {
    box-shadow: 0 0 50px #222;
}

#modalInstallWorkspaceTemplate .form-horizontal .controls {
    margin-left: 121px!important;
}

.appsBorder {
    border: 3px solid #bbb;
    border-radius: 5px;
    margin-top: 10px;
}

#_col22 {
    height: 255px;
}

#publicApps #s3_col .forms {
    font-size: 16px;
    width: 650px;
    max-width: 650px;
}

#publicApps #s3_col .forms>span {
    margin-right: 10px;
}

#s1_col2>video {
    background-color: var(--primary-black, #000000);
}

#myModal-publishCatalog {
    width: 580px;
}

#myModal-publishCatalog .separator {
    display: none;
    border-top: 1px dotted #DCDCDC;
    height: 1px;
    left: 0;
    margin: 0 10px;
    position: absolute;
    top: 135px;
    width: 97%;
}

#publishCatalogForm .options input {
    float: left;
    margin-right: 15px;
}

#publishCatalogForm .filename {
    display: none;
    float: left;
    margin-right: 0;
    margin-top: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 257px;
}

#myModal-publishCatalog .btn-file {
    width: 55px;
}

#appsSelection .close, #createForm #closeform, #printForm .close {
    padding-right: 10px;
}

#appsSelection .pull-right.manage-selects>a {
    margin-left: 5px;
}

#publishCatalogErrorForm .msg-error-report {
    float: left;
    font-size: 14px;
    width: 700px;
    margin-top: 5px;
}

#publishCatalogErrorForm .msg-error-report.exceed {
    font-size: 16px;
}

#publishCatalogErrorForm .msg-error-report .record-no {
    font-weight: bold;
}

#publishCatalogErrorForm .errMsg textarea {
    color: #C00;
    width: 150px;
}

.modal-scrollable button.close {
    float: right;
    font-size: 16px;
    font-weight: 300;
    line-height: 20px;
    color: var(--primary-white, #FFFFFF);
    text-shadow: 0 1px 0 var(--primary-black, #000000);
    background: none;
    min-width: 0;
    height: 20px;
    filter: none;
    margin-top: 6px;
}

.modal-scrollable .modal-header button.close {
    height: 26px;
    margin-top: 0;
    width: 20px;
}

.modal-scrollable #myModal-projectEdit  .modal-header button.close {
    margin-left:12px;
}

.modal .modal-header button.close:focus-visible, .modal-scrollable .modal-header button.close:focus-visible {
    outline: auto;
    outline-width: 1px;
    outline-style: solid;
}

.modal-overflow .modal-body{

    overflow: hidden;

}
.accordianBorder {
    border: 1px dotted #D3D3D3;
}

#viewCatalogModal #treeView .tree-item>.tree-row, #viewDirectoryCatalogModal #treeView .tree-item>.tree-row {
    float: none;
    width: auto;
}

#viewCatalogModal #treeView .tree-item>.tree-row>a, #viewCatalogModal #treeView .tree-item>.tree-row .tree-label, #viewDirectoryCatalogModal #treeView .tree-item>.tree-row>a, #viewDirectoryCatalogModal #treeView .tree-item>.tree-row .tree-label {
    width: 100%;
}

#productCompareDetail, #productStaticContent, #productTiledListing, #thumb-view-product, #list-view-product {
    overflow: auto;
    min-height: 200px;
}

#productTiledListing, #thumb-view-product, #list-view-product {
    padding-top: 20px;
    overflow-y: auto;
    padding-bottom: 30px;
}

#directoryCatalogListingViewblocks {
    overflow-y: auto;
}

#viewCatalogModal #productListPaging, #viewDirectoryCatalogModal #productListPaging {
    position: relative;
    z-index: 1;
    width: auto;
    clear: both;
    margin: 10px 17px 0;
}

#viewCatalogModal #productListPaging ul, #viewDirectoryCatalogModal #productListPaging ul {
    margin-top: 0px;
}

.viewCatalogModalContent #viewCatalogModal #productListPaging ul {
    margin-top: 0px;
}

#externalCatalogJSP #bodyPart.viewCatalogModalContent {
    background-color: #ffffff;
}

#externalCatalogJSP #externalCheckout {
    margin-bottom: 10px;
}

#externalCatalogJSP #bodyPart.viewCatalogModalContent .row-fluid {
    background-color: #eeeded;
}

.viewCatalogModalContent .table thead {
    background-color: #ffffff;
}

.viewCatalogModalContent .table tr {
    background: none;
}

.viewCatalogModalContent .table tbody tr:nth-child(odd) {
    background-color: #eeeded !important;
}

.viewCatalogModalContent .table tbody tr:nth-child(even) {
    background-color: #ffffff !important;
}

#productCompareDetail, #productStaticContent, .hideSearchBtn #sidenav-catalog-basket, .hideSearchBtn .addToCartBtn {
    display: none;
}

#productStaticContent {
    text-align: center;
}

#productStaticContent img {
    vertical-align: middle;
    cursor: pointer;
}

#productsTreeListing .categoryGroup {
    width: 100%;
}

#productsTreeListing .tree-row a {
    width: 95%;
    padding-left: 5px;
}

#productsTreeListing .tree-label {
    max-width: 80%;
}

#productsTreeListing .tree-label-count {
    color: var(--primary-black, #000000);
    font-size: 12px;
    line-height: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    float: none;
    display: inline-block;
    vertical-align: top;
}

#productsTreedialog, #productsTreeListing {
    height: 100%;
}

#productFileListing .view-switch-top-catalogContent {
    width: 220px;
    margin: 0 0 0 10px;
}

#productTiledListing .boxgrid, #thumb-view-product .boxgrid, #productFileListing .boxgrid {
    padding: 10px;
    text-align: left;
    width: 185px;
    font-size: 12px;
    overflow: visible;
}

#productFileListing .boxgrid {
    margin: 0 0 20px 10px;
}

#productContentContainer .width-padded a.btn-mini {
    margin: 2px 5px 0 0;
}

#productStaticContent .static-catalog-text {
    top: 50%;
    position: relative;
    margin-top: -80px;
}

#change-catalog-status-modal {
    width: 490px;
}

#change-catalog-status-modal select {
    height: 34px;
    line-height: 34px;
}

.box-productPrice>div {
    line-height: 20px;
}

#productCompareDetail .color-coding-wrapper {
    padding: 10px 10px 0;
}

#productCompareDetail .color-coding-wrapper .alert {
    margin-bottom: 5px;
    font-size: 13px;
    padding: 3px 35px 3px 14px;
}

#productContentContainer #color-coding-tooltip {
    width: 22px;
    float: right;
    cursor: pointer;
}

#productContentContainer #color-coding-tooltip+.popover {
    width: 137px;
}

#productContentContainer #color-coding-tooltip+.popover ul {
    margin: 0;
    list-style: none;
}

#productContentContainer #color-coding-tooltip+.popover ul li {
    padding: 4px 10px;
    font-size: 13px;
}

#productContentContainer #color-coding-tooltip+.popover ul li span {
    margin-right: 10px;
    width: 15px;
    height: 15px;
    vertical-align: middle;
    display: inline-block;
}

#productContentContainer>.width-padded {
    padding: 0 8px 5px;
}

#productContentContainer .updated {
    color: #f00000;
}

#productContentContainer .new {
    color: #00c;
}

#productContentContainer .deleted {
    color: #600000;
}

#productContentContainer .updated .block {
    background-color: #f00000;
}

#productContentContainer .new .block {
    background-color: #00c;
}

#productContentContainer .deleted .block {
    background-color: #600000;
}

#adoddleAlertUI .compare-catalogue-dialog {
    vertical-align: middle;
    margin-left: 7px;
    cursor: pointer;
}

#productCompareDetail .table-wrapper {
    padding: 0 10px;
}

#productCompareDetail table {
    min-width: 100%;
    padding: 0;
    font-size: 13px;
}

#productCompareDetail table .arrow {
    width: 10px;
    height: 10px;
    margin: 5px 5px 5px 0;
    float: left;
    background: url("../images/arrows.png") no-repeat -4px -6px;
}

#productCompareDetail table .expand .arrow {
    background: url("../images/arrows.png") no-repeat -20px -6px;
}

#productCompareDetail table .child td {
    padding-left: 45px;
    word-break: break-all;
}

#productCompareDetail table th {
    background: var(--primary-color, #3569AE);
    padding: 5px 10px;
    color: var(--primary-white, #FFFFFF);
    border: 1px solid #ccc;
    text-align: left;
}

#productCompareDetail table td {
    padding: 3px 10px;
    border: 1px solid #ccc;
    text-align: left;
}

.box-productPrice {
    color: var(--primary-white, #FFFFFF);
    position: absolute;
    top: 15px;
    right: 5px;
}

.tag-triangle {
    border-color: transparent var(--primary-color, #3569AE) transparent transparent;
    border-style: solid;
    border-width: 12px;
    width: 0;
    height: 0;
    float: left;
}

.tag-rectangle {
    font-weight: bold;
    padding: 2px 5px;
    background-color: var(--primary-color, #3569AE);
    float: left;
    box-shadow: 5px 5px 10px #808080;
}

.tag-hole {
    width: 5px;
    height: 5px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    background-color: #E7E9EB;
    position: absolute;
    left: 20px;
    top: 9px;
}

#list-view-product .box-productName {
    float: left;
    width: 100%!important;
    color: var(--primary-color, #3569AE);
}

#thumb-view-product .box-productName {
    float: left;
    width: 100%!important;
    color: var(--primary-color, #3569AE);
}

#productTiledListing .boxgrid button, #thumb-view-product .boxgrid button, #directoryCatalogListingblocks .boxgrid button {
    font-size: 12px!important;
    margin-top: 5px;
}

#product-listing-container #list-view-product .boxgrid .add-to-cart {
    margin-top: 2px;
}

#searchCatalogItemSection span {
    float: left;
}

#searchCatalogItemSection label {
    display: inline;
    font-size: 12px;
    cursor: default;
}

#searchCatalogItemSection input {
    width: 125px;
    margin-right: 5px;
    margin-bottom: 5px;
    font-size: 13px;
    padding: 3px 5px;
    height: 20px;
}

#searchCatalogItemSection input.placeholderClass {
    color: #aaa;
}

#viewDirectoryCatalogModal {
    height: 92%;
}

#viewDirectoryCatalogModal #searchCatalogItemSection {
    margin-left: 5px;
}

#viewDirectoryCatalogModal .modal-body {
    height: 97%;
}

#viewCatalogModal, #viewDirectoryCatalogModal {
    width: 97%;
}

#viewCatalogModal #treeListing, #viewDirectoryCatalogModal #treeListing, #directoryProductsListTemplete #treeListing {
    height: 100%;
}

#viewCatalogModal #treedialog, #viewDirectoryCatalogModal #treedialog, #directoryProductsListTemplete #treedialog {
    overflow: auto!important;
    border-top: none !important;
}

#productContent, #basketContent {
    height: auto;
    min-height: 300px!important;
    max-height: 96%;
    overflow-y: auto;
}

#basketContent input[type="text"], #productContent input[type="text"], #assoc-prd-Content input[type="text"] {
    width: 100px;
    height: 22px;
    padding: 1px;
}

#basketContent input[type="number"], #productContent input[type="number"], #assoc-prd-Content input[type="number"] {
    width: 100px;
    height: 22px;
    padding: 1px;
}

#basketContent .col-notes input, #productContent .col-notes input, #assoc-prd-Content .col-notes input {
    width: 93%;
    padding-left: 0;
    padding-right: 0;
}

#viewCatalogModal form, #viewDirectoryCatalogModal form {
    margin: 0;
}

#viewDirectoryCatalogModal #myModal-compareItems-top {
    margin: 10px;
}

.orderQtyBasket {
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    width: auto!important;
}

#wbsCodeModal, #listCostAllocationModel {
    width: 300px;
}

#wbsCodeListingSection {
    padding: 0 0 20px 0;
}

#wbsCodeSelect {
    width: 100%;
    padding: 4px 6px;
}

.addBasketIcon {
    height: 21px;
    width: 21px;
    vertical-align: middle;
    margin-left: 5px;
    margin-right: 5px;
}

#compareItemsModal {
    width: 95%;
    max-width: 90%;
    max-height: 96%;
}

#compareItemsListingSection {
    margin-top: 10px;
    overflow: auto;
}

#compareItemsTableContent td, #compareItemsTableHeader td {
    border-style: solid;
    border-color: gray;
    border-width: 1px;
    padding: 2px 4px;
    max-width: 170px;
    font-size: 14px;
}

#compareItemsTableContent td.firstTd, #compareItemsTableHeader td.firstTd {
    background-color: #E5E5E5;
    color: black;
    font-weight: bold;
    min-width: 150px;
}

input.itemQty {
    width: 97px;
    margin-bottom: 1px!important;
}

input.width50 {
    width: 50px;
    margin-bottom: 1px!important;
}

.divth label {
    color: #505050;
}

#uploadDt .ui-state-active {
    border-color: var(--primary-color, #3569AE);
}

#directoryProductsListTemplete .manage-selects {
    margin: 2px 25px 0 0;
}

#directoryProductsListTemplete #catalogMainTitle h2 {
    width: 70%;
}

.overlay-filter-process {
    background: none repeat scroll 0 0 #D3D3D3;
    left: 445px;
    opacity: .45;
    position: absolute;
    z-index: 1000;
}

#myModal-publishCatalogError {
    width: 1000px;
    max-height: 500px;
}

#myModal-publishCatalogError .modal-body {
    max-height: 430px;
    overflow: auto;
}

#table_error_report {
    border: 1px solid #D3D3D3;
}

#table_error_report textarea, #table_error_report input {
    border-radius: 0;
    padding: 0;
    width: 80px;
}

#table_error_report th, #table_error_report td {
    padding-left: 10px;
    border-right: 1px solid #D3D3D3;
}

#table_error_report th {
    color: var(--primary-black, #000000);
    font-size: 13px;
}

#PD_AddtoCart {
    float: right;
    margin-right: 10px;
    color: white;
}

#viewProcutDetailsModal.hideCart #PD_AddtoCart {
    display: none;
}

#PD_AddtoCart img {
    margin: 4px;
    float: left;
}

#PD_AddtoCart>span {
    float: left;
    line-height: 1.6;
    margin: 0;
}

#viewProcutDetailsModal .modal-body {
    height: 650px;
    padding-top: 20px;
    overflow: auto;
}

#viewProcutDetailsModal {
    font-size: 14px!important;
    width: 95%;
}

.catalogViewProcutDetailsModal {
    width: 60%!important;
}

.catalogViewProcutDetailsModal #featuresSection {
    width: 60%!important;
}

.catalogViewProcutDetailsModal #prodImageSection {
    width: 35%!important;
}

#viewProcutDetailsModal #showMoreDetail {
    color: #BE2221;
    font-size: 14px;
}

#featuresSection {
    float: left;
    margin-right: 0;
    width: 35%;
    overflow-y: auto;
    height: 490px;
}

#featuresTitle {
    font-size: 18px;
    font-weight: bold;
}

#buyNowSection {
    float: right;
    margin-right: 1%;
    width: 19%;
}

#viewProcutDetailsModal.hideCart #buyNowSection {
    display: none;
}

.directory-prd-details-true #buyNowSection {
    display: none;
}

.directory-prd-details-true #featuresSection {
    width: 65%;
}

.buyNowtitle {
    background-color: #D3D3D3;
    font-weight: bold;
    padding: 5px 2px;
    text-align: center;
    width: 100%;
}

#txtQuantity {
    float: left;
    height: 18px;
    margin-top: 8px;
    text-align: center;
    width: 93%;
}

#prodImageSection {
    float: left;
    margin-right: 2%;
    width: 30%;
    height: 400px;
}

#featuresSectionDetails {
    border-bottom: 1px solid #CCC;
    float: left;
    height: 270px;
    margin-top: 0;
    overflow: hidden;
    width: 100%;
}

#featuresTitle {
    font-size: 18px;
    font-weight: bold;
}

#relatedProductSection {
    float: left;
    margin-right: 1%;
    width: 124px;
}

.wrapText {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

#left-nav-blocks .btn-disabled:hover {
    color: var(--primary-black, #000000)!important;
}

#frmELearningLogin, #frmDownloadCatalog {
    margin: 0;
}

.manage-files.span10, div#productListingSection {
    margin-left: .5%;
    width: 84.906%;
    height: 100%;
    z-index: 1;
    POSITION: RELATIVE;
}

.main-files #resizeDiv {
    margin-right: 9.1875px !important;
}

.main-files #resizeDiv>.ui-resizable-handle {
    z-index: 5 !important;
}

.main-files #docListingSection {
    overflow: hidden;
    width: auto;
    float: none;
}

#assocFilesModal #resizeDiv, #assocDiscussModal #resizeDiv, #assocCommsModal #resizeDiv {
    margin-right: 8px;
}

#assocFilesModal #docListingSection, #assocDiscussModal #discussListingSection, #assocCommsModal #assocCommsListingSection {
    width: auto!important;
    overflow: hidden;
    float: none;
    margin-left: 8px;
}

#assocFilesModal #docListingSection.window-maximize, #assocDiscussModal #discussListingSection.window-maximize, #assocCommsModal #assocCommsListingSection.window-maximize, .window-maximize {
    background: none repeat scroll 0 0 var(--primary-white, #FFFFFF);
    height: 100%!important;
    left: 0!important;
    margin: 0!important;
    padding: 0;
    position: absolute!important;
    top: 0!important;
    width: 100%!important;
    overflow-x: hidden;
    z-index: 99999;
    border: none;
}

.window-maximize.manage-files {
    z-index: 19;
}

#myModal-upload.window-maximize .modal-body {
    height: auto!important;
}

#myModal-upload.window-maximize .modal-footer {
    clear: both;
}

#myModal-upload.window-maximize .file-attr-table-container {
    max-height: none!important;
}

#myModal-upload.modal.fade.in, #modalEditProxy.modal.fade.in, #appsDistributionAction.modal.fade.in {
    transition: top .5s ease-out, margin-top 0s ease-out;
}

#filterFormData .divtr {
    cursor: pointer;
}

.buttonUI {
    border-color: rgba(0, 0, 0, 0);
    border-image: none;
    border: 1px solid rgba(0, 0, 0, 0);
    border-radius: 3.01px;
}

#clickHereNewForm {
    border-bottom: 1px solid var(--primary-white, #FFFFFF);
    color: var(--primary-white, #FFFFFF);
    padding-bottom: 0;
}

.fileRHdivgroup.span8 {
    width: 67.812%;
    margin-left: .564%;
}

#viewModels.span8 {
    width: 67.812%;
    margin-left: .564%;
}

.buttonUI:hover {
    background: -moz-linear-gradient(center top, var(--primary-white, #FFFFFF) 0, #F7F7F7 100%) repeat scroll 0 0 rgba(0, 0, 0, 0);
    border-color: #999;
    color: var(--primary-black, #000000);
    text-decoration: none;
}

#viewProcutDetailsModal .modal-header h3,
#viewProcutDetailsModal .modal-header .myModalLabel {
    width: 80%;
    max-width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#publicApps .modal-header {
    padding: 2px 15px;
}

#publicApps .modal-header h3,
#publicApps .modal-header .myModalLabel {
    line-height: 25px;
    font-size: 14px;
}

.nav .dropdown-toggle .caret {
    border-left-width: 4px;
    border-right-width: 4px;
    border-top-width: 4px;
    border-bottom-color: var(--primary-black, #000000);
    border-top-color: var(--primary-black, #000000);
}

.noBg {
    background: none!important;
}

#to_list {
    float: left;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    white-space: nowrap;
}

#discussionHeaderIcons, #fileviewHeaderIcons {
    float: right;
    padding-right: 7px;
}

#discussionHeaderIcons>a, #fileviewHeaderIcons>a {
    height: auto;
    margin-left: 10px;
    display: inline-block;
    float: none;
    vertical-align: middle;
    margin-top: 4px;
    color: var(--primary-white, #FFFFFF);
}

#discussionHeaderIcons>a.helpcontent, #fileviewHeaderIcons>a.helpcontent {
    padding: 0;
}

#discussionHeaderIcons>a>img, #fileviewHeaderIcons>a>img {
    width: 16px;
    height: 16px;
    margin-top: 4px;
}

#discussionHeaderIcons>a.helpcontent>img, #fileviewHeaderIcons>a.helpcontent>img {
    margin-top: 1px;
}

#discussionHeaderIcons .maximized>img, #discussionHeaderIcons .restored>img, #fileviewHeaderIcons .maximized>img, #fileviewHeaderIcons .restored>img {
    width: 18px;
    height: 18px;
    margin-top: 0;
}

#fileHistoryHeaderIcons, #formHistoryHeaderIcons, #formAssociationHeaderIcons {
    float: right;
    color: white;
}

#fileHistoryHeaderIcons select, #formHistoryHeaderIcons select, #formAssociationHeaderIcons select {
    margin-top: 3px;
    max-width: 60px;
    border-color: var(--primary-white, #FFFFFF);
}

#fileHistoryHeaderIcons span.lable, #formHistoryHeaderIcons span.lable, #formAssociationHeaderIcons span.lable {
    margin-top: 3px;
    float: left;
    font-size: 12px;
}

#fileHistoryHeaderIcons .restored, #formHistoryHeaderIcons .restored, #formAssociationHeaderIcons .restored, #fileHistoryHeaderIcons .maximized, #formHistoryHeaderIcons .maximized, #formAssociationHeaderIcons .maximized {
    margin: 0 7px 0 -2px;
    position: relative;
    top: -2px;
}

#commRightIcons img, #fileHistoryHeaderIcons .helpcontent, #formHistoryHeaderIcons .helpcontent, #formAssociationHeaderIcons .helpcontent {
    vertical-align: middle;
}

#fileHistoryHeaderIcons .helpcontent>img, #formHistoryHeaderIcons .helpcontent>img, #formAssociationHeaderIcons .helpcontent>img {
    margin-top: 1px;
}

#commRightIcons span {
    padding-right: 8px;
    font-size: 12px;
}

.fRfTTable {
    font-size: 12px;
}

#commshortDescContainer {
    display: table;
    table-layout: fixed;
    width: 85%;
    margin-bottom: 5px;
}

#commshortDesc {
    display: table-cell;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.commentUserImage {
    padding: 5px 5px 5px 1px;
}

.commentInfo {
    width: auto;
    overflow: hidden;
    padding-right: 3px;
}

.commentInfo>div {
    clear: both;
}

.commentShortDesc {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 140px;
    height: 20px;
}

.commentDuration {
    padding-right: 8px;
}

.tblCommentsTree {
    margin: 0;
}

.viewFormMoreOption {
    float: right;
    padding-right: 30px;
}

.tblCommentsTree #moreOption, .tblCommentsTree #lessOption {
    margin-bottom: 4px;
}

#moreOption b, #lessOption b {
    color: var(--primary-black, #000000);
}

#lessOption {
    clear: both;
}

.newdiscussionLayout #fileCommsSection, .newdiscussionLayout #modelCommsSection {
    font-size: 12px;
}

.comHLine {
    background: url('../images/icons/com_vline.gif') repeat-y;
    background-position: center;
}

.unReadMsg #ctitle {
    font-weight: bold;
}

.normalComment, .privateComment, .markupComment, .privateMarkupComment {
    width: 24px;
    height: 24px;
    background-size: 24px 24px;
    background-position: center;
}

.normalComment {
    background: url('../images/icons/normalComment.png');
}

.unReadMsg .normalComment {
    background: url('../images/icons/normalComment_unRead.png');
}

.privateComment {
    background: url('../images/icons/privateComment.png');
}

.unReadMsg .privateComment {
    background: url('../images/icons/privateComment_unRead.png');
}

.markupComment {
    background: url('../images/icons/markupComment.png');
}

.unReadMsg .markupComment {
    background: url('../images/icons/markupComment_unRead.png');
}

.privateMarkupComment {
    background: url('../images/icons/privateMarkupComment.png');
}

.unReadMsg .privateMarkupComment {
    background: url('../images/icons/privateMarkupComment_unRead.png');
}

.parentDocComment .normalComment {
    background: url('../images/icons/link_normalComment.png');
}

.parentDocComment.unReadMsg .normalComment {
    background: url('../images/icons/link_normalComment_unRead.png');
}

.parentDocComment .privateComment {
    background: url('../images/icons/link_privateComment.png');
}

.parentDocComment.unReadMsg .privateComment {
    background: url('../images/icons/link_privateComment_unRead.png');
}

.parentDocComment .markupComment {
    background: url('../images/icons/link_markupComment.png');
}

.parentDocComment.unReadMsg .markupComment {
    background: url('../images/icons/link_markupComment_unRead.png');
}

.parentDocComment .privateMarkupComment {
    background: url('../images/icons/link_privateMarkupComment.png');
}

.parentDocComment.unReadMsg .privateMarkupComment {
    background: url('../images/icons/link_privateMarkupComment_unRead.png');
}

.textOverflow, .actionTd, #docListingSection .fullwidth-box-top h2 {
    text-overflow: ellipsis;
    width: auto;
    overflow: hidden;
    white-space: nowrap;
}

#directoryCatalogProductsListingblocks .textOverflow, .actionTd {
    display: block;
}

#docListingSection .fullwidth-box-top h2 {
    max-width: 50%;
}

#myModal-Comment .modalbody {
    overflow-y: auto;
}

.tblCommentsTree .comDesc {
    width: 100%;
}

#oriCommentInfo #oriComment {
    width: 99%;
    word-break: break-all;
}

#oriCommentInfo {
    max-height: 200px;
    overflow: auto;
}

#formAllAssociationList {
    min-height: 300px;
    padding: 0 5px;
    cursor: pointer;
}

#formAllAssociationContainer {
    overflow-y: auto;
    font-size: 12px;
}

#formAllAssociationList .assocHeaderRow {
    height: 35px;
    margin-top: 5px;
}

#formAllAssociationList .assocHeaderRow>div {
    margin-bottom: 0;
}

#formAllAssociationList .associationRow {
    border-top: 1px solid black;
    padding-bottom: 5px;
    cursor: default;
}

#formAllAssociationList .associationRow[canDownload="1"] img.downloadSingleFile {
    cursor: pointer;
}

#formAllAssociationList .associationRow:hover {
    background: none #E5E5E5!important;
}

#formAllAssociationList .chkBoxDiv {
    float: left;
    text-align: center;
}

#formAllAssociationList .chkBoxDiv input {
    margin: 5px 7px;
}

#allAssociationList {
    min-height: 300px;
    padding: 0 5px;
    cursor: pointer;
}

#allAssociationContainer {
    overflow-y: auto;
    font-size: 12px;
}

#allAssociationList .assocHeaderRow {
    height: 35px;
    margin-top: 5px;
}

#allAssociationList .assocHeaderRow>div {
    margin-bottom: 0;
}

#allAssociationList .associationRow {
    border-top: 1px solid black;
    padding-bottom: 5px;
    cursor: default;
}

#allAssociationList .associationRow[canDownload="1"] img.downloadSingleFile {
    cursor: pointer;
}

#allAssociationList .associationRow ul.tree {
    margin-left: 30px;
    clear: both;
}

#allAssociationList .associationRow ul li {
    font-weight: inherit;
    line-height: 25px;
    background-position-y: 6px;
}

#allAssociationList .associationRow ul.tree li a {
    color: var(--primary-color, #3569AE);
}

#allAssociationList .associationRow ul.tree li a:hover {
    color: var(--hover-color, #335D93);
}

#allAssociationList .associationRow .pright20 {
    padding-right: 20px;
}

#allAssociationList .associationType3 {
    overflow: inherit;
    margin-bottom: 10px;
}

#allAssociationList .associationRow ul li:last-child {
    background: var(--primary-white, #FFFFFF) url(../images/lastnode.png) no-repeat;
    background-position-y: 6px;
}

#allAssociationList .associationRow:hover ul li:last-child {
    background: #E5E5E5 url(../images/lastnode.png) no-repeat;
    background-position-y: 6px;
}

#allAssociationList .selectedAssoc ul li:last-child {
    background: #E5E5E5 url(../images/lastnode.png) no-repeat;
    background-position-y: 6px;
}

#allAssociationList .associationRow ul li:last-child .xref-child>span {
    float: left;
    background-color: #D7D7D7;
    height: 9px;
    margin-left: -12px;
    margin-top: -3px;
    width: 1px;
}

#allAssociationList .associationRow:hover {
    background: none #E5E5E5!important;
}

#allAssociationList .chkBoxDiv {
    float: left;
    text-align: center;
}

#allAssociationList .chkBoxDiv input {
    margin: 5px 7px;
}

.associationRow .assocSubInfo {
    margin-left: 72px;
}

.associationRow .assocSubInfo>div {
    margin-right: 10px;
}

.associationRow .typeIcon {
    font-weight: bold;
}

.associationRow .xrefType {
    width: 90%;
}

.associationRow .typeIcon img {
    height: 18px;
    width: 18px;
    vertical-align: bottom;
    margin: 5px 5px 0 0;
}

.globalActionIcon img {
    margin: 5px;
    height: 18px;
    width: 18px;
}

.associationRow div.assocMsgCode {
    float: right;
}

.disableByOpacity {
    opacity: .5;
    cursor: not-allowed;
    pointer-events: none;
}

.resizable:not(.window-maximize) .assocMax {
    display: none;
}

#fileAllHistoryContainer, #formAllHistoryContainer, #invoiceAllHistoryContainer, #modelAllHistoryContainer, #workflowStatusContainer {
    overflow-y: auto;
    font-size: 12px;
}

#fileAllHistoryList, #formAllHistoryList, #invoiceAllHistoryList, #workflowStatusList {
    min-height: 100%;
    padding: 0 5px;
    cursor: pointer;
    background-color: var(--primary-white, #FFFFFF);
}

.userimgHistory {
    height: 40px;
    width: 40px;
    padding: 5px 10px 0 5px;
}

.historyRow:hover {
    background: none #E5E5E5!important;
}

.historyRow {
    border-top: 1px solid #ccc;
    cursor: pointer;
}

.workflowStatusRow {
    border-bottom: 1px solid #ccc;
}

.historyRow:first-child {
    border-top: none;
}

.historyRow .actionTable {
    overflow-y: auto;
    max-height: 150px;
}

.historyRow .actionRow {
    font-size: 11px;
}

.historyLine {
    margin-left: 55px;
}

.historyRow .subInfo {
    overflow: hidden;
}

.historyRow .actionTd {
    float: left;
    padding-right: 10px;
    height: 30px;
    max-width: 300px;
}

.historyRow .actionTd .userimg {
    margin-right: 3px;
}

@media screen and
/*!YUI Compressor */

(min-width: 700px) and
/*!YUI Compressor */

(max-width:1000px) {
    .historyRemark {
        max-width: 58%;
    }
}

@media screen and
/*!YUI Compressor */

(min-width: 1000px) {
    .historyRemark {
        max-width: 98%;
    }
}

.historyRow .subInfo div {
    padding-right: 10px;
}

.accessLink a {
    margin-left: 5px;
}
.accessLink span {
    margin-left: 5px;
}

.resizable:not(.window-maximize) .historyMax {
    display: none;
}

.detailTd {
    border-bottom: 1px solid #bbb;
}

#fRsT>div {
    float: right;
}

.window-maximize .historyMini {
    display: none;
}

.formCommonDetailTable {
    background: url(../images/files/filler_shadow.png) bottom repeat-x!important;
    padding: 5px 5px;
    height: 40px;
}

.formCommonDetailTable div {
    width: auto;
    padding-right: 30px;
    width: 25%!important;
}

.formCommonDetailTable lable {
    font-weight: bold;
}

.cursorDefault {
    cursor: default!important;
}

.cursorPointer {
    cursor: pointer;
}

#invoiceFormHtml {
    min-height: 400px;
}

#invoiceViewerHeader {
    background: url(../images/files/filler_shadow.png) bottom repeat-x!important;
}

.headerRightIcons {
    float: right;
    padding-right: 10px;
}

#myModal-create-form-option {
    width: auto;
}

#shareLinkModal .control-label {
    float: left;
    padding-right: 20px;
}

#shareLinkModal label {
    cursor: default;
}

#shareLinkModal .hAlign label {
    float: left;
    width: 75px;
    margin-top: 5px;
}

#shareLinkModal .hAlign>div {
    overflow: hidden;
}

#shareLinkModal .hAlign .btn {
    line-height: 19px;
    height: 30px;
    padding: 4px 9px;
    float: right;
    margin-left: 5px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-left: none;
}

#shareLinkModal .hAlign .btn.copied {
    background: #a5e88f;
    border-color: #a5e88f;
}

#shareLinkModal .hAlign .btn img {
    width: 21px;
}

#shareLinkModal .setting-container label {
    cursor: pointer;
}

#shareLinkModal .setting-container .pull-right {
    width: 50%;
}

#shareLinkModal .setting-container input[type="checkbox"] {
    vertical-align: middle;
}

#shareLinkModal .controls {
    float: left;
}

#shareLinkModal .control-group {
    margin-bottom: 0;
}

.radioBox input, .chkBox input {
    margin: 0;
}

.radioBox label, .chkBox label {
    display: inline;
}

#linkAdvanceSetting {
    display: inline;
    margin-left: 5px;
}

#shareLinkBasicOption .modal-header h3,
#shareLinkBasicOption .modal-header .myModalLabel {
    width: 85%;
}

#recipientMsg {
    width: 99.5%;
}

#linkToFile, #recipientList {
    width: 98%;
}

#recipientList {
    margin-bottom: 5px;
}

#linkToFile, #linkFilePath {
    cursor: text;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    padding: 4px 9px;
    height: 30px;
}

#visibilityIcon>img {
    vertical-align: top;
}

#expiryDetail>* {
    display: inline;
}

#expiryTimeUnit, #expiryTimeHours {
    width: 65px;
    height: auto;
    padding: 1px;
    font-size: 14px;
}

#expiryUnitDays {
    width: 30px;
    display: none;
}

#shareLinkAdvOptionForm .radioBox {
    vertical-align: middle;
}

#shareLinkAdvOptionForm #expiryDetail {
    display: inline-block;
}

#shareLinkAdvOptionForm #expiryViewLimit {
    width: 60px;
    padding: 2px 1px 2px 4px;
}

#shareLinkAdvOptionForm #expiryUnitDays {
    padding: 2px 4px 3px;
}

#shareLinkModal:hidden>div {
    display: none;
}

.shareLinkRemark {
    max-width: 50%;
}

.directory-treeitem {
    color: var(--primary-black, #000000);
    cursor: pointer;
    font-size: 14px;
    border-radius: 2px;
    font-size: 12px;
    height: 19px;
    padding: 6px 3px;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.directory-treeitem:hover {
    background: url("../images/icons/middle-bg.png") repeat scroll 0 0 rgba(0, 0, 0, 0)!important;
    border-color: #B5D4FB;
    z-index: 1;
}

.directory-treeitem>img {
    vertical-align: middle;
    margin: 0 5px;
    width: 23px;
    height: 16px;
}

#appsFilter .criteria-wrap.smallMaxWidth {
    max-width: 180px!important;
}

#fileAllHistorySection {
    margin-right: 8px;
    background-color: #f6f6f6;
}

#fileAllHistorySection a.export-icon {
    margin-top: 4px;
    display: block;
    float: right;
    margin-right: 47px;
    padding: 3px 7px;
    margin-left: 10px;
}

#fileAllHistorySection>.fullwidth-box-top.add-minwidth {
    max-height: 100%;
    height: 35px;
    overflow: hidden;
    position: relative;
}

#formAllHistorySection a.export-icon {
    background: url("/images/icons/historyExportIcon.png");
    margin-top: 2px;
    display: block;
    margin-left: 5px;
    width: 20px;
    height: 20px;
    float: right;
    margin-right: 47px;
}

.history-header-option {
    position: absolute;
    right: 5px;
}

#allAssociationSection {
    margin-right: 8px;
}

#allAssociationList a.export-icon {
    float: right;
}

.association-header-option {
    position: absolute;
    right: 5px;
}

#fileCommsSection.window-maximize, #fileAllHistorySection.window-maximize, #viewFormContain.window-maximize {
    z-index: 1000;
}

#formThreadTree {
    padding: 5px 0;
}

#formThreadTree .tblCommentsTree {
    height: 55px;
}

#formThreadTree .tblCommentsTree .fRfTTable:hover {
    background: #dde7f0;
}

#formThreadTree .tblCommentsTree .fRfTTable.selected {
    background: #eee;
}

.window-maximize-navigator {
    background: none repeat scroll 0 0 var(--primary-white, #FFFFFF);
    height: 99%;
    left: 0!important;
    margin: 0!important;
    padding: 0;
    position: absolute!important;
    top: 0!important;
    width: 100%!important;
    z-index: 960;
    overflow-x: hidden;
}

#modelAllHistorySection.window-maximize-navigator {
    height: 100%!important;
}

.graytext {
    color: #6D6D6D;
}

#formThreadTree .tblCommentsTree #ctitle {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    display: block;
    max-width: 85%;
    float: left;
}

#companies-blocks {
    overflow-x: hidden;
    overflow-y: auto;
    padding: 10px 0 0;
}

#companies-blocks .boxgrid {
    border: 1px solid #ddd;
    width: 240px;
    margin: 0 0 10px 10px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

#companies-blocks .boxgrid>img {
    width: 100%;
    height: 60px;
    display: block;
}

#companies-blocks .box-projectname {
    line-height: 23px;
    width: auto;
    text-align: left;
    font-size: 12px;
    white-space: normal;
    padding: 7px 10px 10px;
    border-top: 1px solid #ddd;
}

#companies-blocks .box-projectname>span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#companies-blocks .box-projectname>span:first-child {
    color: #c00;
    font-size: 14px;
}

#companies-blocks .box-projectname>.btn {
    float: right;
    margin: 8px 0 0;
}

#modalCompanyViewDetails {
    width: 1048px;
    max-width: 100%;
}

#modalCompanyViewDetails iframe {
    width: 100%;
    border: none;
    height: 80vh;
}

#modalCompanyViewDetails .modal-body {
    float: left;
    width: 100%;
    margin: 0;
    padding: 0;
}

#modalViewDetails {
    width: 1257px;
}

#modalViewDetailsBody {
    max-height: 750px;
    overflow: auto;
}

#modalViewDetails .divcontainer {
    border-color: #D1D1D1 -moz-use-text-color -moz-use-text-color #D1D1D1;
    border-left: 1px solid #D1D1D1;
    border-radius: 4px;
    border-style: solid;
    border-width: 1px;
    position: relative;
    margin-bottom: 15px;
    min-height: 100px;
}

#regaddmap {
    height: 100px;
}

#modalViewDetailsBody h3 {
    background: none repeat scroll 0 0 var(--primary-white, #FFFFFF);
    color: var(--primary-color, #3569AE);
    font-size: 12px;
    left: 5px;
    line-height: 10px;
    margin: 0;
    padding: 0 5px;
    position: absolute;
    top: -5px;
    z-index: 10000;
}

#modalViewDetails .divcontainer table {
    width: 100%;
    margin-top: 5px;
}

#modalViewDetails .divcontainer table strong {
    font-size: 12px;
    text-align: left;
}

#modalViewDetails .divcontainer table td, #modalViewDetails .divcontainer table th {
    font-size: 12px;
    text-align: left;
}

#modalViewDetails .divcontainer span {
    font-size: 12px;
}

.access-vq {
    background: none repeat scroll 0 0 #c00;
    clear: both;
    float: left;
    padding: 5px;
    margin-top: 15px;
    color: white;
}

#modalViewDetails .reg-address-container {
    border-color: -moz-use-text-color;
    border-radius: 0;
    border-style: none solid;
    border-width: 0 1px;
    height: 110px;
    margin-left: 0;
    margin-top: 10px;
    padding-left: 10px;
}

#modalViewDetails .client-details-container {
    height: 110px;
    margin-left: 0;
    margin-top: 10px;
    padding-left: 15px;
}

#editSharelinkIcon {
    padding-right: 5px;
    vertical-align: sub;
}

#shareLinkModal .controls>input {
    vertical-align: text-top;
}

#modalViewDetails .contact-eco-container {
    min-height: 40px;
}

#modalViewDetails .contact-eco-container table {
    margin-top: 10px;
}

#modalViewDetails .performance-management-container {
    min-height: 40px;
}

#modalViewDetails .performance-management-container span {
    float: left;
    line-height: 20px;
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
}

#filterFormData .divthead {
    display: none;
}

#fromUser {
    max-width: 130px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#assocCommsList .tblCommentsTree #ctitle {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    display: block;
    max-width: 85%;
    float: left;
}

.actionsDropdown, #actionsDropdown {
    float: left;
    font-size: 14px;
    width: 20px!important;
    padding-right: 5px;
}

.actionsDropdown .check-list-item, #actionsDropdown .check-list-item {
    padding: 4px;
}

.actionsDropdown .check-list-item a, #actionsDropdown .check-list-item a {
    padding: 3px 10px;
}

.actionsDropdown .check-list-item a img, #actionsDropdown .check-list-item a img {
    float: left;
    height: 20px;
    width: 20px;
}

.actionsDropdown .check-list-item a span, #actionsDropdown .check-list-item a span {
    float: left;
    margin-left: 10px;
    font-size: 14px;
}

@media(max-width: 1366px) {
    .tabMoreOptions+.dropdown-menu .admin-blocks {
        display: block;
    }
}

@media(max-width: 1024px) {
    .manage-files.span9 {
        margin-left: .5%;
        width: 76.006%;
    }
    #assocFilesModal .manage-files.span9 {
        float: none!important;
        overflow: hidden;
        margin-left: 10px;
        width: auto;
    }
    .manage-files.span10 {
        margin-left: 1%;
        width: 83.97872340425532%;
    }
    #assocFilesModal #resizeDiv {
        margin-right: 10px!important;
    }
    #fileCommsSection {
        width: 39.564%;
    }
    .fileRHdivgroup.span8 {
        width: 67.512%;
        margin-left: .564%;
    }
    .no-records-msg-text {
        background-color: var(--primary-color, #3569AE);
        border: 1px solid var(--primary-color, #3569AE);
        color: var(--primary-white, #FFFFFF);
        font-size: 15px;
        padding: 5px 0;
        width: 286px;
        margin: -63px auto;
        text-align: inherit;
    }
    #commshortDescContainer {
        max-width: 60%;
    }
    #assocCommsList .tblCommentsTree #ctitle {
        max-width: 90%;
    }
    #commheader {
        max-width: 75%;
        float: left;
    }
    #longcomDisc1 {
        max-width: 70%;
    }
    #moreOption {
        max-width: 70%;
    }
    .commheaderClass {
        max-width: 220px;
    }
    .showToListPopover .popover {
        width: 150px;
    }
    .assocCode {
        width: 75%;
    }
    .assocImgType {
        width: 20px;
    }
    .assocTitle {
        margin-left: 25px;
    }
    .assocListLines.multilineTextContainer {
        padding-left: 25px;
    }
    .assocmsgContent {
        width: 100%;
    }
    #showFormSection {
        display: block;
        float: none;
        width: auto;
        clear: both;
    }
}

@media(max-width: 768px) {
    .commheaderClass {
        max-width: 120px;
    }
    #to_list {
        max-width: 70%;
        white-space: nowrap;
    }
    #creationdate {
        float: left;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 70%;
        white-space: nowrap;
    }
    #commheader {
        max-width: 70%;
        float: left;
    }
    #moreOption {
        max-width: 60%;
    }
    .tblCommentsTree #comDesc {
        padding-left: 27px!important;
    }
    .showToListPopover .popover {
        width: 130px;
    }
    .assocCode {
        width: 70%;
    }
    .fullwidth-box-top h2 {
        width: 65%;
        display: block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}

@media(min-width: 1025px) and
/*!YUI Compressor */

(max-width:1199px) {
    .row-fluid .span10 {
        width: 84.5%;
    }
    .row-fluid .span9 {
        width: 75.5%;
        margin-left: 1%;
    }
}

@media(min-width: 970px) and
/*!YUI Compressor */

(max-width:1023px) {
    .row-fluid .span10 {
        width: 83.97872340425532%;
    }
}

@media(min-width: 1200px) {
    .row-fluid .span9 {
        width: 77%;
        margin-left: 1%;
    }
}

@media (max-width:1281px) {
    #myModal-modeladd {
        width: 70%;
    }
}

.field #projectAttrSearchInput {
    width: 350px!important;
}

#projectManageFormStatusModal {
    font-size: 13px;
    width: 90%;
}

.showSettingDiv{
    cursor: pointer;
}

.showSettingDiv:focus{
    outline: auto;
}

#formStatusContainer {
    margin-top: 10px;
    overflow: auto;
    min-height: 200px;
    width: 100%;
}

#formStatusContainer input, #formStatusContainer .select2-container {
    font-size: 12px;
}

#formStatusTable {
    width: 100%;
    margin-left: -1px;
}

#formStatusTable th, #formStatusTable td {
    border: 1px solid #D1D1D1;
    padding: 3px 5px;
    vertical-align: top;
}

#formStatusTable thead tr td {
    border: 0;
}

#formStatusTable thead tr:last-child {
    background-color: #ccc;
    font-weight: bold;
}

#formStatusTable tr td:first-child {
    border: none;
    padding: 0 2px 0 5px;
    vertical-align: middle;
    width: 10px;
    background-color: white;
}

#formStatusTable tr td:first-child+td {
    text-align: center;
}

#formStatusTable tr td.for-comment-review {
    text-align: center;
}

input.formStatusAssignTo {
    width: 99%!important;
    padding: 0!important;
}

div.formStatusAssignTo {
    max-height: 100%;
    margin-bottom: 0;
}

#formStatusUserSearch {
    width: 100%;
}

#formStatusOrgSearch {
    width: 100%;
}

#formStatusTable input {
    margin-bottom: 0;
}

.filterTd1, .filterTd2, .filterTd3 {
    display: none;
}

.removeFormStatusBtn>img {
    height: 12px;
    width: 8px;
}

#formStatusTable .deactivatedRow * {
    font-style: italic;
}

.abbreviationName {
    width: 58px;
}

#projectManageMailModal, #queryBuilderModal, #objectPropertiesModal {
    width: 80%;
    font-size: 13px;
}

#projectmanageRolesModal button.close, #projectManageMailModal button.close, #inviteUsersModal button.close {
    padding-right: 10px;
}

#manageRolesSection, .manageMailSection {
    margin-left: 0;
    min-height: 300px;
    background-color: var(--primary-white, #FFFFFF);
}

#manageRolesTableWrapper {
    overflow: auto;
    min-height: 100px;
    position: relative;
    width: 100%;
}

.cross-access-container {
    font-size: 13px;
}

.cross-access-container div {
    margin-left: 26px;
    margin-bottom: 10px;
    font-weight: bold;
}

.cross-access-container thead tr {
    background-color: #eee;
    font-weight: bold;
}

.manageRolesTable {
    width: 100%;
}

.manageRolesTable input, .manageRolesTable .select2-container {
    font-size: 12px;
}

.manageRolesTable input[type="checkbox"]:focus-visible{
    outline: auto;
}

.manageRolesTable thead tr+tr {
    background-color: #eee;
    font-weight: bold;
}

.manageRolesTable th, .manageRolesTable td {
    border: 1px solid #D1D1D1;
    border-top: none;
    padding: 3px 5px;
    vertical-align: top;
}

.manageRolesTable tr td:first-child {
    border: none;
    vertical-align: middle;
    width: 16px;
    background-color: white;
}

#manageRolesContainer .manageRolesTable .selectAllContainer {
    display: flex;
    flex-direction: row;
    gap: 5px;
}

#manageRolesContainer .manageRolesTable input.selectAllCheckbox {
    margin-top: 0px;
    margin-right: 8px;
    margin-left: 13px;
}

#adoddleAlertUI .modal-header .close{
    margin-top: -6px;
}

#manageRolesContainer .manageRolesTable input.selectAllCheckbox + label {
    cursor: pointer;
    margin-bottom: 0;
}

.manageRolesTable .roleNameCol {
    width: 220px;
}

input.manageRolesAssignTo {
    width: 99%!important;
    padding: 0!important;
}

div.manageRolesAssignTo {
    max-height: 100%;
    margin-bottom: 0;
    position: static;
    display: block;
    max-height: 280px;
}

.manageRolesAssignTo .pendingAcceptance {
    font-style: italic;
}

#manageRolesUserSearch {
    width: 100%;
    box-sizing: border-box;
    min-height: 33px !important;
    line-height: 30px;
    padding: 0px 5px;
}

.manageRolesTable td>input {
    margin-bottom: 0;
    min-height: 20px;
    border-color: transparent;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.manageRolesTable td>input:hover  {
    border-color: #ccc;
}

.manageRolesTable td>input:focus {
    border-color: rgba(82, 168, 236, 0.8);
    -webkit-box-shadow: 0 0 8px rgba(82, 168, 236, 0.6);
    -moz-box-shadow: 0 0 8px rgba(82, 168, 236, 0.6);
    box-shadow: 0 0 8px rgba(82, 168, 236, 0.6);
}

#manageRolesContainer label {
    font-size: 12px;
}

#manageRoleInstantEmailNotify {
    vertical-align: top;
    margin: 2px 0 0 0;
}

#manageRoleInstantEmailNotify:focus {
    outline: auto;
}

a.removeRoleBtn:focus {
    outline: auto;
}

.removeRoleBtn>img {
    height: 12px;
    width: 8px;
}

.flex {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
}

.popover-content, .popover-content * {
    font-size: 12px;
}

@media only screen and
/*!YUI Compressor */

(min-device-width: 768px) and
/*!YUI Compressor */

(max-device-width :1024px) {
    h2#formName {
        width: 320px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
    #viewFormHeaderTable .col1 {
        width: 30%!important;
    }
    #viewFormHeaderTable .intro-scheme .firstcol {
        width: 20%!important;
    }
    #viewFormHeaderTable .accordion-header .divth, #viewAssocCommsHeaderTable .accordion-header .divth, #viewAssocCommentHeaderTable .accordion-header .divth {
        background: none repeat scroll 0 0 rgba(0, 0, 0, 0);
        padding: 5px 0 0 5px;
    }
    .userImage {
        line-height: 0;
    }
}

#formAllHistorySection {
    z-index: 999;
}

@media only screen and
/*!YUI Compressor */

(max-width: 1920px) and
/*!YUI Compressor */

(orientation :landscape) {
    h2#formName {
        width: 320px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
    #viewFormHeaderTable .col1 {
        width: 28%!important;
    }
    #viewFormHeaderTable .intro-scheme .firstcol {
        width: 20%!important;
    }
    .assocCode {
        width: 75%;
    }
    #viewAssocCommsHeaderTable .intro-scheme .firstcol {
        width: 16%;
    }
    #viewAssocCommsHeaderTable .intro-scheme .secondcol {
        width: 33%;
    }
    #viewAssocCommsHeaderTable .intro-scheme .thirdcol {
        width: 30%;
    }
    #viewAssocCommsHeaderTable .intro-scheme .forthcol {
        width: 9%;
    }
}

.menuOpacity {
    opacity: .1;
    filter: Alpha(opacity=10);
    z-index: 1!important;
}

.ui-resizable-resizing>.ui-resizable-e, .ui-resizable-e:hover {
    background: #B4D0E0;
    cursor: col-resize!important;
}

.ui-resizable-n:hover {
    background: #B4D0E0;
}

.ui-resizable-e .parent-container {
    background: url("../images/icons/SplitterVertClose.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    height: 59px;
    position: relative;
    top: 45%;
    width: 10px;
    cursor: pointer;
}

.ui-resizable-e.dockable {
    background: #B4D0E0;
}

.ui-resizable-e {
    z-index: 5;
}

.file-viewer .ui-resizable-e, .viewer .ui-resizable-e {
    z-index: 5 !important;
}

#directoryDefaultContent .manage-folders-content, #directoryDefaultContent .nopadding-box-lower {
    height: 100% !important;
}

#directoryDefaultContent .ui-resizable-e.dockable {
    z-index: 1000!important;
}

#directoryDefaultContent #treeView {
    background-color: var(--primary-white, #FFFFFF);
}

#directoryDefaultContent #treeView>div.directory-treeitem {
    min-width: 0;
    float: none;
    width: auto;
}

.parent-container.dockable {
    background: url("../images/icons/SplitterVertOpen.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0);
}

#fileCommsSection .ui-resizable-e .parent-container>div, #modelCommsSection .ui-resizable-e .parent-container>div {
    background-color: #D3D3D3;
    border-radius: 100%;
    margin: 10px 0;
    width: 100%;
    height: 8px;
}

@media(max-width: 1024px) {
    #fileCommsSection .ui-resizable-e .parent-container>div, #modelCommsSection .ui-resizable-e .parent-container>div {
        height: 5px;
        margin-left: .5px;
    }
}

.modal.modal-overflow.fade.in {
    top: 50%;
    left: 50%;
}

.modal.modal-overflowX, .modal.modal-overflowX.fade.in {
    left: 1%!important;
    margin-left: 0!important;
}

.modal.modal-overflowY, .modal.modal-overflowY.fade.in {
    top: 1%!important;
    margin-top: 0!important;
}

#myModal-annoucement {
    width: 80%;
}

#myModal-annoucement .dynamic-div {
    height: 315px;
    overflow: auto;
}

#myModal-annoucement .static-bg {
    background: url("../images/announcement-bg.jpg") no-repeat scroll 0 0 / 100% auto rgba(0, 0, 0, 0);
    height: 315px;
}

@media only screen and
/*!YUI Compressor */

(min-device-width: 768px) and
/*!YUI Compressor */

(max-device-width :1024px) and
/*!YUI Compressor */

(orientation :portrait) {
    #myModal-upload {
        margin-left: -380px;
    }
    .dashboard-widget .span4 {
        width: 318px;
    }
    .modal-overflowX.modal, .modal-overflowX.modal.fade.in {
        top: 50%!important;
    }
}

#myModal-deactivateReactivateModal {
    width: 80%;
}

#deactivateReactivateModal_body {
    max-height: 450px;
    overflow: auto;
}

.ui-state-active {
    border: none;
    background: #EAF1FD;
    margin: 0px;
}

#assignAppsModal-add-left-margin{
    margin-left: 6px;
}

#assignAppsModal-add-right-margin{
    margin-right: 6px !important;
}

#assignAppsBox_fd_containText{
    margin-right: 12px !important;
    color: var(--dark-gray, #333333);
}

#addProjectSelectClientName span.icon-search, #addProjectSelectClientName span.icon-remove, #caddProjectSelectClientName span.icon-search, #caddProjectSelectClientName span.icon-remove {
    position: absolute;
    right: 10px;
    top: 6px;
    z-index: 1;
}

.inventorFileNote {
    color: red;
    font-size: 13px;
}

.italic-row, .italic {
    font-style: italic;
}

@media print {
    .reomveImg {
        display: none;
    }
    .divifctable {
        max-height: none;
    }
    .onlyPrint {
        visibility: visible!important;
    }
}

.onlyPrint {
    visibility: hidden;
}

.showmorelink {
    font-size: 14px;
}

.ui-dialog .ui-dialog-titlebar-close span {
    margin: -9px 0 0 -8px!important;
}

.ui-widget-header .ui-icon {
    background-image: url(images/ui-icons_222222_256x240.png)!important;
}

.ui-dialog.dialogForSaveMarkup {
    z-index: 100000;
    opacity: 1;
    border: none;
    font-size: 14px;
    -webkit-border-radius: 4px;
    -khtml-border-radius: 4px;
    -mox-border-radius: 4px;
    border-radius: 4px;
    padding: 0;
}

.ui-dialog.dialogForSaveMarkup .ui-dialog-titlebar {
    cursor: move;
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
    font-size: 14px;
    padding: 2px 11px 8px;
    background: var(--primary-color, #3569AE);
    height: 18px;
    font-weight: normal;
    color: var(--primary-white, #FFFFFF);
    border: none;
    border-radius: 4px 4px 0 0;
    -webkit-border-radius: 4px 4px 0 0;
    -khtml-border-radius: 4px 4px 0 0;
    -mox-border-radius: 4px 4px 0 0;
}

.ja_JP .ui-dialog.dialogForSaveMarkup .ui-dialog-titlebar {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

.ui-dialog.dialogForSaveMarkup .ui-dialog-titlebar button.ui-button {
    background: #c00;
    border: none;
    width: 20px!important;
    position: static;
    padding: 2px;
    margin: 1px 0 0!important;
    min-width: initial;
    float: right;
}

.ui-dialog.dialogForSaveMarkup #saveMarkUp-dialog {
    height: auto!important;
    overflow: visible;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
}

.ui-dialog.dialogForSaveMarkup .ui-dialog-buttonpane {
    border: 1px solid #ccc;
    border-radius: 0 0 4px 4px;
    -webkit-border-radius: 0 0 4px 4px;
    -khtml-border-radius: 0 0 4px 4px;
    -mox-border-radius: 0 0 4px 4px;
    margin: 0;
    padding: 10px 15px;
}

.ui-dialog.dialogForSaveMarkup .ui-dialog-buttonpane .ui-dialog-buttonset button {
    margin: 0;
}

.ui-dialog.dialogForSaveMarkup #markUpName {
    margin: 5px 0;
    width: 255px;
}

.ui-dialog.dialogForSaveMarkup button.ui-button .ui-icon {
    position: static;
    padding: 0;
    margin: 0!important;
}

.treeSelectTargetFolder {
    width: auto;
    min-height: 200px;
}

#selectTargetFolderModal {
    width: auto;
}

#selectTargetFolderModal #tree {
    width: 600px;
}

#selectTargetFolderModal #treeView {
    overflow-y: scroll;
}

#linkDocumentModal {
    width: 95%;
}

#linkDocumentModal .modal-body {
    overflow-y: auto;
}

#linkDocumentModal .modal-body * {
    font-size: 12px;
}

#linkDocumentModal .modal-body .apply-all-btn {
    font-size: 14px;
}

#linkDocumentModal .modal-body #selTargetFolderLinkBtn {
    font-size: 14px!important;
    padding: 4px 22px;
    margin-bottom: 3px;
}

.linkDocCommonFields {
    width: 100%;
}

#targetFolderPath {
    margin-right: 10px;
    text-overflow: ellipsis;
    width: auto;
    overflow: hidden;
    white-space: nowrap;
    font-weight: bold;
}

#linkDocumentModal .select2-search-choice, .linkDocCommonFields>input, .linkDocCommonFields>select {
    font-size: 12px;
}

.linkDocCommonFields>label {
    width: 100px;
    float: left;
}

#userToNotifyLinkDoc {
    width: 80%;
}

.linkDocListContainer {
    width: 100%;
    overflow: auto;
    margin: 0;
}

.linkDocListTable {
    min-width: 100%;
}

.linkDocListTable input, .linkDocListTable select {
    margin-bottom: 0;
}

.linkDocListTable tr {
    height: 39px;
    background: url("../images/icons/tr_repeat.png") repeat-x scroll center bottom transparent;
}

.linkDocListTable tr * {
    font-size: 12px;
    line-height: normal;
}

.linkDocListTable th {
    padding-left: 8px;
}

.linkDocListTable tbody td {
    vertical-align: top;
    padding-left: 8px;
}

.linkDocListTable input[type='text'], .linkDocListTable textarea {
    width: 150px;
}

.ld_issueNo, .ld_newRevNum {
    width: 30px!important;
}

.ld_poi, .ld_status {
    width: 130px;
}

.ld_errorCode, .mf_errorCode {
    color: red;
}

.linkDocContainer {
    margin-bottom: 10px;
}

#linkTypeSelect, .camparisionOpt, #criteria_always_status, .statusCriteriaSelect {
    width: 150px;
}

#criteria_always_status {
    vertical-align: top;
}

.dynamicLinkOptions select {
    margin-right: 5px;
}

.dynamicLinkOptions button {
    margin-left: 5px;
    margin-bottom: 5px;
}

.modal button {
    font-weight: normal;
    margin-top: 0;
}

#chkLinkSupersededRev {
    margin: 0 2px 0 20px;
}

.inlineBlock {
    display: inline-block;
}

.adoddleNote {
    color: red;
    font-size: 12px;
    padding-bottom: 2px;
}

.addIcon, .removeIcon {
    height: 24px;
    width: 24px;
    border-style: hidden;
    border: 0;
}

.addIcon {
    background: url("/images/icons/add_icon.png") no-repeat scroll center top transparent;
}

.removeIcon {
    background: url("/images/icons/remove_icon.png") no-repeat scroll center top transparent;
}

#adoddleTopMsgBox {
    position: fixed;
    top: 5px;
    margin: auto;
    max-width: 60%;
    display: block;
    z-index: 1100;
    padding-top: 10px;
    padding-bottom: 10px;
    font-weight: bold;
}

.alert .close {
    color: var(--primary-black, #000000);
    opacity: .2;
    filter: alpha(opacity=20);
}

.adoddleTopMsg * {
    margin: 0;
}

#basketContent .col-qtyinputwithbutton {
    width: 115px;
}

#basketContent .col-partImg-fixed-width div.tbody-data, #basketContent .col-settingIcon-fixed-width div.tbody-data, #assoc-prd-Content .col-partImg-fixed-width div.tbody-data, #assoc-prd-Content .col-settingIcon-fixed-width div.tbody-data {
    text-align: left;
    padding: 0;
}

#modal-associate-product {
    width: 90%;
    height: 600px;
}

#assocPrdselectbutton {
    float: left;
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
}

#basketContent .col-partNumber-fixed-width {
    width: 130px;
}

#basketContent .col-partImg-fixed-width {
    width: 60px;
    vertical-align: top;
}

#basketContent .col-totalprice {
    width: 120px;
}

#basketContent .col-costallocation {
    width: 165px;
}

#basketGrandTotal {
    float: left;
}

#basketContent .col-minOrderQty-fixed-width {
    width: 70px;
}

#basketContent .col-uomDescription-fixed-width {
    width: 60px;
}

#basketContent .col-unitPrice-fixed-width {
    width: 100px;
}

.markAsPrivateDiv>input {
    margin-left: 10px;
}

#fileCommsDetails {
    overflow: hidden;
    float: none;
    width: auto;
    margin-left: 0;
    display: none;
}

#fileViewer, #viewFormContain {
    display: block;
    position: relative;
    margin-left: 8px;
    width: auto;
    float: none;
    overflow: hidden;
}

#fileViewer .loading img {
    margin-left: -390px!important;
    margin-top: 297px!important;
    width: 60px!important;
}

#fileViewer .loading p {
    width: 398px;
}

#fileViewer .fullwidth-box-top h2 {
    width: 40%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
    color: var(--primary-white, #FFFFFF);
    font-weight: normal;
}

.viewer .fullwidth-box-top h2, .file-viewer .fullwidth-box-top h2 {
    color: var(--primary-white, #FFFFFF);
    font-weight: normal;
}

.file-viewer .manage-selects span {
    color: var(--primary-white, #FFFFFF);
}

.attch-file-viewer .manage-files .fullwidth-box-top {
    position: absolute;
    width: 100%;
    border-radius: 0px;
    z-index: 1;
}

.attch-file-viewer .nopadding-box-lower {
    border-top: 33px solid transparent;
    border-bottom: 23px solid transparent;
    box-sizing: border-box;
    position: relative;
    height: 100%;
}

.viewer .modal-header .btn.btn-default, .viewer .fullwidth-box-top .btn.btn-default {
    border-radius: 4px;
}

#modelCommsSection, #modelAllHistorySection {
    margin-right: 8px;
    margin-left: 0;
}

#modelViewer {
    width: auto;
    display: block;
    overflow: hidden;
    float: none;
    margin-left: 8px;
}

#modelViewer.invisible, #fileViewer.invisible {
    position: absolute;
    top: -999999px;
}

#s2id_inptDistTo2 {
    width: 84.3%!important;
}

.commentCreateSection .select2-container, #modalContainerFilesDist .divDistributeFilesFields {
    margin-bottom: 5px;
}

#modalPublishPlaceHolder {
    z-index: 1061!important;
}

#s2id_distTo {
    width: 81%!important;
}

.merge-panel .ui-dropdownchecklist-dropcontainer-wrapper, .attrcontainer .ui-dropdownchecklist-dropcontainer-wrapper {
    height: auto!important;
}

.merge-panel .ui-dropdownchecklist-dropcontainer-wrapper .ui-dropdownchecklist-dropcontainer, .attrcontainer .ui-dropdownchecklist-dropcontainer-wrapper .ui-dropdownchecklist-dropcontainer {
    height: auto!important;
}

.merge-panel .ui-dropdownchecklist-dropcontainer-wrapper .ui-dropdownchecklist-dropcontainer .ui-dropdownchecklist-text, .attrcontainer .ui-dropdownchecklist-dropcontainer-wrapper .ui-dropdownchecklist-dropcontainer .ui-dropdownchecklist-text {
    display: compact!important;
}

div.dragover {
    position: absolute;
    background: transparent;
    height: 100%;
    width: 100%;
    top: 0;
    display: none;
    z-index: 10000;
}

#divModalNotForDownloadFile {
    max-height: 600px;
    overflow: auto;
}

#divModalNotForDownloadFile>table, #modalLockForEditingDownload>table {
    width: 100%;
}

#divModalNotForDownloadFile th {
    text-align: left;
}

#modalLockForEditingDownload {
    max-height: 92%;
}

#divModalNotForDownloadFile {
    height: 100%;
    max-height: 80%;
    overflow: auto;
}

.areYouSureDownText {
    text-align: left;
    padding-left: 100px;
}

#divModalLockForEditingDownloadFile {
    height: 70%;
    max-height: 550px;
    overflow-x: hidden;
    overflow-y: auto;
}

.col-issueNo-fixed-width {
    width: 4%;
    text-align: center;
}

.col-revisionNum-fixed-width {
    width: 4%;
    text-align: center;
}

.col-publishDate-fixed-width {
    width: 8%;
}

.col-checkbox-fixed-width {
    width: 2%;
}

.col-attachmentImgName-fixed-width a img {
    left: -37%;
    position: relative;
}

.col-attachmentImgName-fixed-width a:not(img) {
    position: relative;
    left: 34%;
}

.col-commentImgName-fixed-width a img {
    left: -37%;
    position: relative;
}

.col-commentImgName-fixed-width a:not(img) {
    position: relative;
    left: 34%;
}

#selectMoveFileTargetFolderModal {
    width: auto;
}

#moveFolderModal .manage-folders-content, #selectTargetFolderModal .manage-folders-content, #selectMoveFileTargetFolderModal .manage-folders-content {
    border-top: none !important;
}

#selectMoveFileTargetFolderModal #tree {
    width: 600px;
}

#selectMoveFileTargetFolderModal #treeView {
    overflow-y: scroll;
}

div#fileAttributeDetails {
    clear: both;
    height: 24px;
    border-left: 1px solid #d1d1d1;
    border-right: 1px solid #d1d1d1;
    border-bottom: 1px solid #d1d1d1;
}

#moveFilesModal {
    width: 90%;
}

.file-current-drag .visual-img {
    display: block;
    height: 24px;
    width: 24px;
    background-size: cover;
    float: left;
    margin-left: 5px;
}

.file-current-drag .add-file {
    background: url("../images/icons/drop-file.png");
}

.file-current-drag .noaccess-file {
    background: url("../images/icons/no-access-drop-file.png");
}

.drag-file {
    width: 100%;
    height: 122px;
    text-align: center;
}

.drag-file span.logo {
    background: url("../images/icons/drag_file_icon.png");
    width: 52px;
    height: 52px;
    background-size: contain;
    display: inline-block;
    text-align: center;
}

.drag-file h2 {
    color: #B4B0B0;
    font-size: 20px;
    line-height: 0;
}

.drag-file h4, .drag-file h2 {
    color: #B4B0B0;
    font-size: 13px;
    line-height: 20px;
}

.file-current-drag.ui-widget-header {
    z-index: 10000;
    background: var(--primary-white, #FFFFFF);
    border: 1px solid;
    padding: 3px;
    border-color: lightgray;
    font-weight: normal;
}

#movefiletargetFolderPath {
    margin-right: 10px;
    text-overflow: ellipsis;
    width: auto;
    overflow: hidden;
    white-space: nowrap;
    font-weight: bold;
}

.moveFileCommonFields>label {
    width: 100px;
    display: inline-block;
}

.moveFileListContainer {
    width: 100%;
    overflow: auto;
    margin: 0;
}

.linkDocListTable {
    min-width: 100%;
}

.moveFileListTable tr {
    height: 39px;
    background: url("../images/icons/tr_repeat.png") repeat-x scroll center bottom transparent;
}

.moveFileListTable tr * {
    font-size: 12px;
    line-height: normal;
}

.moveFileListTable th {
    padding-left: 8px;
}

.moveFileListTable tbody td {
    vertical-align: top;
    padding-left: 8px;
}

#modalrenderPlaceholderForRev {
    width: 85%;
}

.batchInfoTable {
    min-height: 250px;
}

#modalFormsbatchforinfo, #myModal-batchforRead, #myModal-batchforinfo {
    min-height: 300px;
    width: 85%;
}

#modalDisplayDeReactivateAction {
    width: 95%;
}

#deReactivateActionTable {
    height: auto;
    min-height: 200px;
    overflow: auto;
}

#deReactivateActionTable.no-item {
    height: auto;
    max-height: 200px;
}

.moreoptions-commu {
    width: auto;
}

#relatedProductSection .disabled {
    opacity: .4;
}

#myModal-clearactiondisplay, #myModal-deactivateactiondisplay, #myModal-delegateactiondisplay, #myModal-formclearactiondisplay, #myModal-formdeactivateactiondisplay, #myModal-formdelegateactiondisplay {
    width: 91%;
}

.overduecolor {
    color: #c00;
}

.completecolor {
    color: #84B326;
}

.weekcolor {
    color: #E85D00;
}

#inventorFileExceptionIcon {
    bottom: 60px;
    right: 50px;
    position: absolute;
    display: none;
    z-index: 1040;
}

#productListingSection {
    width: 83.906%;
}

.pop-doc-info a.completeAction {
    float: left;
    width: 100%;
}

.col-qtyinputwithbutton {
    font-weight: bold;
}

#overWriteMergEdit {
    padding-top: 0;
}

.restrictViewResponse {
    color: red;
    font-weight: bold;
    font-size: 14px;
    margin: 10px;
    line-height: 1.5;
}

.select2-container-multi .select2-choices .select2-search-choice button {
    padding: 0 3px;
    line-height: 11px;
    margin-left: 5px;
    width: 16px;
    height: 14px;
    min-width: 16px;
    margin-right: 0;
    background: none;
}

.select2-container-multi .select2-choices .select2-search-choice span.caret {
    margin: -1px 0px 0px;
    cursor: pointer;
}

.popover-content {
    max-height: 200px;
    overflow: auto;
}

.modal-body .popover, #linkDocumentModal.modal .popover {
    z-index: 10100;
    max-width: 500px;
    width: 700px;
}

#viewCatalogModal .modal-body .popover {
    width: auto;
}

.modal-body #delegateActionForm .popover {
    width: 180px;
}

#myModal-actionforaction .modal-body .popover {
    max-width: 500px;
    width: auto;
}

#myModal-actionforcommentincorporation .modal-body .popover {
    max-width: 500px;
    width: auto;
}

#actionChk {
    margin-left: 10px;
}


.modal-body #formDistDelegateContent .popover {
    width: 170px;
    font-style: normal;
}

.modal-body #worksetsaddForm .popover {
    width: 250px;
}

.modal-body .popover .popover-content {
    padding: 9px 5px;
}

.dist-user-pic {
    width: 102px;
    height: 70px;
}

.user-detail-container {
    float: left;
    width: 66%;
    margin-left: 10px;
}

.user-personal-details {
    float: left;
    width: 100%;
    margin-bottom: 5px;
}

.dist-personal-details {
    float: left;
    width: 100%;
    margin-bottom: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    white-space: nowrap;
}

.org-personal-details {
    float: left;
    width: 100%;
    margin-bottom: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    white-space: nowrap;
}

.org-contact-details {
    float: left;
    width: 100%;
    margin-bottom: 5px;
}

span.user-name {
    float: left;
    width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    white-space: nowrap;
}

span.user-email-id {
    margin-left: 5px;
    width: 47%;
    overflow: hidden;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    float: right;
}

.user-org {
    width: 100%;
    overflow: hidden;
    float: left;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 5px;
}

.dist-users-list {
    width: 100%;
    float: left;
}

.dist-users-list th {
    text-align: left;
    font-size: 12px;
}

.dist-users-list td {
    font-size: 12px;
}

.dist-users-list span {
    margin-left: 10px;
}

.user-action-detail {
    width: 100%;
    float: left;
}

#myModal-download .modal-body .popover {
    max-width: 276px;
    z-index: 1010;
}

#myModal-EditFolder.modal #s2id_txtEditFolderSecurity {
    max-height: 95px;
}

#myModal-EditFolder.window-maximize #s2id_txtEditFolderSecurity {
    overflow: auto;
}

#myModal-EditFolder a:focus{
    outline: auto;
}

#myModal-batchforRead div.modal-body {
    height: auto;
    min-height: 200px;
}

div#formclearactiondetails, #formdeactivateactiondetails, #formdelegateactiondetails {
    max-height: 352px;
    overflow: auto;
}

.isDocAssociated {
    width: 15px!important;
    margin-left: 3px;
}

.filterCell>input {
    font-size: 12px;
}

.filterui .filter-layer-box-static {
    max-width: 25%;
    min-height: 30px;
}

.col-isDistributed-fixed-width>div {
    text-align: center;
}

#projectSeachInput {
    margin: 5px 5px 5px 15px;
    padding: 0 0 0 7px;
    font-size: 12px;
    height: 22px;
    width: 120px;
}

#projectStatusBasicFilter {
    left: 180px;
    top: 5px;
    position: absolute;
}

#selectExistingDocRefModal {
    width: 98%;
    min-height: 300px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0 !important;
    padding: 0 !important;
}

#selectExistingDocRefModal .modal-body {
    width: 100%;
    padding: 0px;
    position: relative;
}

#selectExistingDocRefModal .pagination.pagination-centered {
    margin: 12px 200px -43px;
}

#selectExistingDocRefModal div.myfluid-outer {
    border: none;
}

#selectExistingDocRefModal .fullwidth-box-top {
    position: relative;
    top: -2px;
}

#existingDocRef-blocks {
    max-height: 71vh;
    border: none;
}

.selectExistingDocRefIcon {
    cursor: pointer;
    margin-left: 10px;
    margin-right: auto;
}

#oldViewModel {
    width: auto;
}

.commentCreateSection {
    padding: 7px;
}

.modelBrowserHide {
    top: 105px;
    position: absolute;
    float: right;
    right: 20px;
    z-index: 100;
}

.navigator .modelBrowserHide {
    top: 5px;
}

.icon-down-modified {
    background-position: -313px -119px;
    margin-right: 5px;
}

.modelBrowserToggle {
    -webkit-transform: rotate(270deg);
    -moz-transform: rotate(270deg);
    -o-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    transform: rotate(270deg);
    position: absolute;
    z-index: 100;
    top: 165px;
    left: -55px;
    width: 140px;
    padding: 0;
    font-size: 12px;
}

.navigator .modelBrowserToggle {
    top: 65px;
}

#myModal-printFile {
    width: 90%;
}

#myModal-printFile .element {
    margin-right: 20px;
    float: left;
}

#myModal-printFile .element input {
    float: left;
    margin-right: 5px;
}

#myModal-printFile .element label {
    float: left;
}

#myModal-printFile .ui-icon-carat-1-s {
    background: none;
}

#myModal-printFile .resize {
    display: none;
}

#myModal-printFile .col-fileType-fixed-width {
    width: 40px;
    text-align: center;
}

#myModal-printFile .col-chkStatusImgName-fixed-width, #myModal-printFile .col-commentImgName-fixed-width, #myModal-printFile .col-attachmentImgName-fixed-width {
    width: 40px;
    left: 10px;
}

.deactivated-action, .cleared-action, .delegated-action {
    cursor: pointer;
}

.renderer {
    display: inline-block;
    color: white;
    margin-right: 7px;
    margin-top: 2px;
    font-size: 13px;
}

#renderer {
    float: left;
    margin: 2px 0 0 0;
    width: auto;
    height: 21px;
    padding: 0 0 0 5px;
    line-height: 18px;
    border-radius: 0;
    -webkit-border-radius: 0;
    -khtml-border-radius: 0;
    -mox-border-radius: 0;
    font-size: 12px;
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
    line-height: 16px;
    padding: 1px 5px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    color: var(--primary-white, #FFFFFF);
    margin-top: 2px;
    background: var(--primary-color, #3569AE);
    border: 1px solid var(--primary-white, #FFFFFF);
}

.ja_JP #renderer {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

#renderer-tooltip {
    width: 23px;
    margin: 0 5px;
}

.addProjectLoadingDiv {
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.4);
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    text-align: center;
    z-index: 10001;
}

.hideProjectLoadingDiv {
    display: none;
}

.hilightDiv-mouseup {
    position: absolute;
    z-index: 9999;
}

.hilightDiv-mousedown {
    position: absolute;
    z-index: 99999;
}

#feedback-Modal {
    background-color: gray;
    position: absolute;
    width: 530px;
    height: auto;
    z-index: 10000;
    margin-left: 0;
    top: 75px;
    left: 100px;
}

.highlightLayerDisplay {
    top: 0;
    left: 0;
    position: absolute;
    z-index: 9000;
    background: rgba(0, 0, 0, 0);
    display: block;
    width: 100%;
    height: 100%;
}

.highlightLayerHide {
    display: none;
}

.feedbackHeaderButtons {
    cursor: pointer;
    float: left;
}

#feedbackDescription {
    width: 97%;
}

.feedbackRestoreMinimize {
    float: left;
    margin-right: 5px;
}

.feedbackRestoreMinimize>img {
    margin-top: 2px;
}

.feebbackClose {
    margin: 4px 0 0 5px;
}

.feedbackButtons {
    margin-right: 5px;
}

.feedbackRow {
    margin-bottom: 5px;
}

#feedbackDivBody span {
    margin-left: 5px;
    display: inline-block;
}

.feedbackBanner {
    width: 200px;
    float: left;
}

.feedbackHeaderButtons {
    float: left;
}

.feedback-open {
    display: block;
    top: 75px;
    left: 25 px;
}

.feedback-close {
    display: none;
}

.feedbackBanner>h3 {
    display: inline-block;
    line-height: 25px;
    margin-left: 5px;
}

#moveFolderModal.modal {
    width: 670px;
}

#resizeMoveFolderDiv {
    background: #f6f6f6;
    margin: 0 10px 0 0;
    border: 1px solid #d1d1d1;
    border-top: none;
    padding: 0;
    -webkit-border-radius: 4px 4px 4px 4px;
    -moz-border-radius: 4px 4px 4px 4px;
    border-radius: 4px 4px 4px 4px;
    float: left;
    width: 100%;
}

.fileAttrSpan::before {
    content: " ";
    margin-right: 5px;
}

.customExpand {
    display: block;
    background: lightgray;
}

.customCollapsed {
    display: none;
}

.customTriggerWrapper {
    margin-right: 8px;
    margin-top: 2px;
    float: right;
}

a#customAttrSectionTrigger {
    border: 1px solid #ccc;
    border-bottom: none;
    padding: 0 12px;
    position: relative;
    display: block;
}

a#customAttrSectionTrigger .selected {
    z-index: 7;
}

#fileAttributeDetails hr {
    height: 1px;
    background: black;
    margin: -8px 0 0 0;
    position: absolute;
    width: 100%;
}

.customAttrExpanded {
    background: lightgray;
    z-index: 7;
}

.customAttrCollapsed {
    background: white;
}

.modal-body.customExpand {
    border-top: none;
    position: absolute;
    top: 55px;
    border: 1px solid #ccc;
}

#project-dist-groups {
    width: 90%;
    font-size: 12px;
}

#manageDistributionGroupContainer {
    overflow-x: hidden;
    overflow-y: auto;
    min-height: 400px;
    width: 100%;
}

.manageDistGroupHeaderDiv {
    background-color: var(--primary-white, #FFFFFF);
    height: 24px;
}

.distributionGroupTxtInput {
    width: 30%;
}

.distributionGroupTypeDD {
    width: 100px;
}

#searchDistributionGroupsName {
    width: 8%;
}

#searchDistGroupTypeDD {
    width: 6%;
}

#searchDistributionGroupFormType {
    width: 18%;
}

#searchDistributionGroupsUserList {
    width: 25%;
}

#searchDistributionGroupsAllList {
    width: 25%;
}

.distributionGroupName {
    width: 120px;
}

.removeDistributionGroupBtn {
    padding-left: 10px;
}

.dist-search {
    font-size: 12px;
    border: 1px solid #ccc;
    padding: 10px 0 0 10px;
}

.dist-search input {
    font-size: 12px;
}

.dist-search span {
    border-left: 1px solid #ccc;
    padding: 3% 0 .3% 0;
    vertical-align: bottom;
    margin: 0 1% 0 1%;
}

.dist-content * {
    font-size: 12px;
}

.dist-content .input-disabled {
    background-color: var(--disable-color, #E0E0E0);
    cursor: not-allowed;
    pointer-events: none;
}

.dist-content .dist-group-container {
    border: 1px solid var(--primary-hover-gray, #F1F2F5);
    margin-bottom: 10px;
}

.dist-group-container .dist-group-header {
    background-color: var(--primary-hover-gray, #F1F2F5);
    line-height: 34px;
}

.dist-group-container .dist-group-header .groupType label{
    line-height: inherit;
    margin-bottom: 0;
}

.dist-group-container .dist-input-name {
    width: 45%;
    padding-bottom: 4px;
}

.dist-group-container .dist-group-content {
    background: #FAFAFA;
    padding: 10px 0 10px 10px;
}

.dist-group-container .form-distribution-list.not-allow-task-selection .select2-search-choice-distribution-list .dropdown-toggle {
    display: none !important;
}

.dist-group-container .form-distribution-list.not-allow-task-selection .select2-search-choice-distribution-list div {
    cursor: default;
}

#distContainer .select2-container {
    max-height: none;
}

#distContainer .filterApply {
    display: none!important;
}

#distContainer .removeFilter {
    color: #c00;
}

.dist-group-container .content-width {
    float: left;
    width: 7%;
    padding-left: 1%;
    clear: both;
}

.dist-group-container .groupName {
    width: 40%;
    line-height: 30px;
    padding-left: 10px;
}

.dist-group-container .groupName input {
    margin-left: 5px;
    margin-top: 1px;
    font-size: 12px;
}

.dist-group-header .padding-left10 {
    padding-left: 10px;
}

.edit-group .groupType .padding-left5 {
    padding-left: 5px;
}

.add-group .groupType select {
    margin-left: 5px;
}

.add-group .groupFromType {
    width: 36%;
}

.dist-row .formTypeList, .dist-row .distList, .dist-row .securityList {
    width: 91%;
    padding: 0;
    display: inline-block;
    margin-left: 5px;
}

.dist-group-header .chk-group {
    margin-bottom: 10px;
}

.dist-group-header strong {
    vertical-align: sub;
}

.dist-group-header input {
    height: 15px;
    margin-bottom: 2px;
}

.dist-group-header select {
    height: 30px;
    margin-bottom: 2px;
}

.dist-row .group-position {
    position: relative;
}

.dist-group-content .distributionGroupTxtInput {
    width: 100%;
    margin: 0;
}

p.dist-group-hint, p.dist-bulkApply-hint {
    margin-top: 5px;
    margin-bottom: 5px;
    font-size: 14px;
    padding: 5px 10px;
    color: #C70704;
}

#dist-groups-BulkApply {
    width: 75%;
}

#user-info-table-body {
    max-width: auto;
}

#dist-groups-BulkApply .bulk-apply-list {
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 10px;
}

.bulk-user-details .user-list {
    width: 28%;
    float: left;
}

.user-list select#selected-user-list {
    width: 100%;
    height: 200px;
}

.bulk-user-details .user-arrows {
    float: left;
    width: 4%;
    padding-top: 6%;
}

.bulk-user-details .requiredAction {
    border-color: #c00;
}

.bulk-user-details .user-details {
    float: left;
    padding-left: 10px;
    height: auto;
    overflow: hidden;
}

.bulk-user-details .dist-list {
    width: 65%;
}

.bulk-user-details .security-list {
    width: 50%;
}

#formbulkapply .mbottom0 {
    margin-bottom: 0;
}

.user-details table.user-table {
    font-size: 12px;
}

.dist-list table.user-table {
    width: 100%;
}

.security-list table.user-table {
    width: 100%;
}

.user-details table.user-table tr td {
    padding: .4%;
}

#user-info-table thead tr {
    background-color: #ccc!important;
    font-weight: bold;
    width: 100%;
    height: 20px;
    padding: 0;
    background: none;
}

.user-details .user-table td select {
    width: 100%;
}

.user-table td.input-chk-td .chk-type, .user-table td.input-chk-td .checkallbulkApply {
    margin-left: 5px;
}

.user-table input {
    font-size: 12px;
}

.user-table .input-td input {
    width: 90%;
    min-height: 20px;
}

.user-table .input-remove-td input {
    width: 92%;
    min-height: 20px;
}

.dist-list .select-div-bulkicon, .security-list .select-div-bulkicon {
    margin-left: 5px;
    display: inline-block;
}

.dist-list thead .select-div-bulkicon .icon-chevron-down, .security-list thead .select-div-bulkicon .icon-chevron-down {
    vertical-align: bottom;
    cursor: pointer;
}

.user-details .user-table-body {
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
    border: 1px solid lightgray;
}

.user-table tbody#user-info-details {
    width: 100%;
}

.user-details .user-table thead {
    width: 100%;
}

.bulk-removeuser {
    line-height: 32px;
}

#user-info-body {
    max-width: none;
}

.security-list td.input-td {
    width: 68%;
}

.security-list td.select-td .select-div-acton {
    float: left;
    width: 75%;
}

.security-list td.input-chk-td {
    width: 7%;
    text-align: center;
}

@media(min-width: 1450px) {
    .edit-group .groupType, .add-group .groupType {
        width: 53%;
    }
    .dist-list td.input-chk-td {
        width: 50px;
        text-align: center;
    }
    .dist-list td.input-td {
        width: 500px;
    }
    .dist-list td.input-remove-td, .security-list td.input-remove-td {
        width: 800px;
    }
    .dist-list td.select-td {
        width: 225px;
    }
    .dist-list td.select-td-days {
        width: 150px;
    }
    .dist-list td.select-td-days .select-div-days {
        width: 107px;
        float: left;
    }
    .dist-list td.select-td .select-div-acton {
        float: left;
        width: 180px;
    }
}

@media(max-width: 1450px) and
/*!YUI Compressor */

(min-width:1351px) {
    .edit-group .groupType, .add-group .groupType {
        width: 53%;
    }
    .dist-list td.input-chk-td {
        width: 50px;
        text-align: center;
    }
    .dist-list td.input-td {
        width: 490px;
    }
    .security-list td.input-td {
        width: 63%;
    }
    .dist-list td.input-remove-td, .security-list td.input-remove-td {
        width: 500px;
    }
    .dist-list td.select-td {
        width: 225px;
    }
    .dist-list td.select-td-days {
        width: 165px;
    }
    .dist-list td.select-td-days .select-div-days {
        width: 107px;
        float: left;
    }
    .dist-list td.select-td .select-div-acton {
        float: left;
        width: 170px;
    }
}

@media(max-width: 1350px) and
/*!YUI Compressor */

(min-width:1023px) {
    .edit-group .groupType, .add-group .groupType {
        width: 50%;
    }
    .dist-search span {
        padding: 5% 0 .4% 0;
    }
    #searchDistributionGroupFormType {
        width: 15%;
    }
    .dist-list td.input-chk-td {
        width: 50px;
        text-align: center;
    }
    .dist-list td.input-td {
        width: 500px;
    }
    .dist-list td.select-td {
        width: 265px;
    }
    .security-list td.input-td {
        width: 58%;
    }
    .user-table .input-remove-td input {
        width: 91%;
    }
    .dist-list td.input-remove-td, .security-list td.input-remove-td {
        width: 500px;
    }
    .dist-list td.select-td-days {
        width: 185px;
    }
    .dist-list td.select-td-days .select-div-days {
        width: 110px;
        float: left;
    }
    .dist-list td.select-td .select-div-acton {
        float: left;
        width: 180px;
    }
}

@media(max-width: 1024px) and
/*!YUI Compressor */

(min-width:700px) {
    .edit-group .groupType, .add-group .groupType {
        width: 50%;
    }
    #searchDistributionGroupFormType {
        width: 15%;
    }
    .dist-list td.input-chk-td {
        width: 50px;
        text-align: center;
    }
    .dist-list td.input-td {
        width: 435px;
    }
    .dist-list td.select-td {
        width: 220px;
    }
    .security-list td.input-td {
        width: 54%;
    }
    .user-table .input-remove-td input {
        width: 91%;
    }
    .dist-list td.input-remove-td, .security-list td.input-remove-td {
        width: 500px;
    }
    .dist-list td.select-td-days {
        width: 190px;
    }
    .dist-list td.select-td-days .select-div-days {
        width: 107px;
        float: left;
    }
    .dist-list td.select-td .select-div-acton {
        float: left;
        width: 130px;
    }
}

#adminOptionListDiv {
    min-height: 480px;
}

.adminOptionPage .boxgrid {
    border: 1px solid #ddd;
    margin: 15px 0 15px 15px;
    -webkit-box-shadow: 0 0 0 var(--primary-white, #FFFFFF);
    -moz-box-shadow: 0 0 0 var(--primary-white, #FFFFFF);
    box-shadow: 0 0 0 var(--primary-white, #FFFFFF);
    background-color: var(--primary-white, #FFFFFF);
    cursor: pointer;
}

.adminOptionPage .boxgrid:hover {
    background-color: #f5f5f5;
}

.adminTabThumbImg {
    object-fit: contain;
    width: 175px;
    height: 150px;
}

.admin-box-templateName {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-bottom: 8px;
    min-height: 20px;
}

.fetchAttributeHeaderDiv, .directoryCatalogHeaderDiv {
    height: 24px;
}

#modalCreateOrEditAttrRule.modal, #modalFetchAttributeHistory.modal {
    width: 95%;
}

#adminSelectAttrDiv {
    width: 48%;
    height: 400px;
    float: left;
}

#adminTargetFolderDiv {
    width: 45%;
    height: 400px;
    float: right;
}

#adminTargetFolderDiv #fetchAttrRuleTreedialog {
    border-top: 0px !important;
    height: 96%;
}

#draggablePanelList {
    margin: 0;
}

.formContentStyle {
    border-style: solid;
    border-width: 1px;
    border-color: #CCC;
    padding: 0 10px 10px 10px;
}

.formContentStyleSpan {
    font-size: 13px;
}

.formContentStyle h5 {
    position: relative;
    top: -10px;
    min-width: 100px;
    background-color: white;
    width: 100px;
    text-align: center;
    font-weight: normal;
    font-size: 14px;
    padding: 0 5px 0 5px;
    margin: 0;
}

.adminAvailableAttrList {
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
    float: left;
    height: 340px!important;
    width: 40%;
}

.ja_JP .adminAvailableAttrList {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

#adminAvailableAttrList option {
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
    color: var(--primary-black, #000000);
    display: block;
    float: left;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ja_JP #adminAvailableAttrList option {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

#selectedFetchAttContent {
    width: 45%;
    height: 337px;
    overflow: auto;
}

.attrmoveItemDiv {
    margin-top: 10px;
    float: left;
    height: 100px;
    padding-top: 145px;
    padding-right: 8px;
}

.adminFetchRuleBorderContent {
    border: 1px solid var(--primary-color, #3569AE);
    font-size: 12px;
    line-height: 16px;
    margin: 0 0 0 5px;
    padding: 1px 5px;
    height: auto;
    background: var(--primary-white, #FFFFFF);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    color: var(--primary-white, #FFFFFF);
    border-radius: 4px;
    overflow: auto;
}

#fetchAttrRuleTreeListing {
    overflow: hidden;
    height: 100%;
}

#adminRuleTitleInput {
    margin-right: 40px;
}

#radioAdminRuleActiveStatus, #radioAdminRuleDeActiveStatus {
    width: 20px;
    height: 20px;
    margin-left: 20px;
}

#fetchAttrRuleTreeListing .treeInfo {
    overflow-x: hidden;
}

.admin-selectAttr-li {
    float: left;
    width: 100%;
    height: 30px;
    padding: 3px 0 3px 0;
    line-height: 0;
    background-color: transparent!important;
    background: url(../images/icons/tr_repeat.png) bottom repeat-x;
}

.admin-selectAttr-dragIcon {
    float: left;
    width: 7px;
    background-color: #CCC;
    height: 22px;
    margin: 5px 5px 0 0;
    cursor: move;
}

.admin-selectAttr-name {
    float: left;
    width: 85%;
    height: 15px;
    margin: 8px 5px 0 0;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1;
    color: var(--primary-black, #000000);
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.admin-selectAttr-ListItem {
    cursor: move;
}

.admin-selectAttr-separatorSelect {
    float: left;
    width: 65px;
    height: 35px;
    margin: 5px 5px 5px 0;
}

.admin-selectAttr-removeIcon {
    float: left;
    margin: 5px 0 0 5px;
    cursor: pointer;
}

.fetchRuleHistoryTxt {
    width: 95%;
    font-size: 12px;
    text-shadow: none;
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
    line-height: 22px;
    color: #333;
}

.ja_JP .fetchRuleHistoryTxt {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

#fetchAttributeHistoryMainContent {
    overflow: auto;
}

#fetchAttributeHistoryMainContent .historyRow {
    cursor: pointer;
}

.layercheckbox {
    background-image: url('../images/contextmenuImg/checkbox-new.png');
}

.layercheckbox-red {
    background-image: url('../images/contextmenuImg/checkbox-red-new.png');
}

.layercheckbox label {
    padding-left: 20px;
    height: 15px;
    display: inline-block;
    background-repeat: no-repeat;
    background-position: 0 0;
    font-size: 15px;
    vertical-align: middle;
    cursor: pointer;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

input[type=checkbox].css-checkbox-new {
    overflow: hidden;
    clip: rect(0 0 0 0);
}

input[type=checkbox].css-checkbox-new+label.checkbox-label, input[type=checkbox].css-checkbox-new+label.checkbox-label-advance {
    padding-left: 20px;
    height: 15px;
    display: inline-block;
    line-height: 15px;
    background-repeat: no-repeat;
    background-position: 0 0;
    font-size: 15px;
    vertical-align: middle;
    cursor: pointer;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

input[type=checkbox].css-checkbox-new:checked+label.checkbox-label {
    background-position: 0 -15px;
}

input[type=checkbox].css-checkbox-new:checked+label.checkbox-label-advance {
    background-position: 0 -30px;
}

.selectAttrHeaderh5 {
    width: 140px!important;
    z-index: 10;
}

.formContentSepratorSpan {
    padding: 7px 5px 0 48%;
    float: left;
}

.fetchAttrAargetFolderh5 {
    width: 125px;
}

.adminSelectedAttrMainContent {
    padding-top: 15px;
}

.fileViewerAttrSpan {
    display: inline-block;
    min-width: 70px;
    font-size: 12px;
    text-align: center;
    line-height: 19px;
}

#myModal-new-dashboard .control-label {
    text-align: left;
}

.dropdown-menu.dashboard-dropdown {
    z-index: 10000;
    left: auto;
    right: 0;
    max-height: 400px;
    overflow-y: auto;
    width: 190px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    padding: 0;
    border: 0;
    margin-top: 7px;
}

.dropdown-menu.dashboard-dropdown > li{
    padding: 0;
}

.dropdown-menu.dashboard-dropdown > li > a {
    padding: 10px;
    font-size: 14px !important;
}

.widget-edit .control-label {
    float: left;
    padding-top: 5px;
}

.widget-edit .controls {
    margin-left: 180px;
}

.widget-edit .control-group {
    margin-bottom: 10px;
}

#playground .ui-widget-content {
    border: none;
    color: var(--primary-black, #000000);
}

.report-proress-dialog {
    width: 400px;
    z-index: 99;
    position: fixed;
    top: 50%;
    left: 50%;
    margin: -80px 0 0 -200px;
    -webkit-box-shadow: 1px 1px 10px #ccc;
    -moz-box-shadow: 1px 1px 10px #ccc;
    box-shadow: 1px 1px 10px #ccc;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    display: none;
}

.report-proress-dialog.minimize {
    top: auto!important;
    left: auto!important;
    right: 45px;
    bottom: 0;
}

.report-proress-dialog .m-header {
    color: var(--primary-white, #FFFFFF);
    border-radius: 5px 5px 0 0;
    padding: 5px 10px;
    background: none repeat scroll 0 0 var(--primary-color, #3569AE);
}

.report-proress-dialog.ui-draggable .m-header {
    cursor: move;
}

.report-proress-dialog.ui-draggable.minimize .m-header {
    cursor: default;
}

.report-proress-dialog .m-header .min-max-btn {
    float: right;
    margin: 5px 10px 0 0;
    cursor: pointer;
}

.report-proress-dialog .m-header h3 {
    margin: 0;
    font-size: 15px;
    font-weight: normal;
    line-height: 22px;
}

.report-proress-dialog .m-footer {
    border: 1px solid #ccc;
    border-top: none;
    text-align: center;
    padding: 35px 0;
    background: var(--primary-white, #FFFFFF);
    border-radius: 0 0 5px 5px;
}

.report-proress-dialog.minimize .m-footer {
    display: none!important;
}

#generateLegacyReportModal .modal-footer {
    padding-top: 50px;
    padding-bottom: 50px;
    text-align: center;
}

#header_adoddleCor .criteria-wrap.smallMaxWidth {
    max-width: 100px;
    min-width: 80px!important;
}

.roundedButtonListHeader {
    padding: 6px 10px;
    background: #f3f3f3;
    font-weight: bolder;
}

@-moz-document url-prefix() {
    #header_userlogin+ul {
        left: -84px!important;
    }
}

.resportHelpDeskShow {
    display: block;
}

.resportHelpDeskHide {
    display: none;
}

.ja_JP .jqplot-pie-series.jqplot-data-label {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

.inline {
    display: inline-block;
}

.vmiddle {
    vertical-align: middle;
}

.tright {
    text-align: right;
}

.margin-left10 {
    margin-left: 10px;
}

.margin-right10 {
    margin-right: 10px;
}

.margin-left5 {
    margin-left: 5px;
}

.margin-right5 {
    margin-right: 5px;
}

[class*=" icon-"].dashboard-nav-icon {
    margin-top: 0;
}

#dashboar-title {
    font-size: 14px;
    color: var(--primary-black);
    font-weight: bold;
    text-align: left;
    /*line-height: 30px;*/
}

.dropdown-menu>li>a:hover>[class*=" icon-"].dashboard-nav-icon {
    background-image: url("/images/glyphicons-halflings.png");
}

.dropdown-menu>li>a:hover {
    color: #333;
}

.dashboard-nav .dropdown-menu>li>a {
    padding: 0;
}

.header-fixed {
    position: fixed;
    top: 0;
    display: none;
    background-color: white;
}

.fixed {
    top: 0;
    position: fixed;
    width: auto;
    display: none;
    border: none;
}

.table.fix-header th {
    max-width: none;
}

.modelLoader {
    position: absolute;
    z-index: 10;
    right: 10px;
    top: 10px;
    padding-right: 20px;
}

.modelLoader img {
    vertical-align: text-bottom;
    margin-right: 10px;
}

#model-widget-body .modal-footer {
    padding-left: 0;
    padding-right: 0;
}

#viewModels .popover {
    max-width: 443px;
    width: 443px;
}

.absolute {
    position: absolute;
}

.relative {
    position: relative;
}

.padding5 {
    padding: 5px;
}

.widget-add-container {
    top: 0;
    right: 0;
    line-height: 0;
    background-color: var(--primary-color, #3569AE);
    cursor: pointer;
}

#dashboard-options .dashboard-nav-icon, #dashboard-options .icon-icon {
    margin-top: 3px;
}

.switch-text {
    max-width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    margin-right: 20px;
    /* font-size: 12px; */
}

#legacyExportType, #ExportType, #ExportTypeDiscussions, #ExportTypeGlobal, #contactExportType {
    width: 175px;
    font-size: 12px;
    padding: 0 5px;
    margin: 0;
    height: 25px;
    margin-left: 4px;
}

#ExportTypeDiscussions {
    margin-left: 4px;
    float: left;
}

.activeExport {
    background: url('/images/icons/exportIconActive.png');
    background-size: 23px 24px;
    background-repeat: no-repeat;
}

.deactiveExport {
    background: url('/images/icons/exportIconDeactive.png');
    background-size: 23px 24px;
    background-repeat: no-repeat;
}

.error-msg {
    color: #C00;
}

.cell {
    display: table-cell;
}

.table {
    display: table;
}

.table.invisible {
    display: none;
}

.cell, .table {
    height: 100%;
}

.full-textbox, .cell, .table {
    width: 100%;
}

.full-textbox {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
}

#releaseResponseModal {
    width: auto;
}

#sltMsgStatusViewMsg {
    margin: 0;
    width: 130px;
}

#formFilterProjecttab {
    float: left;
}

#releaseResponseFormViewMsg label, #releaseResponseFormViewMsg select, #releaseResponseFormViewMsg button {
    display: inline;
    margin-right: 5px;
}

.circleShape {
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    border-radius: 50%;
}

.msgHeld, .msgPending {
    width: 12px;
    height: 12px;
    margin: 8px;
}

.msgHeld {
    background: red;
}

.msgPending {
    background: #FFBF00;
}

#actiondisplay {
    position: absolute;
    top: 0;
    margin-left: 0;
    left: 0;
}

.distribution-group:after {
    content: ':';
    display: inline;
    margin-left: 5px;
}

.groupTitle {
    margin-left: 5px;
}

.dist-users-list tbody>tr>td {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

#myModal-upload .control-label {
    padding: 5px 0 0 0;
    text-align: right;
}

#myModal-upload .projectform>form {
    padding-top: 10px;
}

#myModal-upload.adjust-layout {
    top: 20px;
    margin-top: 0!important;
}

.labelText {
    font-size: 13px;
    margin-left: 10px;
}

.list-label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#playground {
    float: left;
    width: 100%;
    position: relative;
    height: 100%;
    box-sizing: border-box;
    z-index: 6;
}

#new-mail {
    width: auto;
}

input[type=checkbox].checkProjectFolder, input[type=checkbox].checkSubFolder {
    display: none;
}

.noteForMailBox {
    font-size: 12px;
}

#createOrEditDocMail .modal-body {
    height: 605px;
    overflow: auto;
}

#viewModels h2 {
    text-rendering: auto;
}

#createOrEditDocMail .popover, #createOrEditFormMail .popover {
    width: auto;
}

#selectFolderManageMailModal #resizeDiv, #selectFolderManageAppsModal #resizeDiv {
    height: 400px;
    overflow: auto;
}

#selectFolderManageMailModal h2, #selectFolderManageAppsModal h2 {
    font-size: 15px;
    line-height: 18px;
    word-break: break-word;
    word-wrap: break-word;
}

#optionsmore-assign-apps {
    background: url(../images/admin/admin_apps.png) 0 0 no-repeat;
}

#optionsmore-add-app {
    background: url(../images/admin/admin_apps.png) 0 0 no-repeat;
}

#optionsmore-manage-app-settings {
    background: url(../images/admin/admin_apps_2.png) 0 0 no-repeat;
}

#optionsmore-manage-mailbox {
    background: url(../images/admin/admin_manage_mail.png) 0 0 no-repeat;
}

#selectedFolderPath {
    padding: 5px;
    float: left;
    margin: 0 5px;
}

#manageAppsModal {
    width: 80%;
}

#assignAppsModal {
    width: 60%;
}

#assignAppsModal .formGroup {
    background: none;
    background-color: #ddd!important;
}

#assignAppsModal .chkBox, #assignAppsModal .downloadTemplate {
    width: 30px;
}

#assignAppsModal .templateTypeTh {
    width: 110px;
}

#assignAppsModal .formGroupChkAll:focus,
#assignAppsModal .templateType:focus,
#assignAppsModal .downloadTemplete:focus {
    outline: auto;
}

div#tblbodyAttributesSection div.bulk-apply-trigger-container {
    display: inline-block;
}

#content {
    width: 100%;
    height: 100%;
    border: 0;
    margin: 0;
    padding: 0;
}

#content.fileViewer {
    position: relative;
}

#content>.loading {
    z-index: 5002;
}

#hoopsWrapper {
    height: 78%;
    padding-top: 75px;
    box-sizing: border-box;
    position: absolute;
    right: 0;
    width: 78%;
    top: 0;
}

#content.fileViewer #ribbon .section .buttonGroup .verticalButton.wireframeShaded {
    font-size: 9px;
}

.advanceViewer #hoopsWrapper, .modelViewerIpad #hoopsWrapper, #modelViewTest.navigator #modelBrowserWindow, #modelViewTest.navigator #hoopsWrapper {
    padding-top: 0;
}

#screen {
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
    font-size: 13px;
    height: 100%;
    position: relative;
}

.ja_JP #screen {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

.advanceViewer #screen, .advanceViewer #hoopsWrapper, .modelViewerIpad #hoopsWrapper {
    width: 100%!important;
}

.fileViewer #hoopsWrapper, .modelViewerIpad #hoopsWrapper, #screen form[name="DocumentView"] {
    height: 100%;
}

#viewsManager {
    border-left: none;
    border-bottom: none;
    bottom: 0;
    height: 22%;
    position: absolute;
    width: 100%;
    border-top: 1px solid #ccc;
    top: auto!important;
    z-index: 9999;
}

#viewsManager #inventorFileExceptionIcon {
    bottom: auto;
    top: 7px;
    right: 10px;
}

#viewsManager .view-container {
    height: 100%;
    position: relative;
    width: 100%;
}

#viewsManager .view-container table {
    margin: 0 7px;
}

#viewsManager .view-container #project-blocks {
    padding: 0;
    position: relative;
    float: none;
}

#viewsManager .view-container td {
    margin: 0;
    border-radius: 0;
    float: none;
    padding: 15px 7px 0;
    border: none;
    cursor: pointer;
}

#viewsManager .view-container td.disabled {
    cursor: default;
    opacity: .3;
}

#viewsManager .view-container .add-new>img {
    height: 25px;
    width: 29px;
    padding: 17px;
    border: 1px solid #999;
    border-radius: 3px;
}

#viewsManager .view-container #project-blocks .update-view {
    margin-left: 0;
    z-index: 10;
    background: white;
    width: auto;
    top: 5px;
    padding: 5px;
    border: 1px solid #aaa;
    font-size: small;
    text-align: left;
    min-width: 308px;
}

#viewsManager .view-container #project-blocks .update-view .btn {
    padding: 2px;
    margin-bottom: 0;
}

#viewsManager .view-container #project-blocks .update-view input[type="text"] {
    width: 160px;
    padding: 3px;
    margin-bottom: 0;
    margin-left: 5px;
}

#viewsManager .view-container #project-blocks .update-view #updateClose.btn {
    width: 30px;
    min-width: 25px;
    margin-left: 5px;
}

#viewsManager .view-container #project-blocks .update-view .detailWrapper {
    margin: 5px 0;
}

#viewsManager .view-container #project-blocks .update-view #setModelImg, #viewsManager .view-container #project-blocks .update-view #downloadImg {
    width: 48.5%;
}

#viewsManager .view-container #project-blocks .update-view img {
    margin-right: 10px;
}

#viewsManager .view-container #project-blocks .modelImages, #viewsManager .view-container #project-blocks .update-view img {
    height: 61px;
    width: 100px;
}

#viewsManager ul.nav-tabs {
    border-bottom-color: #ccc;
}

#viewsManager ul li a {
    padding-top: 0;
    padding-bottom: 0;
    font-size: 12px;
    line-height: 18px;
}

#viewsManager ul li.active a {
    border-color: #ccc;
    border-bottom-color: transparent;
}

#fileViewer #viewAndObjectManager .active+li {
    display: none;
}

#viewsManager .nav {
    margin-bottom: 0;
    margin-top: 3px;
}

#viewsManager .nav li:first-child {
    margin-left: 5px;
}

.singleModel #viewsManager #viewAndObjectManager li.active+li {
    display: none;
}

.singleModel.ifcModel #viewsManager #viewAndObjectManager li.active+li {
    display: block;
}

#viewsManager .tab-content {
    overflow: visible;
}

#viewsManager .tab-content .tab-pane {
    overflow: auto;
}

.toggle-view-manager {
    float: right!important;
}

.toggle-view-manager button {
    min-width: 33px;
    margin-top: -2px;
    height: 20px!important;
    padding-top: 0!important;
}

.collapsed .toggle-view-manager .fa-chevron-down, .toggle-view-manager .fa-chevron-up, .toggle-view-manager .toggle-text {
    display: none;
}

.toggle-view-manager .fa-chevron-down, .collapsed .toggle-view-manager .fa-chevron-up, .collapsed .toggle-view-manager .toggle-text {
    display: inline-block;
}

.toggle-view-manager .fa-chevron-down {
    background-position: -313px -119px;
    background-image: url(/images/glyphicons-halflings.png);
}

.collapsed .toggle-view-manager button {
    position: absolute;
    bottom: 5px;
    right: 5px;
    visibility: visible;
}

#viewsManager.collapsed {
    visibility: hidden;
}

#searchTab select, #queryBuilderModal select {
    height: 25px;
    padding: 2px;
    font-size: 12px;
    line-height: 20px;
}

#searchTab span {
    font-size: 12px;
    margin-left: 20px;
}

#searchTab table th, #searchTab table td, #listDetail table th, #listDetail table td {
    text-align: center;
    padding-left: 5px;
}

#listManagerTab input[type="text"], #queryBuilderModal input[type="text"] {
    height: 20px;
    padding: 2px;
    font-size: 12px;
}

#searchTab #objectName {
    min-width: 200px;
}

#searchTab #worksets {
    display: inline-block;
    font-size: 12px;
    margin-left: 20px;
}

#searchTab #worksets li a {
    display: inline-block;
}

#searchTab #worksets .dropdown-menu {
    min-width: 150px;
    max-height: 70px;
    overflow: auto;
}

#myLists {
    margin-right: 8px;
}

#myLists .ui-resizable-handle {
    right: -10px;
}

.list-hidden #myLists .ui-resizable-handle {
    right: -27px;
    width: 27px;
}

.list-hidden #myLists .list-wrapper, .list-hidden .parent-container {
    display: none;
}

#objectManagerTab .ui-resizable-e.dockable {
    background: #ddd;
}

#objectManagerTab.list-hidden #myLists {
    margin-right: 27px;
    width: 0!important;
}

.list-hidden #show-list {
    display: block;
}

#show-list {
    left: -58px;
    top: 62px;
    display: none;
}

#searchTab #worksets input[type="checkbox"] {
    margin: 5px;
}

#searchTab #worksets .dropdown-menu label {
    width: 100%;
}

#objectManagerTab #myLists {
    float: left;
    width: 30%;
    height: 100%;
}

#objectManagerTab #myLists .list-wrapper {
    overflow: auto;
    height: 100%;
    display: block;
}

#objectManagerTab #listDetail {
    float: none;
    height: 100%;
    overflow: auto;
}

#myLists img {
    height: 20px;
    width: 20px;
}

.fixedWidthThirty {
    width: 30px;
}

#myLists button, #queryBuilderModal table button {
    width: 35px;
    min-width: 35px;
}

#myLists #addnewlist {
    padding: 0;
    display: inline-block;
}

#myLists tbody tr {
    cursor: pointer;
}

#myLists tbody tr.edit {
    cursor: default;
}

#myLists table th {
    font-weight: bold;
    color: #666;
}

#myLists table th, #myLists table td {
    padding-left: 0;
    vertical-align: middle;
}

#myLists .fixedWidthThirty {
    text-align: center;
}

#myLists td input[type="text"] {
    margin: 0;
    width: 96%;
    padding: 1px 2%;
}

#myLists input[type="text"], #myLists .removeList, #myLists .saveList, #myLists .edit .listNameText, #myLists .edit .editList {
    display: none;
}

#myLists .edit input[type="text"], #myLists .edit .removeList, #myLists .edit .saveList {
    display: inline-block;
}

#myLists .rowSelected, #searchTab .rowSelected, #listDetail .rowSelected {
    background-color: #E5E5E5!important;
}

#searchTab form {
    margin-top: 10px;
}

#searchTab table {
    margin-top: 10px;
    border-top: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    height: auto;
}

#listDetail table {
    border-bottom: 1px solid #ccc;
    height: auto;
}

#myLists table {
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    height: auto;
}

#queryBuilderModal .modal-body {
    overflow: auto;
}

#top-search .form-search {
    width: 100%;
}
#close_btn{
    display: none;
    color: var(--dark-gray, #333333);
}

#close_btn>i{
    color: var(--primary-white, #FFFFFF);
}

.change-bg-grey{
    color: var(--dark-gray, #333333) !important;
}

#header #search_btn.aLoader:before {
    box-sizing: border-box;
    top: 0px;
    height: 100%;
    width: 100%;
    top: 0px;
    left: 0px;
    margin-left: 0px;
    border-width: 2px;
    border-top-color: var(--primary-color, #3569AE);
}

#divsearch.input-append.span12 {
    margin: 0;
    padding: 0;
    border-radius: 6px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1px;
}

.form-search .input-append .search-query {
    border: none;
}

#header {
    background-color: var(--primary-color, #3569AE);
    position: absolute;
    width: 100%;
    z-index: 8;
    max-height: var(--header-expanded-height, 83px);
    min-width: 970px;
    border-top: 0px solid var(--primary-white, #FFFFFF);
}

#header:has(.global-search .popover) {
    z-index: 108;
}

#top-search .search-query {
    background-color: transparent;
    box-shadow: none;
}

#top-search input:-moz-placeholder {
    color: var(--primary-white, #FFFFFF);
}

#top-search input:-ms-input-placeholder {
    color: var(--primary-white, #FFFFFF);
}

#top-search input::-webkit-input-placeholder {
    color: var(--primary-white, #FFFFFF);
}

#top-search input:focus::-webkit-input-placeholder {
    color: var(--primary-btn-disable-text, #757575);
}

#header .filterui-button, a.filterui-button, .filterui-button:visited {
    text-shadow: none;
}

#divsearch.input-append.span12 div.pull-left.span9 {
    margin: 0;
}

#myLists .loading img {
    height: auto;
}

#queryBuilderModal .table td {
    padding: 8px;
}

#queryBuilderModal .table select, #queryBuilderModal .table input {
    margin-bottom: 0;
}

.listNameText {
    max-width: 200px;
    display: inline-block;
    white-space: pre;
    text-overflow: ellipsis;
    overflow: hidden;
}

#objectPropertyDiv {
    overflow: auto;
}

#objectPropertyDiv .addRemoveButtonContainer {
    padding: 5px;
}

#objectPropertyDiv table {
    width: auto;
    min-width: 100%;
}

#objectPropertiesModal.hoops #sidenav-assoc, #objectPropertiesModal.hoops #sidenav-audit-history {
    display: none;
}

#objectPropertiesModal.hoops table {
    text-transform: capitalize;
}

#objectPropertyDiv table tr.selected {
    background: #e5e5e5!important;
}

#objectPropertyDiv .addRemoveButtonContainer a {
    margin-left: 5px;
}

#objectPropertiesModal #left-nav-blocks a.active {
    background-position: 0 -70px;
    text-decoration: none;
    color: var(--primary-white, #FFFFFF);
}

#objectPropertyDiv .table {
    height: auto;
}

#objectPropertyDiv button {
    width: 22px;
    min-width: 16px;
    padding: 4px;
    margin-right: 10px;
}

.propSection .icon-chevron-right {
    margin-top: -3px;
    margin-left: -1px;
}

.propSection .icon-chevron-down {
    margin-top: -2px;
    background-position: -313px -119px;
    background-image: url("/images/glyphicons-halflings.png");
}

#objectPropertyDiv tr.propSection {
    background: #ccc!important;
}

#creatFormModal {
    height: 100%!important;
    top: 0 !important;
}

#myModal-actionforcommentcoordination {
    min-height: 103px;
    width: 85%;
}

#myModal-actionforacknowledgment, #myModal-actionforcommentincorporation {
    min-height: 300px;
    width: 85%;
}

.user-email-notification {
    width: 20px;
    height: 16px;
    position: relative;
    top: 4px;
}

#optionsmore-invite-users {
    background: url(../images/admin/more_options_inviteUsers.png) 0 0 no-repeat;
}

#inviteUsersSection, #invitedUsersStatusSection {
    margin-left: 0;
}

#inviteUsersModal {
    width: 80%;
}

#inviteUsersTableWrapper, #invitedStatusTableWrapper {
    margin: 0 15px 0 5px;
    overflow-x: hidden;
    overflow-y: auto;
    min-height: 100px;
    position: relative;
    height: 100%;
}

#invitedStatusTableWrapper {
    overflow: auto;
}

#inviteUsersTable, #invitedStatusTable {
    width: 100%;
    position: absolute;
}

#invitedStatusTable {
    margin: 5px 0;
}

#inviteUsersTable *, #invitedStatusTable * {
    font-size: 12px;
}

#inviteUsersTable thead tr, #invitedStatusTable thead tr {
    font-weight: bold;
}

#inviteUsersTable td, #invitedStatusTable td {
    padding: 3px 5px;
    vertical-align: top;
}

#inviteUsersTable tr td {
    width: 24%;
}

#inviteUsersTable tr td:first-child {
    border: none;
    padding: 0 2px 0 5px;
    vertical-align: middle;
    width: 10px;
    background-color: white;
}

#inviteUsersTable tr td:last-child {
    width: 26%;
    padding-right: 0;
}

#inviteUsersTable tr input {
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    margin-bottom: 0;
    min-height: 20px;
}

div.inviteForRoles {
    max-height: 100%;
    margin-bottom: 0;
}

#inviteUsersContainer label {
    font-size: 12px;
}

.removeInvitationBtn>img {
    height: 12px;
    width: 8px;
}

.inviteDateDisplayTd {
    width: 75px;
}

#invitedUsersModal {
    font-size: 12px;
}

#invitedUsersModal .userTablesContainer {
    overflow-y: auto;
}

.alreadyUserTable {
    margin-bottom: 20px;
    width: 100%;
}

.alreadyUserTable thead tr {
    background-color: #ccc;
    font-weight: bold;
}

.alreadyUserTable caption {
    text-align: left;
    width: 100%;
}

.alreadyUserTable tr>* {
    border: 1px solid #D1D1D1;
    padding: 3px 5px;
    vertical-align: top;
}

.ui-front {
    z-index: 11000!important;
}

.ui-front ul li {
    list-style: none;
    overflow-y: auto;
    overflow-x: hidden;
}

.ui-autocomplete {
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
}

.column-freeze {
    clear: both;
}

.column-freeze table {
    width: 100%;
}

.column-freeze td {
    border: 2px solid #e6e6e6;
    background-color: #f6f6f6;
    color: black;
    font-size: 12px;
    border-bottom: none;
    text-align: center;
    font-weight: bold;
}

.column-freeze td:first-child {
    width: 44px;
}

.table-layout {
    clear: both;
    max-height: 250px;
    overflow: auto;
    overflow-x: hidden;
    margin-right: -15px;
}

.table-layout table {
    width: 100%;
}

.table-layout td {
    border: 2px solid #e6e6e6;
}

.table-layout td:first-child {
    width: 44px;
}

.table-layout td:first-child .redicon-remove {
    margin: 0 auto;
    left: 50%;
    position: relative;
    top: 50%;
    margin-left: -7px;
    margin-top: 0;
}

.table-layout input {
    border: none;
    border-radius: 0;
    box-shadow: none;
    margin-bottom: 0;
}

span.filterTextStyle, input#procu_pd_datepicker, input#procu_fd_containText {
    font-size: 12px;
    font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
}

.ja_JP span.filterTextStyle, .ja_JP input#procu_pd_datepicker, .ja_JP input#procu_fd_containText {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

.curtain {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    opacity: .4;
    background: white;
    z-index: 11;
}

#activityCentre{
	position: absolute;
	bottom: 15px;
	right: 15px;
	z-index: 1000;
	width: 40%;
	background-color: white;
	border-radius: 4px;
    border: 1px solid #ccc;
}

#activityCentre .activityCentreHeader {
    padding: 5px 10px;
    background-color: #3569AE;
    color: #FFFFFF;
}

#activityCentre .activityCentreHeader #toggleContent {
    cursor: pointer;
    background-image: none;
}

#activityCentre #activityCentreBody {
    max-height: 500px;
    overflow: auto;	
}

#activityCentre #activityCentreBody .infoDiv {
    width:70%;
    float: left;			
}

#activityCentre #activityCentreBody .infoDiv .fullWidth {
    width:100%;					
}

#activityCentre #activityCentreBody .infoDiv .fullWidth .halfWidth {
    width:50%;	
    float:left;
}
		
#activityCentre #activityCentreBody .activity {
    width: 100%;
    border-bottom: 1px solid #ccc;
    padding: 10px;
    box-sizing: border-box;
    margin: 0px;
    background-color: #fff;
}
		
#activityCentre #activityCentreBody .loadingDiv {
    width:30%;
    float: left;
}

#activityCentre #activityCentreBody .loadingDiv .processing > i {
    margin-left: 5px;
    color: #337ab7;
}
		
#activityCentre #activityCentreBody .loader{
    margin-left: 10px;
    height: 20px;
    vertical-align: text-top;
}

.rangeconfigurationWrapper {
    padding-bottom: 0;
}

.range-errorborder {
    border: 1px solid #F00;
}

.range-border {
    border: 1px solid #ccc;
}

.range-error {
    color: red;
    padding-top: 10px;
    padding-left: 10px;
    font-size: 12px;
}

.hidecontainer {
    display: none;
}

.shoowcontainer {
    display: block;
}

.rangeintervaldiv>div {
    padding-top: 10px;
    padding-left: 5px;
}

.range-rpt {
    padding-left: 1px;
    float: left;
}

.datetimeintervallbl {
    padding-left: 3px;
    padding-right: 5px;
    float: left;
}

.Numintervallbl {
    padding-left: 3px;
    padding-right: 5px;
    float: left;
}

.rangeintervaldiv label, .rangeintervaldiv span, .rangeintervaldiv select, .rangeintervaldiv input[type="text"], .rangeintervaldiv input[type="number"] {
    font-size: 12px;
    margin-bottom: 0;
}

.rangecompContainer .withintimeinterval {
    float: left;
    width: 55px;
    height: 22px;
    margin-right: 10px;
    line-height: 23px;
    padding: 0;
}

.rangecompContainer .withintimeintervaldrp {
    width: 73px;
    height: 23px;
    padding: 0;
    line-height: 23px;
}

.rangecompContainer .input-range {
    width: 90px;
    height: 23px;
    margin-right: 5px;
    line-height: 23px;
    padding: 0 0 0 3px;
}

.range-button {
    margin-left: 3px;
    padding-bottom: 10px;
    padding-right: 5px;
    float: left;
    padding-top: 10px;
    clear: both;
}

.range-button input {
    margin-right: 5px;
}

.rangecompoverlay {
    position: absolute;
    top: 0;
    left: 0;
    display: none;
    width: 100%;
    height: 100%;
    z-index: 2000;
}

.rangecompContainer {
    background-color: white;
    border: 1px solid #ccc;
    position: absolute;
    z-index: 2000;
    float: left;
    padding: 5px;
    min-width: 333.5px;
}

.rangeNum {
    width: 300px;
}

.rangeDate {
    width: 350px;
}

.rangecompContainer .pright34 {
    padding-right: 34px;
}

.rangecompContainer .pright21 {
    padding-right: 21px;
}

.rangecompContainer .margin-zero {
    margin-right: 0;
}

.rangecompContainer img.ui-datepicker-trigger {
    vertical-align: top;
    margin-top: 3px;
}

#co-ordinate-container>div, #map-container>div {
    padding: 10px 0 0 5px;
    font-size: 12px;
}

#co-ordinate-container .exact, #map-container .exact {
    clear: both;
}

#co-ordinate-container input[type="radio"], #map-container input[type="radio"] {
    margin-top: 4px;
}

#co-ordinate-container .label-text label, #map-container .label-text label {
    font-size: 12px;
}

#co-ordinate-container .range-controls, #map-container .range-controls {
    margin-left: 5px;
}

.co-ordinate-row, .map-row {
    float: left;
    clear: both;
    margin-bottom: 10px;
}

#co-ordinate-container label, #map-container label {
    font-size: 12px;
    float: left;
    margin-top: 0;
    padding: 0 3px;
}

#co-ordinate-container input[type="number"], #map-container input[type="number"] {
    float: left;
    width: 60px;
    height: 22px;
    margin-right: 10px;
    line-height: 23px;
    padding: 0;
}

#co-ordinate-container .exact .range-controls, #map-container .exact .range-controls {
    margin-left: 12px;
}

#map-container .map-latitude {
    margin-right: 8px;
}

.coo-label, .map-label {
    margin-right: 5px;
}

#activityCentreBody .infoDiv {
    width: 70%;
    float: left;
}

#activityCentreBody .infoDiv .fullWidth {
    width: 100%;
}

.activity {
    width: 100%;
    margin: 10px 0;
    background-color: #ccc;
    padding: 10px;
    box-sizing: border-box;
}

#activityCentreBody .infoDiv .fullWidth .halfWidth {
    width: 50%;
    float: left;
}

#activityCentreBody .loadingDiv {
    width: 30%;
    float: left;
}

#activityCentreBody .loader {
    margin-left: 10px;
    height: 20px;
    vertical-align: text-top;
}

#editAppsModal .modal-body {
    overflow: auto;
}

#editAppsModal .modal-footer .popover  .popover-content {
    white-space: pre-wrap;
}

#editAppsModal #enableECatalagueLabel {
    margin-left: 12px;
    display: flex;
    align-items: center;
    white-space: nowrap;
}

#editAppsModal #enableECatalagueLabel input[type='checkbox'] {
    margin: 0 12px;
}

.wait-async-msg {
    display: none;
    float: left;
    margin: 0 0 0 10px;
}

.noteForManageApps {
    color: red;
    font-size: 12px;
    margin: 5px 0 10px 0;
}

#editAppsModal .formStatusTable td, #editAppsModal .formStatusTable th {
    text-align: left;
    padding-right: 10px;
}

#editAppsModal .noOfInstances {
    width: 60px;
}

#editAppsModal .instanceFor {
    width: auto;
}

#editAppsModal .controls {
    margin-left: 280px!important;
}

#editAppsModal .control-label {
    margin-left: 30px;
    text-align: left;
    width: 240px;
    font-weight: bold;
}

#editAppsModal .grayout {
    color: var(--disable-text-color,#757575);
}

#editAppsModal .form-horizontal .control-group {
    margin-bottom: 5px;
}

#editAppsModal .form-horizontal .control-group.masterWorkspace {
    position: relative;
}

#editAppsModal .insideControlGroup .control-label {
    width: 125px;
}

#editAppsModal .insideControlGroup .controls {
    margin-left: 165px!important;
}

#manageAppsModal #apps {
    width: 100%;
}

#createOrEditDocMail .emailDomain, #createOrEditFormMail .emailDomain {
    height: 24px;
}

#createOrEditDocMail #emailId, #createOrEditFormMail #emailId {
    height: 20px;
    font-size: 12px;
}

.closeEditFolderJsp {
    background: transparent;
    background-color: transparent;
    color: var(--primary-white, #FFFFFF);
    border: none;
    margin-right: 10px;
}

.worksetListForEditFolder {
    height: auto;
    margin-top: 32px;
}

.headerEditFolderJsp {
    position: fixed;
}

.worksetListForEditFolder .disabled {
    opacity: .4;
}

#parentdivModalrenderActionforCommentIncorporation, #parentdivModalrenderActionforCommentCoordination, #parentdivModalrenderActionforAcknowledgment, #parentdivModalrenderActionforAction {
    border: 1px solid #d1d1d1;
    overflow-x: auto;
    margin: 0 5px 5px 5px;
    border-radius: 4px;
    overflow-y: hidden;
}

.directoryCatalogMode {
    width: 100px;
    float: left;
}

#directoryProductsListTemplete .modal-body {
    padding: 0;
}

.fileupload-tablet .divSelectedFilesList {
    height: 50px!important;
}

body.modal-open {
    overflow: hidden;
}

.modelType {
    height: 22px;
    width: 22px;
    top: 5px;
    position: absolute;
    left: 5px;
}

.customList-ul {
    overflow: overlay;
    height: inherit;
    color: black;
    width: 93%;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.customList-li {
    outline: none;
    text-decoration: none;
    color: var(--primary-black, #000000);
    background: var(--primary-white, #FFFFFF);
    cursor: pointer;
    overflow: hidden;
    height: 20px;
    padding: 0 5px 0 5px;
}

.customList-li:hover {
    color: var(--primary-black, #000000);
    background: #D3D7DB;
}

.customList-li.selected {
    color: var(--primary-white, #FFFFFF);
    background: #4F9DFA;
}

.distributionGroupName, .distFormType, .distAssignToUserWithAction, .securityAssignTo {
    min-height: 20px;
}

#dialogForSaveMarkup {
    width: 500px;
}

#assignAppsModal table {
    height: auto;
}

td.xdTableContentCell span.xdRichTextBox {
    height: auto!important;
    width: 99%!important;
    min-height: 21px;
}

#model-blocks .listingImage>div {
    margin-left: 20%;
}

.width-padded .textcontain {
    width: 140px;
    height: 20px;
    padding: 2px 0 3px 7px;
    font-size: 12px;
    border-color: #d4d4d4;
    color: #acacac;
    margin: 0;
    border-radius: 0;
    display: block;
    border-radius: 4px;
    line-height: 19px;
    border-radius: 4px;
}
.width-padded .textcontain:focus {
    outline: 1px solid black;
}
  

.width-padded .textcontain:-moz-placeholder {
    color: #ACACAC;
}

.width-padded .textcontain:-ms-input-placeholder {
    color: #ACACAC;
}

.width-padded .textcontain::-webkit-input-placeholder {
    color: #ACACAC;
}

.grey-bg-color {
    background-color: #9D9D9D;
}

.grey-border-color {
    border: 1px solid #9D9D9D;
}

#manageAppsModal .dragdiv-drag-handle.divth.img>div {
    text-align: left;
}


#modalOrgLayoutHistory {
    max-height: 750px;
}

#modalOrgLayoutHistory .modal-body {
    max-height: 650px;
    overflow: auto!important;
}

.export-container {
    padding: 0;
    margin: 0;
}

div.showMore span.recordCounter {
    display: block;
}

span.recordCounter {
    display: none;
}

div.showMore span.initialGlobalSearch {
    display: none;
}

#manageAppsMainDiv .dragdiv-drag-handle.divth.img img {
    left: 32%;
}

#editAppsModal {
    width: 90%;
}

#editAppsModal legend, #myModal-EditFolder legend, #myModal-addfolder legend, #batchChangeStatusModal legend {
    border: none;
    width: auto;
    font-size: 15px;
    line-height: normal;
    margin: 0;
    padding: 0 3px;
    font-weight: bold;
}

#myModal-addfolder .form-inline {
    margin-bottom: 10px;
}

#myModal-addfolder .folder-settings .loading img {
    margin-top: 38px!important;
}

#editAppsModal .formDetailSettings {
    display: inline-block;
    margin-bottom: 10px;
    width: 245px;
}

#editAppsModal .formDetailSettings .controls {
    margin-left: 30px!important;
}

#editAppsModal .radio {
    display: inline-block;
}

#editAppsModal .responseSettings {
    overflow: hidden;
    clear: none;
}

#editAppsModal .wrpperCER {
    margin-left: 1%;
    width: 49%;
}

#editAppsModal .distributionFieldSet legend+.control-group, #editAppsModal .editAndForwardFieldSet legend+.control-group, #editAppsModal .attachmentFieldSet legend+.control-group, #editAppsModal .responseSettings legend+.control-group {
    margin-top: 2px;
}

@media(max-width: 1100px) {
    .filter-btn-container .btn {
        padding: 4px 10px;
    }
    .filter-btn-container .textcontain {
        width: 86px;
    }
}

@media(max-width: 979px) {
    #editAppsModal .wrpperCER {
        width: auto;
        margin-left: 0;
        float: none;
    }
    #editAppsModal .additionalDivTwo, #editAppsModal .additionalDivOne {
        width: auto;
    }
}

@media(min-width: 979px) and
/*!YUI Compressor */

(max-width:1024px) {
    #editAppsModal .controllerSettings .controls, #editAppsModal .editOriSettings .controls, #editAppsModal .responseSettings .controls {
        margin-left: 270px!important;
    }
}

#editAppsModal .controls input[name="FT_FORM_ACTION_EMAIL_SUBJECT"] {
    width: 100%;
    box-sizing: border-box;
}

@media(min-width: 1200px) {
    #editAppsModal .controls input[name="FT_FORM_ACTION_EMAIL_SUBJECT"] {
        width: 200px;
    }
}

#editAppsModal .actionRequired .assocAction {
    width: 170px;
    display: inline-block;
    margin-top: 10px;
}

#editAppsModal .formStatusTable .formStatusWrapper {
    width: 330px;
    border: 1px solid #ccc;
    border-left: 1px solid #ccc;
    margin: 1px;
    margin-bottom: -27px;
    float: left;
    background: var(--primary-white, #FFFFFF);
    position: relative;
    margin-left: -2px;
    zoom: 1;
}

#editAppsModal .formStatusTable .closeOutFormText {
    text-align: right;
    font-weight: bold;
    padding: 2px;
    border-bottom: 1px solid #ccc;
}

#editAppsModal .formStatusTable .formStatusDiv {
    display: inline-block;
    padding: 5px;
    margin-left: 5px;
    max-width: 230px;
}

#editAppsModal .formStatusTable .formStatusDiv label {
    text-overflow: ellipsis;
    white-space: pre;
    overflow: hidden;
}

#editAppsModal .formStatusTable .cloaseOutFormDiv {
    width: 20px;
    float: right;
    margin-right: 60px;
}

#editAppsModal .additionalDivOne {
    width: 50%;
    display: inline-block;
}

#editAppsModal .additionalDivTow {
    width: 49%;
    display: inline-block;
    vertical-align: top;
}

#editAppsModal select, #editAppsModal input[type="text"] {
    width: auto;
}

#editAppsModal .infoIcon, .userCheckBox .infoIcon {
    width: 25px;
    margin-bottom: -10px;
}

#editAppsModal .popover {
    color: red;
    width: auto;
}

#editAppsModal .insideControlGroup {
    margin-top: -30px;
}

#editAppsModal hr {
    margin: 5px 0;
}

 #editAppsModal .allowDistributionAfterCreationRecipients, #editAppsModal .allowDistributionAfterCreationOriginator, #editAppsModal .allowDistributionByAll, #editAppsModal .allowDistributionAfterCreationRoles {
    display: inline-block;
}

#editAppsModal .allowEditAndForwardRecipients .control-label, #editAppsModal .allowDistributionAfterCreationRecipients .control-label, #editAppsModal .allowDistributionAfterCreationOriginator .control-label, #editAppsModal .allowDistributionAfterCreationRoles .control-label, #editAppsModal .allowDistributionByAll .control-label {
    width: 100px;
}

#editAppsModal .allowDistributionAfterCreationRecipients .controls, #editAppsModal .allowDistributionAfterCreationOriginator .controls, #editAppsModal .allowDistributionAfterCreationRoles .controls, #editAppsModal .allowDistributionByAll .controls {
    margin-left: 145px!important;
}

#editAppsModal .allowDistributionByAll .controls, #editAppsModal .allowDistributionAfterCreationRecipients .controls, #editAppsModal .allowDistributionAfterCreationOriginator .controls, #editAppsModal .allowDistributionAfterCreationRoles .controls {
    margin-left: 10px!important;
    display: inline-block;
}

#editAppsModal .allowDistributionByAll .control-label, #editAppsModal .allowDistributionAfterCreationRecipients .control-label, #editAppsModal .allowDistributionAfterCreationOriginator .control-label, #editAppsModal .allowDistributionAfterCreationRoles .control-label {
    width: auto;
    display: inline-block;
}

#editAppsModal .allowDistributionAfterCreationRoles {
    float: left;
}

#rolesFilterAppsSettings {
    width: 240px;
    display: inline-block;
    border-left: none;
    margin-left: 10px;
}

@media(max-width: 1520px) {
    #editAppsModal .allowEditAndForwardRecipients .controls, #editAppsModal .allowEditAndForwardOriginator .controls,#editAppsModal .allowDistributionAfterCreationRecipients .controls {
        margin-left: 280px!important;
    }
}

#editAppsModal .assocHeaderDiv {
    font-weight: bold;
    margin-left: 5px;
}

#editAppsModal .enableSpellCheck {
    display: inline-block;
    width: 145px;
    margin-left: 10px!important;
}

.co-type {
    clear: both;
    padding-top: 5px;
}

th.co-ordinates {
    text-align: center;
}

.co-xyztype>div {
    width: 32.33%;
    float: left;
}

.co-xytype>div {
    width: 49%;
    float: left;
}

.co-xtype>div {
    width: 95%;
    float: left;
}

.co-input-type>div {
    margin: 0 5px;
}

.co-input-type>div>input {
    width: 60px;
    float: left;
    font-size: 11px;
    margin-bottom: 0;
}

.co-font-size>div>input {
    font-size: 11px;
}

.co-input-xyztype {
    width: 252px;
}

.co-input-xytype {
    width: 168px;
}

.co-input-xtype {
    width: 84px;
}

.co-padding-08 {
    padding: 8px;
}

.merge-panel .co-input-type input {
    width: 43px!important;
}

.merge-panel th div.co-input-type.attribdata.thMergePanel, .merge-panel .coordinate-width {
    width: auto!important;
    margin-right: 0!important;
}

.co-input-type i.icon-chevron-down {
    position: static;
    margin-left: 3px;
}

h2#formName {
    width: 90%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.proxy-user #saveMyAccount, .proxy-user #userPhoto, .proxy-user #header_adoddleCor, .proxy-user .dashboard-nav, .proxy-user #first_row, .proxy-user #col1 {
    display: none!important;
}

.proxy-user #userProfImg {
    border-style: solid;
    border-radius: 50%;
    border-color: #888383;
    height: 30px;
}

img#userProfImg.circle:hover {
    border-radius: 0;
}

#switchUser :focus {
    color: #333;
}

.ja_JP #myModal-switch-proxy-user *:not(.myModalLabel) {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

#proxy-user-search-input {
    border: none;
    border-radius: 3px;
    float: right;
    margin: 0 7px 0;
    padding: 0px 10px;
    width: 170px;
    font-size: 12px;
}

.boxUserName {
    width: 170px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#modal-manage-proxy-user-detail {
    width: 90%;
}

#myModal-switch-proxy-user {
    width: 90%;
}

#myModal-switch-proxy-user .helpcontent img {
    margin-top: -5px;
}

#myModal-switch-proxy-user .modal-body {
    padding: 0;
    margin: 0;
}

#proxy-user-list-container {
    min-height: 400px;
    padding-top: 10px;
    overflow: auto;
}

#myModal-switch-proxy-user .boxgrid {
    border: 1px solid #E2E2E2;
    border-radius: 4px;
    float: left;
    margin: 0 0 20px 20px;
    overflow: hidden;
    padding: 10px 10px 5px;
    position: relative;
    text-align: center;
    font-weight: normal;
    -webkit-transition: all .2s ease;
    -moz-transition: all .2s ease;
    -ms-transition: all .2s ease;
    -o-transition: all .2s ease;
    transition: all .2s ease;
    vertical-align: middle;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

#myModal-switch-proxy-user .boxgrid:hover {
    cursor: pointer;
    -webkit-box-shadow: 2px 2px 15px #eee;
    -moz-box-shadow: 2px 2px 15px #eee;
    box-shadow: 2px 2px 15px #eee;
}

#myModal-switch-proxy-user .boxgrid-active, #myModal-switch-proxy-user .boxgrid-active:hover {
    background-color: #f1f1f1;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

#myModal-switch-proxy-user .boxgrid img {
    width: 150px;
    height: 150px;
}

#modal-manage-proxy-user-detail input, #modal-manage-proxy-user-detail .select2-container {
    font-size: 12px;
}

.ja_JP #modal-manage-proxy-user-detail *:not(.myModalLabel) {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

#modal-manage-proxy-user-detail .checkbox-group-div {
    float: left;
}

#modal-manage-proxy-user-detail .checkbox-group-div .bulk-apply-input {
    margin: 0 6px 0;
}

#modal-manage-proxy-user-detail #apply-to-all-btn {
    float: left;
}

#modal-manage-proxy-user-detail .modal-body {
    overflow: hidden;
}

#modal-manage-proxy-user-detail .restored, #modal-manage-proxy-user-detail .maximized {
    float: right;
    line-height: 0;
    padding-top: 7px;
}

#modal-manage-proxy-user-detail .modal-main-content {
    padding-top: 15px;
}

#modal-manage-proxy-user-detail thead tr {
    font-size: 13px;
    font-weight: bold;
}

#modal-manage-proxy-user-detail .form-content-style-span {
    vertical-align: sub;
    margin-right: 5px;
    font-size: 13px;
    float: inherit;
    margin-left: 0;
}

#modal-manage-proxy-user-detail .divGap {
    padding-left: 60px;
}

#modal-manage-proxy-user-detail .projects-users-span {
    text-align: left;
    padding-left: 0;
    margin-left: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 20px;
}

#modal-manage-proxy-user-detail .formContentStyle h5,
#modal-manage-proxy-user-detail .formContentStyle h2 {
    position: relative;
    top: -10px;
    min-width: 100px;
    background-color: white;
    width: 200px;
    text-align: center;
    font-weight: normal;
    padding: 0 5px 0 5px;
    margin: 0;
    font-size: 14px;
    line-height: normal;
}

#modal-manage-proxy-user-detail .define-user-status {
    width: 145px!important;
}

#modal-manage-proxy-user-detail .define-proxy-user-access {
    width: 180px!important;
}

#modal-manage-proxy-user-detail .form-content-style-header {
    padding-right: 10px;
    font-weight: bold;
    vertical-align: sub;
}

#modal-manage-proxy-user-detail #manage-proxy-user-list-container {
    min-height: 200px;
    width: 100%;
    padding-top: 5px;
    overflow: auto;
}

#modal-manage-proxy-user-detail #manage-proxy-user-pic {
    width: 60px;
    height: 60px;
    float: left;
    border-style: solid;
    border-radius: 5px;
    border-color: #888383;
}

#modal-manage-proxy-user-detail #header-activity-div {
    padding-top: 20px;
    padding-left: 80px;
}

#modal-manage-proxy-user-detail #add-proxy-user-btn {
    margin-bottom: 8px;
}

#manage-proxy-user-list-table {
    width: 100%;
    border-top: 1px dotted #c7c7c7;
}

#manage-proxy-user-list-table .icon-chevron-down {
    margin-bottom: 0;
    margin-right: 3px;
    position: relative;
}

#manage-proxy-user-list-table .ui-datepicker-trigger {
    vertical-align: middle;
}

#manage-proxy-user-list-table .remove-proxy-row-btn {
    margin-left: 7px;
}

#manage-proxy-user-list-table thead tr input[type='checkbox'] {
    margin-top: 0;
    margin-left: 20px;
}

#manage-proxy-user-list-table tr {
    background-color: #ccc;
    width: 100%;
    height: 44px;
    padding: 0;
    background: url("../images/icons/tr_repeat.png") repeat-x scroll center bottom transparent;
}

#manage-proxy-user-list-table tr td {
    padding: 0;
    margin: 0;
}

#manage-proxy-user-list-table tr input[type='checkbox'] {
    margin-left: 25px;
}

#manage-proxy-user-list-table tr input[type="checkbox"]+.icon-chevron-down {
    float: none;
    margin-top: 2px;
}

#manage-proxy-user-list-table .projects-name {
    height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 99%;
}

#manage-proxy-user-list-table input, #manage-proxy-user-list-table .select2-container {
    margin-bottom: 0;
}

#projectmanageRolesModal {
    font-size: 13px;
    width: 100%;
    height: 100%;
    margin: 0 !important;
    top: 0px !important;
    left: 0px !important;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    border: none;
}

#projectmanageRolesModal .modal-body .fixed-fixed {
    overflow: hidden;
}

#projectmanageRolesModal #left-nav-blocks a.active, #projectmanageRolesModal #left-nav-blocks a:hover, #projectmanageRolesModal .left-nav-blocks a.active {
    background-position: 0 -70px;
    color: var(--primary-white, #FFFFFF);
    text-decoration: none;
}

#projectmanageRolesModal .left-nav-blocks li>a#sidenav-role-form-permissions {
    letter-spacing: -0.46px;
}

#roles-table, #form-permission-table {
    margin: 0 10px 10px 15px;
    border: 1px solid #ccc;
}

#roles-table li.ch, .role-name li.ch, .role-val ul.ch, #form-permission-table li.ch, #form-permission-table li.cht {
    display: none;
}

.fixedTable-header {
    margin-left: 228px;
    overflow: hidden;
    border-right: 17px solid transparent;
    border-left: 1px solid #ccc;
}

.fixedTable-header li {
    display: inline-block;
    width: 43px;
    box-sizing: border-box;
    padding: 0;
    text-align: left;
    background: white;
    border-radius: 0;
    color: black;
    font-size: 12px;
    border-right: 1px solid #ccc;
}

.fixedTable-header .form-name li {
    width: 301px;
    border-bottom: 1px solid #ccc;
}

#rolePrivilegesMainDiv .fixedTable-header li {
    position: relative;
    height: 190px;
    overflow: hidden;
}

#rolePrivilegesMainDiv .fixedTable-header li div {
    -webkit-transform: rotate(270deg);
    -moz-transform: rotate(270deg);
    -o-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    transform: rotate(270deg);
    height: 22px;
    position: absolute;
    bottom: 85px;
    width: 180px;
    text-overflow: ellipsis;
    white-space: pre;
    overflow: hidden;
    left: -67px;
    text-align: left;
}

#roleFormPermissionsMainDiv .fixedTable-header .role-form-permission-header li {
    position: relative;
    height: 157px;
    overflow: hidden;
}

#roleFormPermissionsMainDiv .fixedTable-header li div {
    -webkit-transform: rotate(270deg);
    -moz-transform: rotate(270deg);
    -o-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    transform: rotate(270deg);
    height: 22px;
    position: absolute;
    bottom: 70px;
    width: 150px;
    text-overflow: ellipsis;
    white-space: pre;
    overflow: hidden;
    left: -52px;
    text-align: left;
}

#roleFormPermissionsMainDiv .cv1 .fixedTable-header li div {
    left: 76px;
}

#roleFormPermissionsMainDiv .cv2 .fixedTable-header li div {
    left: 1px;
}

#roleFormPermissionsMainDiv .cv3 .fixedTable-header li div {
    left: -24px;
}

#roleFormPermissionsMainDiv .cv4 .fixedTable-header li div {
    left: -37px;
}

#roleFormPermissionsMainDiv .cv5 .fixedTable-header li div {
    left: -44px;
}

#roleFormPermissionsMainDiv .cv6 .fixedTable-header li div {
    left: -49px;
}

.fixedTable-sidebar {
    float: left;
    overflow: hidden;
    padding-left: 15px;
    margin-left: -15px;
    border-top: 1px solid #ccc;
}

.fixedTable-sidebar ul {
    border-bottom: 17px solid transparent;
}

.fixedTable-header ul {
    height: auto;
    white-space: nowrap;
}

.fixedTable-sidebar li {
    display: block;
    padding: 3px 4px;
    margin: 0;
    border-bottom: 1px solid #ccc;
    position: relative;
    height: 41px;
}

.fixedTable-sidebar input {
    margin: 0;
    font-size: 12px;
    min-height: 20px;
}

.fixedTable-body {
    overflow: auto;
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#roles-table ul, #form-permission-table ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

#roles-table li, #form-permission-table li {
    vertical-align: middle;
    text-align: center;
    box-sizing: border-box;
}

.fixedTable-body ul {
    height: auto;
    background: white;
    padding: 0;
    display: block;
    margin: 0;
    border-bottom: 1px solid #ccc;
    height: 41px;
    list-style: none;
    font-size: 0;
    white-space: nowrap;
    display: inline-block;
    min-width: 100%;
    box-sizing: border-box;
}

.fixedTable-body ul li {
    display: inline-block;
    height: 40px;
    border-right: 1px solid #ccc;
    font-size: 12px;
    box-sizing: border-box;
    width: 43px;
    cursor: pointer;
    line-height: 40px;
}

.fixedTable-body ul li.na {
    cursor: default;
}

.fixedTable-body ul li:hover {
    background-color: #f0f5f9;
}

.fixedTable-body ul li.na:hover {
    background-color: var(--primary-white, #FFFFFF);
}

.fixedTable-body ul li.checked {
    position: relative;
}

.fixedTable-body ul li.checked:before {
    position: absolute;
    width: 6px;
    height: 13px;
    border-bottom: 2px solid #008000;
    border-right: 2px solid #008000;
    content: "";
    top: 50%;
    left: 50%;
    margin-left: -4px;
    margin-top: -9px;
    -webkit-transform: rotate(40deg);
    -moz-transform: rotate(40deg);
    transform: rotate(40deg);
}

.fixedTable-body ul li.disabled, .fixedTable-body ul li.disabled:hover {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

.cv1 .fixedTable-header .form-name li, .cv2 .fixedTable-header .form-name li, .cv3 .fixedTable-header .form-name li, .cv4 .fixedTable-header .form-name li, .cv5 .fixedTable-header .form-name li, .cv6 .fixedTable-header .form-name li, .cv1 .role-form-permission-header li, .cv1 .fixedTable-body ul li {
    width: 300px;
}

.cv2 .role-form-permission-header li, .cv2 .fixedTable-body ul li {
    width: 150px;
}

.cv3 .role-form-permission-header li, .cv3 .fixedTable-body ul li {
    width: 100px;
}

.cv4 .role-form-permission-header li, .cv4 .fixedTable-body ul li {
    width: 75px;
}

.cv5 .role-form-permission-header li, .cv5 .fixedTable-body ul li {
    width: 60px;
}

.cv6 .role-form-permission-header li, .cv6 .fixedTable-body ul li {
    width: 50px;
}

.fixedTable-body li input {
    margin: 0;
}

.roleHeaderSection {
    margin: 0 10px 0 15px;
    background-color: #ccc;
    font-weight: bold;
    font-size: 12px;
    line-height: 30px;
}

.roleHeaderSection div {
    display: inline-block;
}

.roleHeaderSection div:first-child {
    width: 228px;
    margin-left: 8px;
}

#historyMainDiv .history-export-wrapper {
    padding-right: 10px;
}

#manageRolesSection {
    padding: 0;
    background-color: #eeeded;
    height: 100%;
}

#manageRolesSection .fullwidth-box-top {
    position: absolute;
    z-index: 999;
}

#manageRolesSection .sidebar-nav {
    margin: 42px 10px 0 0px;
}

#manageRolesSection .nopadding-box-lower {
    /* overflow: hidden; this line is causing slowness in IE */
    margin-left: 85px;
    background-color: var(--primary-white, #FFFFFF);
    height: 100%;
    border-top: 34px solid var(--primary-white, #FFFFFF);
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

#manageRolesSection .modal-footer {
    border-top: 1px solid #ccc;
    border-radius: 0px;
    box-shadow: none;
}

#manageRolesSection .form-filters-wrap {
    padding: 10px;
    text-align: right;
    border-bottom: 1px solid #ccc;
}

#manageRolesSection .form-filters-wrap>* {
    display: inline-block;
    vertical-align: middle;
    text-align: left;
    line-height: 18px;
    margin: 0 0 0 4px;
}

#manageRolesSection .aLoader.small.hide{
    display: none;
}

#manageRolesSection .btn-disabled .aLoader.small{
    height: 11px;
    width: 11px;
    margin-left: 5px;
}

#manageRolesSection .btn-disabled .aLoader.small:before {
    height: 7px;
    width: 7px;
}

#manageRolesSection .btn-disabled .aLoader.small:after {
    background-color: transparent;
}

#manageRolesSection .details {
    line-height: 2;
}

#manageRolesSection b.user, #manageRolesSection span.actionName {
    padding-right: 10px;
}

#manageRolesSection .remarks {
    padding-left: 10px;
    margin-left: 55px;
    max-width: 70%;
    display: block;
    white-space: pre-wrap;
}

#manageRolesSection .time {
    margin-top: 2.2%;
}

#manageRolesSection .userimage {
    padding: 5px 5px 0 10px;
}

#manageRolesSection .red {
    color: #C00;
}

#manageRolesSection .smallUserImage img {
    width: 22px;
    height: 22px;
    vertical-align: middle;
}

#manageRolesSection #historyMainDiv {
    min-height: 300px;
    display: none;
    overflow: hidden;
    border: 1px solid #d1d1d1;
    border-top: 30px solid var(--primary-white, #FFFFFF);
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
}

.smallUserImage {
    padding-right: 10px;
    height: 20px;
}

.manageRoleHistory {
    border-bottom: 1px solid #ccc;
    padding: 5px 0;
    float: left;
    width: 100%;
}

.manageRoleHistory .span10, .manageRoleHistory .span1, .manageRoleHistory .span12 {
    margin-left: 0;
}

.historyWrapper {
    overflow: auto;
    height: 94.5%;
}

#form-permission-table .form-name th {
    text-align: center;
    border-bottom: 1px solid #ccc;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 300px;
    max-width: 400px;
    padding: 5px;
    font-weight: bold;
    overflow: hidden;
}

.fixedTable-sidebar .redicon-remove {
    margin: 10px 10px 0 -19px;
    position: absolute;
}

.side-wrapper {
    float: left;
    padding-left: 15px;
    margin-left: -15px;
    overflow: hidden;
    position: relative;
    width: 228px;
}

.fixedTable-header .role-header th {
    max-width: 43px;
    min-width: 43px;
    box-sizing: border-box;
    padding: 0;
    text-align: left;
}

#projectmanageRolesModal .criteria-wrap.smallMaxWidth {
    max-width: 150px!important;
}

#projectmanageRolesModal .filterui-button, #projectmanageRolesModal .filterui-button-subtle, #projectmanageRolesModal .filterui-button:focus, #projectmanageRolesModal .filterui-button:hover, #projectmanageRolesModal .filterui-button-subtle.filterui-button:focus, #projectmanageRolesModal .filterui-button-subtle.filterui-button:hover {
    border: 1px solid #ccc;
}

.container-skin {
    min-height: 100%;
}

.container-fluid.margin-in {
    min-height: 100%;
    padding: 0;
}

.formCreateSection {
    min-height: 80%;
}

.publicFolderPage .innerContainer a {
    color: black;
}

.publicFolderPage .innerContainer a[href] {
    color: var(--primary-color, #3569AE);
}

.publicFolderPage #fileType {
    text-align: center;
}

.publicFolderPage .col-status-fixed-width span {
    padding-left: 0!important;
}

#passwordNote {
    width: 345px;
}

.fixedTable-header tr.role-header th:last-child {
    border-right: 1px solid #ddd;
    border-radius: 0;
}

.fixedTable-header tr.form-name th:last-child {
    border-right: 1px solid #ddd;
}

.fixedTable-header tr.role-form-permission-header th:last-child {
    border-right: 1px solid #ddd;
}

#model-open-assoc-for-mobile {
    height: 100%!important;
    width: 100%!important;
    left: 0!important;
    top: 0!important;
    margin: 0!important;
    padding: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    opacity: 1;
}

#model-open-assoc-for-mobile .modal-header {
    position: relative;
    z-index: 1;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    padding: 2px 8px 4px;
}

#model-open-assoc-for-mobile .modal-header h3,
#model-open-assoc-for-mobile .modal-header .myModalLabel {
    white-space: pre;
    text-overflow: ellipsis;
    width: 93%;
    overflow: hidden;
}

#model-open-assoc-for-mobile .modal-body {
    height: 100%!important;
    padding: 0;
    margin-top: -33px;
    border-top: 33px solid;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.mobileViewer {
    min-width: 0!important;
}

.mobileViewer #wrap .container-skin, .mobileViewer #wrap .container-skin>.container-fluid.margin-in {
    padding: 0;
}

.mobileViewer #wrap, .mobileViewer #wrap .container-skin, .mobileViewer #wrap .container-skin>.container-fluid.margin-in, .mobileViewer #wrap .container-skin>.container-fluid.margin-in #bodyPart, .mobileViewer #wrap .container-skin>.container-fluid.margin-in #bodyPart .nopadding-box-lower {
    height: 100%!important;
    min-height: 100%!important;
}

.mobileViewer #wrap .container-skin>.container-fluid.margin-in #bodyPart .nopadding-box-lower {
    width: 100%;
}

.mobileViewer #wrap .container-skin>.container-fluid.margin-in #bodyPart #viewerParent {
    position: absolute;
}

.mobileViewer #wrap .container-skin>.container-fluid.margin-in #bodyPart #viewerParent, .mobileViewer #wrap .container-skin>.container-fluid.margin-in #bodyPart #viewerParent object {
    height: 100%;
}

.contactDetailModal-2 .action-container .edit-detail {
    display: none;
}

.window-maximize-navigator .historyRow .historyMax {
    display: block;
}

select {
    font-family: inherit;
    outline: none;
}

a.select2-search-choice-expand {
    color: #333;
    margin-left: 3px;
}

div.projects-users-input.proxy-users-select2 {
    width: 100%;
}

#manageRolesSection .select2-search-choice-expand {
    display: none!important;
}

.expand-spinner {
    float: right;
    margin-left: 7px;
}

.modal-open div#header {
    z-index: auto;
}

div#nonScheduling-Report {
    height: 95%;
}

#filterAddUpdateForm label {
    width: 12%;
    text-align: left;
}

#filterAddUpdateForm .saved-filter-name {
    line-height: 20px;
}

#filterAddUpdateForm .controls {
    margin-left: 90px;
}

#fieldLocDrpdwn {
    width: 100%;
    margin: -3px;
    background-color: var(--primary-color, #3569AE);
    color: var(--primary-white, #FFFFFF);
    float: left;
    font-size: 14px;
    font-weight: 300;
    line-height: 25px;
    border-color: var(--primary-color, #3569AE);
}

.orgID, .roleID {
    display: none;
}

#planViewer {
    display: none;
}

.viewer-open #planViewer, .viewer-open .fullwidth-box-top h2 {
    display: block !important;
}

.viewer-open .tab-listing-footer, .viewer-open #communicationContent, .viewer-open .listing-setting, .viewer-open .manage-selects, .viewer-open .export-dropdown {
    display: none !important;
}

.viewer-open .filter-btn-container {
    right: 5px;
}

#planView {
    float: right;
    display: none;
    cursor: pointer;
}

.locationTree {
    display: none;
}

#locationListView a {
    display: none;
}

.tree-row a span.locationTreeImg {
    background: url(../images/files/location-folder-icon.png) no-repeat scroll 0 -13px rgba(0, 0, 0, 0);
    height: 14px;
    line-height: 16px;
    padding-right: 6px;
    width: 13px;
    vertical-align: top;
}

.tree-row a span.locationTreeImg.Calibrated {
    background: url(../images/files/location-folder-icon.png) no-repeat scroll transparent;
}

form#actionforactionForm div.divCommentFields {
    display: block;
    float: left;
    width: 100%;
}

#modelAllHistorySection #fileHistoryHeaderIcons select {
    max-width: inherit;
}

.hideText {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.locationTreeRightClick .tree-row {
    position: relative;
}

.locationTreeRightClick .tree-row a.hasChild, .locationTreeRightClick .tree-row a.expandChild {
    float: none;
}

.locationTreeRightClick .tree-row a.docList, .locationTreeRightClick .tree-row .tree-label {
    width: auto;
}

.status-count-div {
    position: absolute;
    right: 10px;
    top: 9px;
}

.status-count-div>span {
    width: 22px;
    height: 18px;
    text-align: center;
    float: left;
    margin-right: 5px;
    line-height: 18px;
    border-radius: 3px;
    color: white;
    font-size: 12px;
}

#project-location-manage-type-groups {
    width: 90%;
    font-size: 13px;
}

#mangeStatusTable input {
    margin-bottom: 0;
}

#mangeStatusTable .deactivatedRow * {
    font-style: italic;
}

#typeNameSearchInput, #orgSearchInput, #userSearchInput {
    width: 96%;
}

.download-non-supported-file {
    position: absolute;
    margin: 0 auto;
    text-align: center;
    top: 50%;
    margin-top: -50px;
    background: var(--primary-white, #FFFFFF);
    padding: 15px;
    margin-left: -130px;
    left: 50%;
    display: none;
}

.download-non-supported-file .btn-primary {
    background-color: var(--primary-color, #3569AE);
    background-image: linear-gradient(to bottom, var(--primary-color, #3569AE), var(--primary-color, #3569AE));
}

.download-non-supported-file .btn-primary:hover, .download-non-supported-file .btn-primary:focus {
    background-color: var(--primary-color, #3569AE);
}

.mangeTypeName, .manageOrg, .manageUser {
    width: 97%;
}

.map-info-container {
    width: 100%;
    font-size: 14px;
    color: black;
}

.ja_JP .map-info-container {
    font-family: "ＭＳ Ｐゴシック", Osaka, "ヒラギノ角ゴ Pro W3";
}

.statusCount {
    width: 22px;
    height: 18px;
    text-align: center;
    float: left;
    margin-right: 5px;
    line-height: 18px;
    border-radius: 3px;
    color: white;
    font-size: 12px;
    margin-bottom: 8px;
}

.ipadAttachAndAssocModal .alldownloadBtn, .androidAttachAndAssocModal .alldownloadBtn {
    display: none;
}

.lazy-loading {
    position: absolute;
    bottom: 0;
    height: 34px;
    width: 100%;
    background: var(--primary-white, #FFFFFF);
    border: 1px solid #d3d3d3;
    color: var(--primary-color, #3569AE);
    text-align: center;
    padding-top: 5px;
    z-index: 1000;
    box-sizing: border-box;
    padding-bottom: 5px;
}

.lazy-loading span {
    display: block;
    margin: 0 auto;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color, #3569AE);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    height: 15px;
    width: 15px;
    display: block;
    margin: 0 auto;
}

.opacity-45 {
    opacity: .45;
    background: var(--primary-white, #FFFFFF);
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    z-index: 2;
}

.editProjectContainer .opacity-45 {
    height: 111%;
}

.opacity-45 .divth, .opacity-45>div, .opacity-45 .divtr {
    background: none;
}

.androidAttachAndAssocModal #tblAttributesAttachedFiles {
    overflow: hidden;
}

#formMsgThreadSection {
    z-index: 1;
}

#divViewFolderPermission a.copy-ext-access-link img {
    margin-top: 5px;
}

.folder-settings .add-folder-button {
    padding-bottom: 0;
}

.folder-settings .accessLink {
    box-sizing: border-box;
}

.folder-settings legend {
    font-size: 14px;
}

.folder-settings .setting-div {
    margin: 10px;
}

.folder-settings fieldset .txtbox {
    max-height: 97px;
}

.folder-settings #overWriteMergEdit {
    display: block;
    float: left;
}

.folder-settings #overWriteMergEdit input {
    margin-left: 10px;
}

.folder-settings #overWriteMergEdit label {
    width: auto;
    float: left;
    margin-left: 5px;
}

.folder-settings .accessLink a {
    vertical-align: middle;
}

#workflowPublishProcess {
    border-radius: 4px 4px 0 0;
    margin-top: -40px;
    max-height: 400px;
    min-height: 40px;
    position: fixed;
    right: 20px;
    width: 20%;
    height: auto;
    bottom: 0;
    z-index: 1100;
    font-size: 14px;
    overflow-y: auto;
}

#workflowPublishProcess.activity-center-visible {
    right: calc(20px+20.25%);
}

#workflowPublishProcess .toggle-delete {
    margin: 5px 10px 0 4px;
}

#workflowPublishProcess .toggle-dash {
    margin: 5px 5px 0 4px;
}

#workflowPublishProcess div#expand-div {
    cursor: pointer;
}

#workflowPublishProcess .dashboard-box-top {
    min-height: 40px;
    height: auto;
}

#workflowPublishProcess #workflow-publish-process-status {
    max-height: 400px;
    overflow: auto;
}

#workflowPublishProcess .publish-status-row {
    background-color: #E3E3E3;
    margin-bottom: 10px;
}

#workflowPublishProcess .projectModelName {
    display: block;
    float: left;
    font-size: 13px;
    margin-left: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 120px;
}

#workflowPublishProcess strong {
    float: left;
}

#workflowPublishProcess .widgetHeader {
    margin-left: 10px;
}

#workflowPublishProcess .marginTop {
    background-color: var(--primary-white, #FFFFFF);
    float: left;
    height: 10px;
    width: 100%;
}

.workflow-details .applyPadding {
    padding: 0 10px;
}

#workflowPublishProcess .publish-status-row .span7 {
    padding-left: 7px;
}

#workflowPublishProcess .entity-name {
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

#workflowPublishProcess .status-inprocess {
    padding-top: 3px;
    width: 16px;
    height: 16px;
    background: url("/images/spinner.gif") no-repeat scroll right center;
}

div.SecondaryFileAttached:hover span.removeAttachement {
    display: block;
}

span.removeAttachement {
    display: none;
    float: right;
    position: absolute;
    right: 4px;
}

div.btn.fileinput-button {
    padding: 5px 4px;
}

.nicEdit-main:focus {
    border: none;
    outline: none;
}

.nicEdit-main sub, .nicEdit-main sup {
    line-height: normal;
}

.content-placeholder {
    color: #888;
    font-size: 14px;
}

.no-comment-text {
    font-size: 14px;
    color: #555;
}

.remove-all-users {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 0 0 3px 3px;
    outline: none;
    padding: 3px;
    position: absolute;
    right: 26.1%;
    top: 7px;
    z-index: 1;
}

.aui-button, a.aui-button, .aui-button:visited {
    background: #f2f2f2;
    background: -moz-linear-gradient(top, var(--primary-white, #FFFFFF) 0, #f2f2f2 100%);
    background: -ms-linear-gradient(top, var(--primary-white, #FFFFFF) 0, #f2f2f2 100%);
    background: -o-linear-gradient(top, var(--primary-white, #FFFFFF) 0, #f2f2f2 100%);
    background: -webkit-linear-gradient(top, var(--primary-white, #FFFFFF) 0, #f2f2f2 100%);
    background: linear-gradient(top, var(--primary-white, #FFFFFF) 0, #f2f2f2 100%);
    border: 1px solid #ccc;
    border-radius: 3.01px;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    color: #333;
    cursor: pointer;
    display: inline-block;
    font-family: inherit;
    font-size: 12px;
    font-variant: normal;
    font-weight: normal;
    height: 2.1428571428571em;
    line-height: 1.4285714285714;
    margin: 0;
    padding: 4px 10px;
    text-decoration: none;
    text-shadow: 0 1px 0 white;
    vertical-align: baseline;
    white-space: nowrap;
}

.navigator-comment .remove-all-users {
    display: inline-block;
    position: initial;
}

.navigator-comment #commentButtons .showAttachmentLink {
    display: none;
}

.navigator-comment .divCommentFields {
    width: 97%;
}

#creatFormModal.modal.createFormModalApp-2 {
    overflow: auto;
}

#search-dist-groups input.checkallDistGroup {
    vertical-align: inherit;
}

div#fileUploadfundingServiceBody, #editAttrCustValidationBody {
    padding-top: 1px;
}

div#fileUploadfundingServiceBody p:first-of-type, #editAttrCustValidationBody p:first-of-type {
    padding-top: 15px;
    padding-bottom: 5px;
}

div#fileUploadfundingServiceBody p:last-of-type, #editAttrCustValidationBody p:last-of-type {
    padding-top: 5px;
}

#editAttributeTable td .docTitleAttribute, #editAttributeTable td .docReffAttribute {
    width: 400px!important;
}

#fileUploadfundingService .customValidation-tableWrapper .bodyTable-wrapper, #editAttrCustValidation .customValidation-tableWrapper .bodyTable-wrapper {
    max-height: 270px;
    overflow: auto;
}

#fileUploadfundingService .customValidation-tableWrapper td, #fileUploadfundingService .customValidation-tableWrapper th, #editAttrCustValidation .customValidation-tableWrapper td, #editAttrCustValidation .customValidation-tableWrapper th {
    text-overflow: ellipsis;
    overflow: hidden;
}

#fileUploadfundingService .customValidation-tableWrapper td.index-no, #fileUploadfundingService .customValidation-tableWrapper th.index-no, #editAttrCustValidation .customValidation-tableWrapper td.index-no, #editAttrCustValidation .customValidation-tableWrapper th.index-no {
    max-width: 50px;
}

#fileUploadfundingService .customValidation-tableWrapper td.file-name, #editAttrCustValidation .customValidation-tableWrapper td.file-name {
    max-width: 250px;
}

#fileUploadfundingService .customValidation-tableWrapper td.validation-message, #fileUploadfundingService .customValidation-tableWrapper th.validation-message, #editAttrCustValidation .customValidation-tableWrapper td.validation-message, #editAttrCustValidation .customValidation-tableWrapper th.validation-message {
    white-space: pre-line;
}

.btn.btn-clone-template {
    background-image: url("/images/icons/clone-project.png");
    height: 24px;
    width: 44px;
}

.btn.btn-clone-template:hover, .btn.btn-clone-template:focus {
    background-position: 0 0;
    color: #333;
    text-decoration: none;
    transition: none;
}

#editAttributeTable_clone .link-Dynamic-type {
    display: none;
}

#selectFileEditAttributeModal.remove-fade {
    -webkit-transition: opacity .3s linear, top 0s ease-out, bottom 0s ease-out, margin-top 0s ease-out;
    -moz-transition: opacity .5s linear, top 0s ease-out, bottom 0s ease-out, margin-top 0s ease-out;
    -o-transition: opacity .3s linear, top 0s ease-out, bottom 0s ease-out, margin-top 0s ease-out;
    transition: opacity .5s linear, top 0s ease-out, bottom 0s ease-out, margin-top 0s ease-out;
}

.switch {
    position: relative;
    display: inline-block;
    width: 80px;
    height: 28px;
    vertical-align: middle;
    margin-bottom: 0;
}

.switch input {
    display: none;
}

.switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    -webkit-transition: .4s;
    transition: .4s;
    background: #CCC url(/images/unlock-white.png) no-repeat 50px 4px;
    background-size: 18px;
}

.switch .slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 47%;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

.switch input:checked+.slider {
    background: var(--primary-color, #3569AE) url(/images/lock-white.png) no-repeat 10px;
    background-size: 18px;
}

.switch input:focus+.slider {
    box-shadow: 0 0 1px var(--primary-color, #3569AE);
}

.switch input:checked+.slider:before {
    -webkit-transform: translateX(35px);
    -ms-transform: translateX(35px);
    transform: translateX(35px);
}

.switch .slider.round {
    border-radius: 4px;
}

.switch .slider.round:before {
    border-radius: 4px;
}

#modalActivityLock {
    width: 400px;
}

#modalActivityLock .modalActivityLock-wrapper.place-holder #unlock-all, #modalActivityLock .modalActivityLock-wrapper.place-holder #lock-all, #modalActivityLock .modalActivityLock-wrapper.linked-files #unlock-all, #modalActivityLock .modalActivityLock-wrapper.linked-files #lock-all {
    display: none;
}

#modalActivityLock .modal-body {
    min-height: 200px;
}

#modalActivityLock .modalActivityLock-wrapper.place-holder .modal-body, #modalActivityLock .modalActivityLock-wrapper.linked-files .modal-body {
    min-height: 50px;
}

#modalActivityLock .modal-body table {
    margin-top: 5px;
}

#modalActivityLock button.close {
    margin: 0px;
}

#activity-lock-tbody td {
    padding-top: 4px;
    padding-bottom: 4px;
}

.dy-dropdown-wrapper {
    width: 215px;
}

.merge-panel .dy-dropdown-wrapper {
    width: 200px;
    float: left;
    margin-right: 5px;
}

.activity-lock-white-img {
    height: 16px;
    margin: 1px 5px 0 -14px;
    vertical-align: middle;
    float: left;
    padding-left: 7px;
}

.activity-lock-white-span {
    display: inline-block;
    vertical-align: middle;
    margin-left: 0;
}

#model-copyfolderstructure, #model-copyfolderstructure-finalscreen {
    width: 90%;
}

#model-copyfolderstructure-finalscreen div.message {
    margin-bottom: 10px;
    font-size: 13px;
    font-weight: bold;
}

#copyfolderstructure-pageLayoutHeader div.headerTab {
    float: left;
    padding: 8px 16px 12px 8px;
}

#cfstwofooter {
    clear: both;
}

#folderpermission table#tblFldPermission, #tblFldPermission-csfthree {
    font-size: 12px;
    border-right: 1px solid #ccc;
    border-left: 1px solid #ccc;
}

#model-copyfolderstructure #selectfolder li {
    list-style: none;
}

#cfsoneCheckboxPanel label.checkbox, #folderpermissioncheckboxpanel label.checkbox {
    display: inline-block;
    margin-left: 5px;
}

#cfsoneCheckboxPanel label.checkbox {
    float: right;
}

#folderpermissioncheckboxpanel label.checkbox, #cfsoneCheckboxPanel label.checkbox {
    font-size: 12px;
}

#folderpermissioncheckboxpanel label.checkbox {
    float: left;
}

#selectfolder {
    font-size: 12px;
    width: 250px;
    float: left;
    margin-right: 10px;
    margin-top: 10px;
}

#cfsone legend, #cfstwo legend, #cfsthree legend {
    border: none;
    width: auto;
    font-size: 13px;
    line-height: normal;
    margin: 0;
    padding: 0 3px;
    font-weight: bold;
    background: var(--primary-white, #FFFFFF);
    margin-top: -15px;
}

#cfsone #mainContainer {
    height: 400px;
    overflow: auto;
}

#model-copyfolderstructure div.modal-body {
    font-size: 12px;
    padding-top: 10px;
}

#cfsoneCheckboxPanel span, #cfsOneNoteSection label {
    font-size: 12px;
}

#cfsOneNoteSection label {
    margin-bottom: 0;
}

#copyfolderstructure-pageLayoutHeader a {
    font-size: 13px;
}

#folderpermissioncheckboxpanel span, #cfsOneNoteSection label.note {
    font-size: 13px;
    font-weight: bold;
}

#folderpermissioncheckboxpanel span {
    float: left;
}

#cfsone div.modal-footer, #cfstwo div.modal-footer, #cfsthree div.modal-footer {
    padding: 14px 0;
}

#mainContainer ul {
    margin-left: 0;
    clear: both;
}

#selectFolder div {
    float: none;
}

#selectfolder .tree-row {
    height: auto;
    float: none;
    display: block;
    padding: 0;
}

#selectfolder .tree-row * {
    float: none;
}

#selectfolder .closeFolder {
    display: inline-block;
    padding-top: 0;
    vertical-align: middle;
    margin: 0 0 0 1px;
}

#selectfolder .tree-row:hover {
    z-index: 1;
    background: url(../images/icons/middle-bg.png) repeat scroll 0 0!important;
}

#selectfolder .expandChild {
    vertical-align: middle;
}

.trtableheading {
    background: #DDD;
}

#folderpermission {
    overflow: hidden;
    padding-top: 10px;
}

#mainContainer .tree-item>.tree-row {
    font-size: 12px;
    vertical-align: middle;
    background: none!important;
}

#mainContainer .tree-row a.hasChild {
    vertical-align: middle;
}

#mainContainer .tree-row div.folderTitle {
    cursor: pointer;
    display: inline-block;
}

#tblFldPermission td, #tblFldPermission-csfthree td {
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
}

#tblFldPermission td.tdWordWrap span, #tblFldPermission-csfthree td.tdWordWrap span {
    white-space: pre-line;
    word-break: break-all;
    word-wrap: break-word;
    display: inline-block;
    overflow: hidden;
}

#tblFldPermission td input {
    width: 90%;
    padding: 3px 8px;
    font-size: 12px;
}

#tblFldPermission-csfthree tr.tableData.odd {
    background-color: #f9f9f9;
}

#cfstwo div.manage-folders-content {
    height: 400px;
    overflow: auto;
}

#tblFldPermission {
    min-width: 1115px;
}

#tblFldPermission-csfthree {
    min-width: 1395px;
}

#folderPermissionTableDiv {
    height: 400px;
    overflow: auto;
}

#cfsone legend, #cfstwo legend, #cfsthree legend {
    border: none;
    width: auto;
    font-size: 13px;
    line-height: normal;
    margin: 0;
    padding: 0 3px;
    font-weight: bold;
    background: var(--primary-white, #FFFFFF);
    margin-top: -15px;
}

#tblFldPermission td.permission, #tblFldPermission-csfthree td.permission {
    width: 90px;
}

#tblFldPermission td.foldername, #tblFldPermission-csfthree td.foldername {
    width: 180px;
}

#tblFldPermission td.folderpath, #tblFldPermission-csfthree td.folderpath {
    width: 300px;
}

#tblFldPermission td.rolename, #tblFldPermission td.cfsusername, #tblFldPermission-csfthree td.rolename, #tblFldPermission-csfthree td.cfsusername {
    width: 150px;
}

#tblFldPermission td.folderpath span, #tblFldPermission-csfthree td.folderpath {
    width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#cfsSelectDestinationFolderTree #treeView {
    overflow: hidden;
}

#cfsOneNoteSection label.copy-folder-structure-note {
    font-weight: bold;
}

#cfsOneNoteSection label.cfsOneNote {
    color: #F00;
}

#cfsTwoNoteSection label {
    font-size: 12px;
    color: #F00;
}

.loading a.disabled {
    pointer-events: none;
    cursor: default;
}

div#accessHistoryDetails {
    width: 92%;
    position: inherit!important;
}

div#accessHistoryDetails .modal-body {
    padding: 0;
}

div#historyListingWrapper {
    min-height: 400px;
    height: 550px!important;
}

@media only screen and (min-width: 1024px) and (max-width :1366px) {
    div#historyListingWrapper {
        height: 430px!important;
    }
}

div#accessHistoryDetails .manage-selects {
    margin-top: 0;
}

div#accessHistoryDetails .manage-selects span.showLabel {
    display: block;
    float: left;
    font-size: 12px;
    margin-top: 4px;
    line-height: 15px;
    padding: 2px 2px 0 0;
}

div#accessHistoryDetails .manage-selects select#optshow {
    font-size: 12px;
    line-height: 15px;
    float: left;
    margin: 5px 10px 0 5px;
    padding: 1px 5px;
    height: auto;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

div#accessHistoryDetails .manage-selects a, div#accessHistoryDetails .manage-selects .input-medium, div#accessHistoryDetails .manage-selects .customizeParent, div#accessHistoryDetails .manage-selects .configurationContainer {
    display: none!important;
}

label.label-common-show-table{
    display: inline-block;
}

div#accessHistoryDetails a.export-icon:hover {
    cursor: pointer;
}

div#folderAccessList {
    padding-top: 14px;
    max-height: 430px;
}

#folderAccessList td {
    vertical-align: top;
}

.fix-table {
    position: fixed;
    background: var(--primary-white, #FFFFFF);
    z-index: 10;
}

.fix-table table {
    width: 100%;
}

.fix-table th {
    text-align: left;
    padding: 8px;
    font-weight: bold;
    font-size: 12px;
    border-top: 1px solid #d3d3d3;
    border-bottom: 1px solid #d3d3d3;
    vertical-align: top;
}

.td-table strong {
    float: left;
    width: 100px;
}

div#folderAccessList td, div#folderAccessList th {
    padding: 8px;
    background: none;
}

div#folderAccessList .table-bordered th, div#folderAccessList .table-bordered td {
    border-left: none;
}

div#folderAccessList th table th, div#folderAccessList td table td {
    width: 40%;
    border-top: none!important;
}

div#folderAccessList th table th:first-child, div#folderAccessList td table td:first-child {
    width: 20%;
}

div#folderAccessList .table-hover tbody tr:hover>td, div#folderAccessList .table-hover tbody tr:hover>th {
    background: none;
}

div#folderAccessList .edited-by {
    padding-left: 50px;
}

div#folderAccessList .edited-by>div {
    padding-bottom: 5px;
}

div#folderAccessList .table-bordered thead:first-child tr:first-child th {
    border-top: 1px solid #e4eaec;
}

.td-table .divtd {
    padding: 3px 0;
}

.td-table {
    width: 290px;
}

.td-table .div-row {
    display: inline-block;
    width: 135px;
}

table.tablesaw {
    empty-cells: show;
}

.tablesaw {
    width: 100%;
    border-collapse: collapse;
}

.tablesaw {
    padding: 0;
    border: 0;
}

.tablesaw td, .tablesaw th {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: .5em .7em;
}

.table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
    border: 1px solid #e4eaec;
}

.tablesaw td {
    line-height: 1.5em;
}

.tablesaw td, .tablesaw th {
    line-height: 1em;
    text-align: left;
    vertical-align: middle;
}

.tablesaw tbody th, .tablesaw td {
    vertical-align: top;
}

.tablesaw-sortable, .tablesaw-sortable thead, .tablesaw-sortable thead tr, .tablesaw-sortable thead tr th {
    position: relative;
}

.table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
    border-bottom-width: 2px;
}

.table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
    border-bottom-width: 1px;
}

.accessLink {
    line-height: 20px;
    vertical-align: middle;
    display: inline-block;
}

#app-settings-history-modal {
    width: 90%;
    height: 90%;
}

#app-settings-history-modal .modal-body {
    min-height: 200px;
    height: 82%;
    overflow: auto;
}

#app-settings-history-modal .new-xsn-file .xsnFile {
    display: none;
}

#app-settings-history-modal a.export-icon {
    float: right;
}

#app-settings-history-modal .modal-header h3,
#app-settings-history-modal .modal-header .myModalLabel {
    line-height: 26px;
}
.form-label-inline-block{
    display: inline-block;
}
table.tablesaw th {
    font-size: 13px;
}

table.tablesaw td {
    font-size: 12px;
}

.user-data {
    min-width: 190px;
}

.user-data img {
    float: left;
}

.edited-by {
    padding-left: 50px;
}

.edited-by div {
    padding-bottom: 5px;
}

.date-time {
    min-width: 140px;
}

thead .date-time {
    cursor: pointer;
}

.old-val {
    padding: 5px;
    display: inline-block;
    color: #333;
    text-decoration: line-through;
}

.new-val {
    padding: 5px;
    display: inline-block;
    background: rgba(255, 255, 0, 0.41);
    color: #333;
}

a.new-val, a.old-val {
    color: #005884;
    border-bottom: 2px solid #d3d3d3;
    padding-bottom: 5px;
}

#app-settings-history-modal ul {
    list-style: none;
    margin: 0;
}

#app-settings-history-modal ul Li {
    display: inline-block;
    margin-right: 15px;
}

#app-settings-history-modal .table-bordered thead:first-child tr:first-child th {
    border-top: 1px solid #e4eaec;
}

div#historyListingWrapper div.divthead.divtr>div.divth:first-child, div#historyListingWrapper div.divtr.rows>div.divtd:first-child {
    padding-left: 12px;
}

div#workspaceHistoryPagging ul li:hover {
    cursor: pointer;
}

div#workspaceHistoryPagging ul li:first-child img, div#workspaceHistoryPagging ul li:last-child img {
    margin-bottom: 1px;
}

.history_filterCells i.icon-remove-circle.margin-left {
    margin-top: 3px;
}

#myModal-dwonloadErrorFiles .modal-body {
    overflow: auto;
    max-height: 427px;
}

#myModal-nonPublishFiles-batchPrint div.loading.loading-no-record-msg img {
    margin-top: 50px!important;
}

#myModal-printFile div.othersContainer {
    margin-bottom: 10px;
}

div.loading div.loader_new {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color, #3569AE);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    display: inline-block;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.operator-filterCell .ui-widget-content {
    border: 0;
}

.operator-filterCell .ui-widget-content a {
    color: #2d2f2f;
}

.appDesignerContainer {
    height: 500px;
    width: 100%;
    border: none;
}

.ie11 #selectFileEditAttributeModal #freezeFileNameDiv {
    margin-top: 4px;
}

.ie11 #editAttributeTable input[type='text'] {
    height: 16px;
}

.ie11 #freezeFileNameDiv span.text-elipssis {
    padding-top: 11px!important;
}

.ie11 #editAttributeTable td.filename {
    padding-top: 8px!important;
}

#locationMapParent {
    height: 600px;
    width: 600px;
    display: inline-block;
}

#locationMapParent .clearMarker:before {
    content: '';
    display: inline-block;
    margin: 20px 0;
}

#locationMapParent .clearMarker:after {
    content: '';
    display: inline-block;
    margin-right: 20px;
}

#locationMapParent .clearMarker {
    position: absolute;
    top: 0;
    right: 0;
}

button.locationButton, button.locationButtonNavigator {
    margin-left: 5px;
    padding: 3px;
    background: #eee url(/images/icons/pin_location.png) no-repeat -2px 4px;
    width: 33px;
    height: 33px;
    border-radius: 5px;
    border: solid 1px #ccc;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

button.locationButton:hover {
    border: solid 1px #ccc;
    -moz-box-shadow: 1px 1px 5px #999;
    -webkit-box-shadow: 1px 1px 5px #999;
    box-shadow: 1px 1px 5px #999;
}

#locationMapParent button#clearMapMarker {
    margin-right: 5px;
}

#locationMapParent button.mapButtons {
    box-shadow: rgba(0, 0, 0, 0.3) 0 1px 4px -1px;
    border: none;
    background: white;
    font-size: 11px;
    padding: 4px;
    border-radius: 2px;
}

.beta-view.close.view {
    font-size: 14px;
    margin-right: 20px;
    padding: 2px 10px;
    border: 1px solid var(--primary-white, #FFFFFF);
    text-shadow: none;
    border-radius: 4px;
}

.beta-view.close.view:hover, .beta-view.close.view:active, .beta-view.close.view:focus {
    color: white;
}

.metadata-table {
    width: 100%;
    min-width: 150px;
}

#folderAccessList .table-bordered .metadata-table tr td {
    border-left: none!important;
    padding-top: 3px;
    padding-bottom: 3px;
}

#folderAccessList .table-bordered .metadata-table tr td:first-child {
    border-left: none!important;
    padding: 3px 0;
}

#status-history-container {
    clear: both;
}

#myModal-viewFolderPermission .is-Template .accessLink {
    display: none;
}

#myModal-viewFolderPermission .accessLink {
    padding: 5px;
}

#myModal-viewFolderPermission a:focus {
    outline: auto;
}

.is-template>div:first-child div, .is-template>div:last-child {
    visibility: hidden;
}

.is-form-template #appListingHeader>div, .is-form-template #commuDoclist, .is-form-template>div:last-child {
    visibility: hidden;
}

div.locationcustomAttribute {
    position: relative;
}

div.locationInputOverLay {
    position: absolute;
    display: inline-block;
    height: 34px;
    width: 220px;
    top: 0;
    left: 0;
}

.btn-danger {
    background-color: var(--primary-btn-bg, #3569AE);
    color: var(--primary-btn-text, #FFFFFF);
    border-radius: var(--primary-btn-border-radious);
    background-image: none;
    border-color: transparent;
}

.btn-danger:hover {
    background-color: var(--primary-btn-hover-bg, #335D93);
    color: var(--primary-btn-text, #FFFFFF);
    background-image: none;
    border-color: transparent;
    outline: none;
    box-shadow: none;
}

.btn-danger:focus, .btn-danger:active{
    background-color: var(--primary-btn-focus-bg, #244674);
    color: var(--primary-btn-text, #FFFFFF);
    outline: none;
    box-shadow: none;
}

.btn-danger[disabled], .btn-danger.btn-disabled {
    background-color: var(--primary-btn-disable-bg, #E0E0E0);
    color: var(--primary-btn-disable-text, #757575);
}

#biDirectionAssociationModal .btn-danger.btn-disabled {
    background-color: var(--primary-btn-disable-bg, #E0E0E0) !important;
    color: var(--primary-btn-disable-text, #757575);
}

.btn-secondary {
    color: var(--secondary-btn-text, #3569AE);
    background-color: var(--secondary-btn-bg, #FFFFFF);
    border: var(--secondary-btn-border);
    border-radius: var(--secondary-btn-border-radious);
    background-image: none;
}

.btn-secondary:hover {
    background-color: var(--secondary-btn-hover-bg, #EEF7FF);
    color: var(--secondary-btn-hover-text, #335D93);
    border: var(--secondary-btn-hover-border);
    outline: none;
    box-shadow: none;
}

.btn-secondary:focus, .btn-secondary:active{
    background-color: var(--secondary-btn-focus-bg, #D4EAFF);
    color: var(--secondary-btn-focus-text, #244674);
    border: var(--secondary-btn-focus-border);
    outline: none;
    box-shadow: none;
}

.btn-secondary[disabled] {
    background-color: var(--secondary-btn-disable-bg);
    color: var(--secondary-btn-disable-text);
    border: var(--secondary-btn-disable-border);
}

.btn-tertiary {
    color: var(--tertiary-btn-text, #3569AE);
    background-color: var(--tertiary-btn-bg);
    border-radius: var(--tertiary-btn-border-radious);
    background-image: none;
    border: none;
    text-shadow: none;
}

.btn-tertiary:hover {
    background-color: var(--tertiary-btn-hover-bg, #EEF7FF);
    color: var(--tertiary-btn-hover-text, #335D93);
    outline: none;
    box-shadow: none;
}

.btn-tertiary:focus, .btn-tertiary:active{
    background-color: var(--tertiary-btn-focus-bg, #D4EAFF);
    color: var(--tertiary-btn-focus-text, #244674);
    outline: none;
    box-shadow: none;
}

.btn-tertiary[disabled] {
    color: var(--tertiary-btn-disable-text, #757575);
    background-color: var(--tertiary-btn-bg);
}

#modalOrgLayoutBranding.modal {
    width: 52%;
}

#modalOrgLayoutBranding .bgColorPicker {
    margin: 0;
    border: 0;
    padding: 0;
    width: 80px;
    height: 36px;
    border-right: 60px solid var(--primary-color, #3569AE);
    line-height: 20px;
    padding-left: 5px;
    cursor: pointer;
}

#modalOrgLayoutBranding .modal-body {
    max-height: 650px;
    overflow: auto!important;
}

#modalOrgLayoutBranding #brandingBody {
    width: 100%;
}

#modalOrgLayoutBranding .brangingBorder {
    width: 100%;
    border: 1px solid #908d8d;
    border-radius: 5px;
}

#brandingMainContainer {
    position: relative;
}

#brandingMainContainer .brangingSubSection {
    float: left;
    border: 1px solid #c3c3c3;
    border-radius: 5px;
    width: 100%;
    position: relative;
}

#brandingMainContainer .subSectionLabel {
    top: -10px;
    left: 15px;
}

#brandingMainContainer .bgColorSection {
    padding-top: 10px;
}

#modalOrgLayoutBranding .form-horizontal .control-label {
    text-align: left;
}

#modalOrgLayoutBranding .bgColorSection .controls {
    margin-left: 0;
}

#brandingMainContainer .url-section {
    margin-top: 25px;
}

#brandingMainContainer .customLandingPageFlag-section {
    margin-top: 15px;
}

#brandingMainContainer .customLandingPageFlag-section .control-group{
    padding: 10px;
    display: flex;
    align-items: center;
    gap: 5px;
}

#brandingMainContainer .customLandingPageFlag-section .control-group input,
#brandingMainContainer .customLandingPageFlag-section .control-group label{
    padding: 0;
    margin: 0;
}

#brandingMainContainer .customLandingPageFlag-section .control-group input{
    margin-bottom: 1px;
}

#brandingMainContainer .customLandingPageFlag-section .control-group label{
    line-height: 22px;
}

#modalOrgLayoutBranding .logo-section .controls {
    margin-left: 15px;
    margin-top: 20px;
}

#modalOrgLayoutBranding .url-section .controls {
    margin-top: 10px;
}

#modalOrgLayoutBranding .modal-body {
    max-height: 650px;
    overflow: auto!important;
}

#modalOrgLayoutBranding .control-group:last-child {
    margin-bottom: 0;
}

#modalOrgLayoutBranding .logo-section .infoIcon {
    width: 25px;
    margin-bottom: -10px;
}

#editAttributeModelBody .merge-panel div.locationInputOverLay {
    width: 75%;
    padding: 0 6px;
    border: 2px solid transparent;
}

#addAppModal .wrapper {
    margin: 0;
}

#modalOrgLayoutBranding .logo-section .select-change-logo {
    margin-bottom: 10px;
    margin-left: 3px;
}

#modalOrgLayoutBranding .logo-section .tipText {
    color: red;
    font-size: 12px;
    margin-bottom: 10px;
    clear: both;
}

#brandingMainContainer #removeImage {
    margin-bottom: 10px;
}

#brandingMainContainer .urlsection, #brandingMainContainer .urltooltipsection {
    float: left;
    margin-right: 10px;
}

#brandingMainContainer .url-section .mar-left0 {
    margin-left: 7px;
}

.branding-layout-enabled {
    font-size: 15px!important;
    color: #84a6c6;
}

.branding-layout-disabled {
    font-size: 15px!important;
    cursor: default;
}

.url-section .select-change-logo {
    margin-left: 10px;
    line-height: 23px;
}

.urlsection .link-remove-img {
    line-height: 23px;
    margin-left: 5px;
}

.url-section .link-remove-img {
    line-height: 23px;
    margin-left: 7px;
}

.add-remove-header-link {
    float: right;
    margin-top: 5px;
    cursor: pointer;
}

#brandingMainContainer #add-link1 {
    margin-right: 33px;
}

#brandingMainContainer .add-link {
    color: green;
    font-size: 19px;
    margin-left: 15px;
    cursor: pointer;
}

#brandingMainContainer .remove-link {
    color: #f10e0e;
    margin-left: 15px;
    font-size: 19px;
    cursor: pointer;
}

#brandingMainContainer #friUrlDomain {
    font-size: 12px;
}

#brandingMainContainer #copyFriendlyUrlLink {
    margin-left: 5px;
}

#brandingMainContainer .friUrlSection {
    padding: 20px 0 10px 15px;
}

#brandingMainContainer .brd-pt5 {
    padding-top: 5px;
}

#brandingMainContainer .brd-mt20 {
    margin-top: 20px;
}

#brandingMainContainer .brd-pb20 {
    padding-bottom: 20px;
}

#brandingMainContainer .colorDetailSettings {
    display: inline-block;
    margin-bottom: 20px;
    margin-left: 15px;
    width: 170px;
}

div.dashboardColumnChart .highcharts-data-label {
    cursor: default!important;
}

div.dashboardColumnChart .highcharts-data-label tspan {
    font-weight: normal!important;
}

.emptyWidgetBox {
    text-align: center;
    font-weight: bold;
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    top: 42%;
    bottom: 0;
}

div.emptyWidgetWrapperDiv span {
    font-size: 30px;
    display: block;
}

.no-pointer {
    cursor: default;
}

#draftNote {
    color: red;
    margin-left: 20px;
}

.formGroupTitle {
    overflow: hidden;
    text-overflow: ellipsis;
    width: 225px;
    display: inline-block;
    white-space: nowrap;
}

div#sectionDueTodayActions {
    margin-top: 10px;
}

#creatFormModal.ipadAttachAndAssocModal {
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}

#roleFormPermissionsMainDiv, #manageRolesContainer {
    background-color: white;
}

.update-activation {
    position: absolute;
    width: 480px;
    left: 50%;
    margin-left: -240px;
    background: var(--primary-white, #FFFFFF);
    padding: 15px;
    box-shadow: 0 1px 5px 0 rgba(45, 62, 80, .12);
    top: 20%;
}

.update-activation>img {
    margin-left: -5px;
}

.update-activation>h3 {
    display: none;
}

.update-activation>p {
    line-height: 20px;
    font-weight: bold;
    margin-top: 25px;
}

.update-activation>p>a {
    color: black;
    cursor: text;
}

.update-activation>div {
    margin-top: 10px;
}

.update-activation>div>a {
    font-weight: bold;
}

.update-activation>div>ul {
    list-style: none;
    padding: 5px 0;
    margin: 10px 0 0 0;
}

.update-activation>div>ul>li>span:first-child {
    font-weight: 600;
    margin-left: 0;
    width: 100px;
    display: inline-block;
}

#my-nav li {
    vertical-align: top;
}

#my-nav>ul>li:not(:last-child) {
    margin-right: 10px;
}

#header_moreNav {
    color: var(--primary-black, #000000);
    display: block;
    font-size: 14px;
    text-align: center;
    width: auto;
    border: none;
    padding: 4px 26px 4px 8px;  
    border-radius: 10px;
    margin: 4px;
}

#header_moreNav:hover, #header_moreNav:focus{
    background-color: var(--primary-hover-gray, #F1F2F5);
    color: var(--primary-color, #3569AE);
    }
    
#header_moreNav.drop-arrow:hover::after, #header_moreNav.drop-arrow:focus::after{
    border-color: var(--primary-color, #3569AE) !important;
}

#header_moreNav+.dropdown-menu {
    top: 100%;
    border-radius: 0;
    z-index: 10000;
    padding: 0;
    margin: 0;
    position: relative;
    left: auto;
    right: 35px;
}

#header_moreNav+.dropdown-menu li {
    padding: 0;
}

#header_moreNav+.dropdown-menu li>a {
    padding: 5px 25px 5px 20px;
}

.app-id-2 #fileViewer #discussionHeaderIcons .view {
    display: none;
}

#sidenav-discuss-unread {
    letter-spacing: -0.46px;
}

#manageAppsModal .fullwidth-box-top .showLabel, #manageAppsModal .fullwidth-box-top #optshow, #manageAppsPagingDiv {
    display: none;
}

#docListingSection.fileListContent a.download {
    display: inline-block;
}

#docListingSection.transmittalsListContent a.download, #docListingSection.discussionsListContent a.download {
    display: none;
}

#docListingSection.transmittalsListContent select#optshow, #docListingSection.transmittalsListContent span.showLabel, #docListingSection.transmittalsListContent .pagination, #docListingSection.transmittalsMainContent select#optshow, #docListingSection.transmittalsMainContent span.showLabel, #docListingSection.transmittalsMainContent .pagination, #commuContentContainerShowMore select#optshow, #commuContentContainerShowMore span.showLabel, #commuContentContainerShowMore .pagination, #fileContentContainerShowMore select#optshow, #fileContentContainerShowMore span.showLabel, #fileContentContainerShowMore .pagination, #commuContentContainer select#optshow, #commuContentContainer span.showLabel, #commuContentContainer .pagination, #fileContentContainer select#optshow, #fileContentContainer span.showLabel, #fileContentContainer .pagination {
    display: none;
}

#docListingSection.fileListContent select#optshow, #docListingSection.fileListContent span.showLabel, #docListingSection.fileListContent .pagination, #docListingSection.discussionsListContent select#optshow, #docListingSection.discussionsListContent span.showLabel, #docListingSection.discussionsListContent .pagination, #docListingSection.appsMainContent select#optshow, #docListingSection.appsMainContent span.showLabel, #docListingSection.appsMainContent .pagination {
    display: inline-block;
}

.appsMainContent .filterCell {
    margin-right: 0px;
}

#fileActiveListType {
    width: 96%;
}

.is-template #fileActiveListType {
    display: none;
}

#docListingSection.transmittalsMainContent #myModal-createform-top {
    display: none;
}

.btn.btn-outline {
    background: transparent;
    border-color: #B9B7B8;
    box-shadow: none;
}

.adoddle-btn-group .btn.dropdown-toggle {
    color: #727372;
    padding: 4px 12px;
    min-height: 16px;
    z-index: 0;
    text-shadow: none;
    font-size: 13px;
    line-height: normal;
}

.adoddle-btn-group .caret {
    border-top: 4px solid #727372;
    float: none;
    display: inline-block;
    margin-left: 5px;
    margin-top: 4px;
}

.adoddle-opt-show {
    color: #727372;
    background: transparent;
    border-color: #B9B7B8;
    padding: 4px 4px;
    line-height: 20px;
    height: auto;
    margin-bottom: 0;
    margin-right: 10px;
}

.sel-btn-group {
    position: relative;
    display: inline-block;
    white-space: nowrap;
    vertical-align: middle;
}

.manage-selects .adoddle-show-label {
    line-height: 30px;
    margin-right: 2px;
}

.switch-view {
    color: #727372;
    font-size: 18px;
    padding: 5px 0;
    line-height: 25px;
    height: 20px;
    margin-left: 5px;
    vertical-align: middle;
}

.switch-view.active {
    color: #c1c1c1d9;
}

a.switch-view:hover, a.resize-view:hover {
    color: #727372;
}

.modal a.switch-view:hover, .modal a.resize-view:hover {
    color: var(--primary-white, #FFFFFF);
}

.modal .manage-toolbar-header .switch-view {
    color: #727272;
}

.modal .manage-toolbar-header .switch-view:hover {
    color: #727272;
}

.modal .manage-toolbar-header .switch-view.active {
    color: #c1c1c1d9;
}

.resize-view {
    font-size: 18px;
    line-height: 25px;
    height: 20px;
    margin-left: 5px;
    color: #727372;
}

.listing-setting .resize-view {
    color: #727372;
    vertical-align: middle;
    margin-left: 10px;
}

.listing-setting a.resize-view:hover {
    color: #727372;
}

.filter-save-dropdown {
    margin: 0 0 0 0!important;
    display: inline-block;
}

.filter-save-dropdown .filter-dropdown-menu a {
    padding: 5px 10px;
}

.filter-save-dropdown .filter-dropdown-menu a i {
    font-size: 16px;
}

.filter-save-dropdown .btn {
    padding: 2px 10px;
    height: 26px;
}

.filter-save-dropdown .btn.dropdown-toggle {
    padding: 2px 6px;
    line-height: 16px;
}

.filter-btn-container>div {
    float: none;
    display: inline-block;
    vertical-align: middle;
}

.filter-btn-container>.display-inline-class{
    display: inline-block !important;
}

.filter-btn-container {
    position: absolute;
    top: 3px;
    background: transparent;
    right: 47px;
    height: 0;
    z-index: 3;
    padding: 0;
}

.manage-toolbar-header .btn-group.open .btn.dropdown-toggle {
    box-shadow: none;
    background-color: transparent;
}

.restored .fa-window-minimize, .maximized .fa-window-maximize {
    display: none;
}

.restored .fa-window-maximize, .maximized .fa-window-minimize {
    display: inline-block;
}

.sublisting-type {
    padding: 0;
    margin: 0;
}

.sublisting-type li {
    display: inline-block;
    text-align: center;
}

.sublisting-type li a {
    font-size: 12px;
    color: var(--primary-black, #000000);
    padding: 5px 15px;
    display: block;
    text-align: center;
    border-bottom: 3px solid transparent;
    min-width: 70px;
}

.sublisting-type li a:hover, .sublisting-type li a:focus, .sublisting-type li a.active {
    font-weight: bold;
    border-bottom-color: var(--primary-color, #3569AE);
}

.document-main-body .close-button-wrapper .helpcontent:focus-visible {
    color: var(--primary-black, #000000);
}

div.role-privilage-info {
    position: relative;
}

.btn-group.open .manage-toolbar-header .btn.dropdown-toggle {
    background-color: transparent;
    box-shadow: none;
}

.modal .width-padded.filter-placeholder>div {
    display: inline-block;
    margin: 0;
    vertical-align: middle;
}

.modal .listing-setting {
    margin-right: 5px!important;
}

.tabMoreOptions+.dropdown-menu, .tabMoreOptions .dropdown-menu {
    max-height: 67vh;
    overflow: auto;
}

.modal .manage-files-content {
    border: 1px solid #eeeced;
    box-sizing: border-box;
}

#myModal-widget-icons [class*=" icon-"] {
    background-image: url(/images/glyphicons-halflings-white.png);
}

@media(max-width: 1366px) {
    .filter-btn-container {
        position: static;
        background: #eeeced;
        height: auto;
    }
}

div.info-message {
    position: absolute;
    right: 5px;
    top: 130%;
    background: white;
    padding: 10px;
    width: 450px;
    z-index: 200;
    box-shadow: 1px 3px 5px #555;
    display: none;
}

div.info-message div.triangle {
    height: 0;
    width: 0;
    position: absolute;
    border-bottom: 10px solid var(--primary-white, #FFFFFF);
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    top: -10px;
    right: 0;
}

.history-header-option a {
    color: var(--primary-white, #FFFFFF);
    display: inline-block;
    vertical-align: middle;
    line-height: 30px;
}

div.info-message.showInfo {
    display: block;
}

div.info-message p {
    margin-top: 15px;
}

.dropdown-menu-left {
    right: auto;
    left: 0;
}

.dropdown-menu-right {
    right: 0;
    left: auto;
}

div.info-message i.info-message-close {
    position: absolute;
    top: -1px;
    right: 0;
    font-size: 25px;
}

#delegateActionForm .delegateAccessMsg {
    font-size: 12px;
    margin-bottom: 5px;
}

#dashboard-help {
    width: 260px;
    padding: 0px 14px;
    max-height: 402px;
}

#dashboard-help table {
    left: 0px;
    top: 16px;
}

#dashboard-help table tr {
    height: 331px;
    padding-top: 5px;
}

#adTableBody .inactive-status {
    cursor: none;
}

#adTableBody .active-status {
    cursor: pointer;
}

#manageAppsMainDiv #listing .divtr:has(input[type="checkbox"]:focus)  {
    background: var(--primary-hover-gray, #F1F2F5);
}

#manageAppsModal .helpbrand.helpcontent, #manageAppsModal .manage-toolbar-header.dropdown-menu a,
#manageAppsModal .manage-toolbar-header.dropdown-menu select,
#editAppsModal .controls .radio input[type="radio"],
#editAppsModal .control-group input[type="checkbox"],
#editAppsModal .controls input[type="checkbox"],
#editAppsModal .controls input[type="file"],
#editAppsModal .controls select {
    outline: revert;
}

/* customize dropdown css releated accessibility */
.listing-setting .manage-toolbar-header.dropdown-menu select,
.listing-setting .manage-toolbar-header.dropdown-menu a{
    outline: revert;
}
.listing-setting .manage-toolbar-header .btn-group.open .dropdown-menu-right{
    outline: none;
}


@media(max-width: 880px) {
    /* #my-nav .nav>li>a {
        width: 80px;
    } */
}

.ipad #viewAssocCommentHeaderTable .accordion-header {
    font-size: 10px!important;
}

.hide-dotted-border {
    border-top: none;
}

.popover.dist-user-list {
    max-width: 250px !important;
}

.popover.dist-user-list .transparent-paading-row {
    position: absolute;
    width: 100%;
    height: 16px;
    left: 0px;
    bottom: -14px;
}

.popover.dist-user-list .dist-user-list-loading {
    color: #7b7b7b;
    text-align: center;
}

.popover.dist-user-list .dist-user-list-no-records {
    color: #7b7b7b;
    text-align: center;
}

.popover.dist-user-list .dist-user-list-container ul {
    list-style: none;
    padding-right: 12px;
    margin-left: 12px;
}

.popover.dist-user-list .dist-user-list-container ul li {
    border-bottom: 1px solid #ddd;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    height: 22px;
    line-height: 22px;
    box-sizing: content-box;
    padding: 1px 2px;
    font-size: 12px;
    vertical-align: middle;
}

.popover.dist-user-list .dist-user-list-container ul li:first-child {
    font-weight: 700;
}

#recentFormContainer {
    min-height: 300px;
    max-height: 400px;
    overflow: auto;
}

#recentFormContainer ul {
    list-style: none;
}
#recentFormContainer ul li{
    vertical-align: top;
    display: inline-block;
    text-align: center;
    margin-right: 30px;
    margin-bottom: 15px;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

#recentFormContainer .form-code {
    display: inline-block;
    float: none;
    height: 65px;
    clear: both;
}

#recentFormContainer .form-code .name {
    padding: 0 5px;
    border: 1px solid #bec0cc;
    width: 100%;
    line-height: 21px;
}

#recentFormContainer .form-code .form-icon {
    width: 100%;
}

#recentFormContainer .form-code i {
    font-size: 20px;
    color: #bfbfcb;
    margin: 10px 0;
}

#recentFormContainer .form-title {
    margin-left: 10px;
    text-overflow: ellipsis;
    width: 80px;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
}

#recentFormModal {
    width: 690px;
}

#recentFormModal input#recentAppSearch {
    float: right;
    margin-right: 10px;
    height: 15px;
    margin-bottom: 0px;
}

#recentFormContainer .separator {
    display: flex;
    align-items: center;
    text-align: center;
    margin-bottom: 15px;
}

#recentFormContainer .separator span {
    color: var(--primary-color, #3569AE);
    margin: 0px 10px;
}

#recentFormContainer .separator::before, .separator::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid var(--primary-color, #3569AE);
}

#recentFormContainer .separator::before {
    margin-right: .25em;
}

#recentFormContainer .separator::after {
    margin-left: .25em;
}

.cursor-pointer {
    cursor: pointer
}

#myModal-addsite .fa-file-excel-o {
    color: var(--primary-white, #FFFFFF);
    font-size: 12px;
    margin-top: 7px;
}

#myModal-addsite .excel-download-icon .fa-long-arrow-down,
#myModal-addsite .excel-import-icon  .fa-long-arrow-up {
    display: inline-block;
    height: 9px;
    overflow: hidden;
    position: relative;
    top: 3px;
    left: -2px;
    color: var(--primary-white, #FFFFFF);
}

#myModal-addsite .excel-download-icon .fa-long-arrow-down {
    top: 4px;
    line-height: 3px;
}

ul.targetFoldersPathList {
    list-style: none;
    margin: 0px 0px 10px 0px;
    max-height: 100px;
    overflow-x: auto;
}

ul.targetFoldersPathList li {
    font-weight: bold;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

ul.targetFoldersPathList li.deactivated {
    text-decoration: line-through;
}
#myModal-status-export-top .aLoader.small.hide{
    display: none;
}
#myModal-status-export-top.btn-disabled .aLoader.small {
   height: 11px;
    width: 11px;
    margin-left: 5px;
}
#myModal-status-export-top.btn-disabled .aLoader.small:before {
	height: 7px;
    width: 7px;
}
#myModal-status-export-top.btn-disabled .aLoader.small:after {
    background-color: transparent;
}
.fullwidth-box-top .dropdown-menu.filterui-list-section.btn-disabled{
    display:none;
}
#manageDistExportTop .aLoader.small.hide{
    display: none;
}
#manageDistExportTop.btn-disabled .aLoader.small {
   height: 11px;
    width: 11px;
    margin-left: 5px;
}
#manageDistExportTop.btn-disabled .aLoader.small:before {
	height: 7px;
    width: 7px;
}
#manageDistExportTop.btn-disabled .aLoader.small:after {
    background-color: transparent;
}

#exportMailBoxResult .aLoader.small.hide{
    display: none;
}
#exportMailBoxResult.btn-disabled .aLoader.small {
   height: 11px;
    width: 11px;
    margin-left: 5px;
}
#exportMailBoxResult.btn-disabled .aLoader.small:before {
	height: 7px;
    width: 7px;
}
#exportMailBoxResult.btn-disabled .aLoader.small:after {
    background-color: transparent;
}
#exportMailBoxResult{
    margin-top: 3px !important;
}
#manageMailBox_fd_containText {
    margin-right: 15px;
}
#workspaceHistoryExport a.btn-disabled .aLoader.small {
    height: 11px;
     width: 11px;
     margin-left: 5px;
 }
#workspaceHistoryExport a.btn-disabled .aLoader.small:before {
	height: 7px;
    width: 7px;
}
#workspaceHistoryExport a.btn-disabled .aLoader.small:after {
    background-color: transparent;
}
#workspaceHistoryExport .aLoader.small.hide{
    display: none;
}

#inviteUsersModal{
    font-size: 13px;
    width: 100%;
    height: 100%;
    margin: 0 !important;
    top: 0px !important;
    left: 0px !important;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    border: none;
}

#inviteUsersModal .modal-body{
    padding: 0;
    height: 100% !important;
}

#inviteUsersModal .container-fluid{
    height: 100%;
    overflow: hidden;
}

#inviteUsersModal .container-fluid .row-fluid{
    height: 100%;
}

#inviteUsersModal .container-fluid .row-fluid #inviteUsersSection{
    height: 100%;
}

#inviteUsersModal .fullwidth-box-top .close img{
    height: 12px;
}

#inviteUsersModal .content{
    height: 100%;
    background: var(--primary-white);
    overflow: auto;
}

#inviteUsersModal .content .form-action-wrapper{
    border-bottom: 2px solid var(--disable-color);
    padding: 10px 15px;
}

#inviteUsersModal .content #inviteUsersForm{
    padding: 20px 0;
}

#inviteUsersModal .content #addUserInvitationBtn{
    display: flex;
    align-items: center;
    gap: 5px;
}

#inviteUsersModal .content .removeInvitationBtn svg{
    fill: var(--disable-text-color);
    stroke: var(--disable-text-color);
}

#inviteUsersModal .content .removeInvitationBtn svg:hover{
    fill: #F44336;
    stroke: #F44336;
}

#inviteUsersModal .content .modal-footer {
    border-top: 2px solid var(--disable-color);
    margin-top: 10px;
    border-radius: 0px;
    box-shadow: none;
}

.no-user-found {
    border-left: 4px solid #f44336;
    width: 99%;
    font-size: 14px !important;
    color: var(--primary-black, #000000) !important;
    background-color: var(--primary-white, #FFFFFF) !important;
    margin-left: -4px;
    margin: -4px 0 -4px -4px;
}

.no-user-found i {
   color: #f44336;
   font-size: 18px;
   font-weight: 500;
   padding: 16px 12px 12px 16px;
}

.no-user-found span {
    padding: 16px 8px 8px 4px;
}

.no-user-found a {
    cursor: pointer;
    text-decoration: underline;
    font-weight: 400;
    display: block;
    padding: 4px 4px 4px 0px;
    margin-left: 46px;
}

.btn.header-btn {
    border-radius: 8px !important;
    border-color: #E9E9E9 !important;
    font-size: 14px;
    font-family: 'Sofia Pro';
    padding: 4.5px 12px;
    line-height: normal;
    background: var(--primary-white, #ffffff) !important;
    color: var(--primary-black, #000000) !important;
}
.btn.header-btn .iconoir-nav-arrow-down {
    font-size: 17px;
    margin-left: 4px;
}
.btn.header-btn:hover:active, .btn.header-btn:hover, .btn.header-btn:active, .btn.header-btn:focus, .btn.header-btn.focus {
    background-color: var(--primary-hover-gray, #F1F2F5) !important;
    color: var(--primary-color, #3569AE) !important;
    border-color: var(--primary-hover-gray, #F1F2F5) !important;
    box-shadow: none;
}
.btn.header-btn:disabled, .btn.header-btn:disabled:hover:active, .btn.header-btn:disabled:focus, .btn.header-btn:disabled:hover, .btn.header-btn:disabled:active {
    color: var(--secondary-btn-disable-text) !important;
    background: var(--secondary-btn-disable-bg) !important;
    border-color: #E9E9E9 !important;
    cursor: not-allowed !important;
}
.popover .popover-title a:focus-visible{
    outline: auto;
}
.popover .popover-title a:focus-visible{
    outline: auto;
}


.dynamic-link-attention-box * {
    font-size: 14px;
}

.dynamic-link-attention-box .promt-divider {
    border-top: 1px solid #cecbcb;
    margin: 16px 0px;
}

.dynamic-link-attention-box .ack {
    margin-top: 16px;
}

.dynamic-link-attention-box .ack input[type=checkbox] {
    margin: 0;
    margin-right: 4px;
    margin-bottom: 1px;
}

.workflow-trigger-issue-confirm-popup ul {
    margin-left: 0px;
    padding-left: 20px;
    max-height: 200px;
    overflow: hidden;
    overflow-y: scroll;
}



.new-download-file-css .add-folder-button {
    padding: 0px !important;
}

.new-download-file-css .add-folder-button.download-instructions {
    padding: 20px 40px !important;
    border-bottom: 1px solid #EEEEEE;
}


.new-download-file-css .modal-body {
    float: unset;
    margin: 0;
    padding: 19px 40px;
    max-height: calc(100vh - 245px);
    overflow: hidden;
    overflow-y: auto;
}

.new-download-file-css .ndfc-element {
    margin-bottom: 12px;
    position: relative;
}

.new-download-file-css .ndfc-element input[type=checkbox]{
    margin: 0px 8px 0px 0px !important;
    padding: 0;
    display: inline-block;
    min-height: 0;
    height: 18px;
    width: 18px;
    border-radius: 4px;
    border: 2px solid #9E9E9E;
    cursor: pointer;
    accent-color: var(--primary-color, #3569AE);
}

.new-download-file-css .ndfc-element label {
    font-size: 14px;
    line-height: 14px;
    font-weight: 400;
    margin: 1px 0px 0px 0px !important;
}

.new-download-file-css .ndfc-element .downloadInfoNote {
    top: -4px;
}

.new-download-file-css .ndfc-comment {
    padding: 0px !important;
    position: relative;
}

.new-download-file-css .ndfc-comment .inner-download-modal{
    position: absolute;
    top: -4px;
}

.new-download-file-css .ndfc-comment label{
    padding: 0;
    margin: 0;
    margin-bottom: 12px;
}

.new-download-file-css .ndfc-comment label strong,
.new-download-file-css .modal-body .ndfc-label strong {
    font-size: 16px;
    line-height: 16px;    
}

.new-download-file-css .modal-body .ndfc-label {
    margin-bottom: 12px;
}

.new-download-file-css .form-dotted-line {
    border: 1px solid #EEEEEE !important;
    margin: 8px 0px 20px 0!important;
}

.new-download-file-css .warning {
    background: #FFF5E7;
    border: 1px solid #974801;
    border-radius: 4px;
    padding: 16px;
    font-size: 14px;
    line-height: 14px;
    color: #974801;
    margin: 0px !important;
}

#adminTab_model{
    width: 100%;
    height: 100%;
    top: 0 !important;
    left: 0 !important;
    margin-left: 0 !important;
    margin-top: 0 !important;
    border-radius: 0;
    border: none;
    overflow: hidden;
}

#adminTab_model .modal-body{
    height: 100%;
    width: 100%;
}

.noscroll-noborder #wrap app-root{  
    border-top-width: 0;
    border-bottom: 0 ;
    overflow: hidden;
}

#adminTab_model .modal-header button.close{
    width: auto !important;
}   

#adminTab_model .aLoader{
    min-height: 100%;
}

#showAdminTab{
    height: 100%;
    width: 100%;
}

.new-download-file-css .warning strong{
    font-size: 16px;
    line-height: 16px;
    font-weight: 600;
    display: block;
    margin-bottom: 12px;
}

.new-download-file-css .warning p{
    margin: 0px;
}

.new-download-file-css .mb-12 {
    margin-bottom: 12px !important;
}

.new-download-file-css .modal-footer {
    padding: 12px 40px 40px 40px;
}

.new-download-file-css .modal-footer .btn {
    font-size: 14px;
    line-height: 14px;
    padding: 12px 16px;
    border-radius: 4px;
}

.new-download-file-css .modal-footer .btn-danger {
    margin-left: 12px;
}

.new-download-file-css .inner-download-modal {
    position: absolute;
    top: -4px;
}

.new-download-file-css .areYouSureDownText {
    padding: 0 !important;
    font-size: 14px;
    line-height: 14px;
    margin-bottom: 12px;
}

.new-download-file-css.font-sans-serif {
    font-family: sans-serif;
}
    
#workflow-not-publish-modal .workflow-not-publish-modal-body {
    overflow: auto; 
    max-height: 60vh;
    padding: 10px;
}
#workflow-not-publish-modal .text-elipssis{
    float:none;
}
  
#workflow-not-publish-modal .workflow-not-publish-modal-body p {
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: baseline;
}
  
#workflow-not-publish-modal .workflow-not-publish-modal-body .copy-to-clipboard {
    flex: 0 0 auto;
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
    margin-left: 10px;
}

#workflow-not-publish-modal .workflow-not-publish-modal-body table {
    width: 100%;
    margin-bottom: 15px;
}
  
#workflow-not-publish-modal .workflow-not-publish-modal-body table tr {
    border-bottom: 1px solid #ddd;
}
  
#workflow-not-publish-modal .workflow-not-publish-modal-body table tr.thead {
    background-color: #fff;
}
  
#workflow-not-publish-modal .workflow-not-publish-modal-body table tr th,
#workflow-not-publish-modal .workflow-not-publish-modal-body table tr td {
    padding: 7px 10px;
}