.ibox-header .header-action .search-inpt {
    margin-right: 3px;
}

h1 {
    margin: 4px 7px 4px 0 !important;
    font-size: 15px;
    font-weight: 700;
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
}

.header-action {
    &:not(.mobile) {
        min-width: 400px;
    }
}

.rev-select {
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    max-width: 53px;
}

.dist-history-action.show {
    display: inline-block !important;
}

.disabled {
    pointer-events: none;
}
 
.dist-history-action .history-dist-dropdown .dropdown-item {
    padding: 0;    
}

.loading.small{
    min-width: 20px !important;
}

.delegate-controls{
    background: var(--primary-white, #ffffff);
    padding: 10px;

    .text-right .btn{
        margin-right: 5px;
    }
}

.history-container{
    padding-top: 0px !important;
    max-height: 90vh;
    background-color: var(--primary-white, #ffffff);

    .table-grid {
        height: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        background-color: var(--primary-white, #ffffff);

        .ghead{
            flex: 0 1 auto;
        }
    }

    .gldate .fa-arrow-down {
        margin-top: 1px;
        padding: 3px;
        cursor: pointer;
    }
}

.delegate-action-table {
    .ghead {
        position: sticky;
        top: 0;
        z-index: 1;

        ul {
            background: var(--primary-white, #ffffff);
        }
    }
}

.fa-file-excel-o {
    padding-right: 3px;
}

.excel-export {
    #file-history-btn {
        outline: none;
    }
}

.dist-type-userlist {
    display: flex;
    justify-content: center;
    max-height: 169px;
    overflow: auto;

    .empty-option {
        width: 67%;
        font-style: italic;
        color: var(--gray, #808080);
    }

    ul {
        display: flex;
        flex-wrap: wrap;
        border-bottom: 0;
    }
}

.event-time {
    font-size: 11px;
}