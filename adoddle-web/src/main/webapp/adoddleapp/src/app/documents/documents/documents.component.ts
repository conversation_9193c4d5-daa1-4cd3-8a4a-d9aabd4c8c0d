
import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ement<PERSON>ef, OnDestroy, ViewChild, NgZone, TemplateRef, ViewContainerRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Location } from '@angular/common';

import { ClipboardService } from 'ngx-clipboard'

import { ActionCompleteService } from '../action-complete.service';

import { Filter } from 'app/shared/listing/filter/filter';
import { Export } from 'app/shared/listing/export/export';
import { ContextMenuItem, ContextMenu } from 'app/shared/context-menu/context-menu';
import { Observable, Subject, Subscription, take } from 'rxjs';

import { FileUploader } from 'app/upload/upload-directive/file-uploader.class';
import { FileItem } from 'app/upload/upload-directive/file-item.class';
import { BroadcastService } from 'app/shared/broadcast.service';
import { DocTemplateSelectorComponent } from '../doc-template-selector/doc-template-selector.component';
import { WarnNotificationComponent } from '../warn-notification/warn-notification.component';
import { ReactivateFoldersComponent } from '../reactivate-folders/reactivate-folders.component';
import { CommonMessagePromptComponent } from 'app/common/common-message-prompt/common-message-prompt.component';
import { ShareLinkUploadComponent } from 'app/shared/share-link/share-link-upload/share-link-upload.component';
import { FolderTreeComponent } from 'app/shared/listing/folder-tree/folder-tree.component';
import { TableListingComponent } from 'app/shared/listing/table-listing/table-listing.component';
import { COMMENT_TYPE } from 'app/shared/action/comment-type.enum';
import { ENTITY_TYPE, DOC_TYPE_ID } from 'app/shared/action/entity-type.enum';
import { DOC_ACTIONS, HISTORY_TYPE, PROJECT_STATUS } from 'app/shared/actions.enum';
import { ApiConstant } from 'app/shared/api-constant';
import { AppConstant } from 'app/shared/app-constant';
import { ACTIVE_APP_TYPE } from 'app/shared/app-type.enum';
import { ContextMenuService } from 'app/shared/context-menu/context-menu.service';
import { CommonUtilService } from 'app/shared/common-util.service';
import { FileFormUtilService } from 'app/shared/file-form-util.service';
import { ListingApiService } from 'app/shared/listing/listing-api.service';
import { RouteDataService } from 'app/shared/route-data.service';
import { ValidationService } from 'app/shared/validation.service';
import { ActivityService } from 'app/upload/activity-center/activity.service';
import { PlaceholderService } from '../create-placeholder/placeholder.service';
import { CommonModalComponent } from 'app/common/common-modal/common-modal.component';
import { FileTypeComponent } from '../file-type/file-type.component';
import { LeftNavButtonData } from 'app/shared/interfaces/left-nav-button-data.interface';
import { KeyCodes } from 'app/shared/enums/accessibility-keys.enum';
import { attributeType } from 'app/upload/file-upload.enum';
import { TourService } from 'app/shared/tour.service';
import { TOUR_SCREENS_ENUM } from 'app/common/enums/tour-screens-enum';
import { CognitiveCdeService } from 'app/cognitive-cde/cognitive-cde.service';
import { AdoddleCommonDialogService } from 'app/shared/adoddle-common-dialog.service';
import { VECTORIZATION_STATES } from 'app/cognitive-cde/cognitive.enum';
import { CognitiveSettingsComponent } from 'app/cognitive-cde/cognitive-settings/cognitive-settings.component';
import { HtmlOperationService } from 'app/shared/html-operation.service';

@Component({
  selector: 'adoddle-documents',
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.scss']
})

export class DocumentsComponent implements OnInit, OnDestroy {

  /**
   * Hold select file input for publish revision
   * @type {ElementRef}
   * @memberof DocumentsComponent
   */
  @ViewChild('publishRevisionFileInput', { static: true }) publishRevisionFileInput: ElementRef;

  @ViewChild('deleteVersions', { static: true }) deleteVersions: TemplateRef<any>;;

  @ViewChild('msPopupDialog', { static: true }) msPopupDialog: TemplateRef<any>;;

  @ViewChild('folderUpload') folderUpload: ElementRef;

  /**
   * @description Tablelisting component context
   * @private
   * @type {TableListingComponent}
   * @memberof DocumentsComponent
   */
  @ViewChild(TableListingComponent, { static: true }) public tableListingComponent: TableListingComponent;
  /**
   * @description Folder Tree component context
   * @type {FolderTreeComponent}
   * @memberof DocumentsComponent
   */
  @ViewChild(FolderTreeComponent, { static: true }) public folderTreeComponent: FolderTreeComponent;

  /**
   * @description Global jQuery context
   * @private
   * @type {*}
   * @memberof DocumentsComponent
   */
  private $: any = window['jQuery'];

  /**
   * @description Application constatants
   * @type {AppConstant}
   * @memberof DocumentsComponent
   */
  AppConstant: any = AppConstant;

  /**
    * @description Login user's subscription plan ID
    * @type {Number}
    * @memberof DocumentsComponent
    */
  subscriptionPlanId: any = window['USP'].subscriptionPlanId;

  directAccess: any = window['ADODDLE'].directAccessQueryString; // TODO: get data from service

  viewerTypeNoServer: string = window['viewerTypeNoServer']; // TODO: get data from service

  targetProjectId: string = '';

  targetFolderId: string = '';

  disabledFilesList: any = [];

  isProjectModelExist: boolean = false;

  checkProjectModelExistXhr: any;

  onlyContainSearchApplied: boolean = false;

  isDeleteVersions: boolean = false;

  isShowBatchAssoc: boolean = false;

  batchAssocData = [];

  private fileExtName : any;

  private enableMicrosoftOfficeBtn : boolean = true;

  private docuSignAllowedExtensions: string[] = window['docuSignAllowedExtensions'] ? window['docuSignAllowedExtensions'].split(',') : [];

  private disableUndoCheckout : boolean = false;

  // used to unsubscribe OPEN_IN_WEBAPP broadcast subscription
  private openInWebApp: Subscription;

  private officeFileDetails;

  private selectedMSFiles;

  private otherSelectedFiles;

  private labelLoading: string = this.util.lang.get('loading');

  /**
   * @description Identify listing is loaded directly using filter qr llink
   * @memberof DocumentsComponent
   */
  private isSharedFilterLink = false;

  /**
   * @description True when lazy loading is true and new data is loading from server
   * @memberof DocumentsComponent
   */
  lazyLoading: boolean = false;

  /**
   * @description Current Listing apptype
   * @type {Number}
   * @memberof DocumentsComponent
   */
  appType: Number = 1;

  /**
   * @description Left navigation object
   * @type {*}
   * @memberof DocumentsComponent
   */
  leftNavJsonData: LeftNavButtonData = {
    all: {
      label: this.util.lang.get('all'),
      type: 'ALL',
      xhr: false,
      tooltip: this.util.lang.get('all') + " ",
      iconClass: "fa fa-list",
      linkClass: "welcome-nav-link",
      isActive: true,
      list: [],
      clickHandler: (ele, object)=>{
        this.setActiveTab(object);
      }
    },
    incomplete: {
      label: this.util.lang.get('incomplete'),
      secondaryLabel: this.util.lang.get('tasks'),
      type: 'INCOMPLETE_ACTIONS_COUNT',
      disabled: this.subscriptionPlanId === AppConstant.KEY_LITE_SUBSCRIPTION_PLAN_ID,
      actionId: AppConstant.GET_INCOMPLETE_ACTIONS_COUNT,
      entityTypeId: ENTITY_TYPE.FILE,
      appType: 1,
      totalCount: 0,
      count: 0,
      xhr: false,
      isActive: false,
      list: [],
      clickHandler: (ele, object)=>{
        this.processLeftNavClick(object);
      },
      labelLoading: this.labelLoading,
      loaded: false
    },
    overdue: {
      label: this.util.lang.get('overdue'),
      secondaryLabel: this.util.lang.get('tasks'),
      type: 'OVERDUE_ACTIONS_COUNT',
      disabled: this.subscriptionPlanId === AppConstant.KEY_LITE_SUBSCRIPTION_PLAN_ID,
      actionId: AppConstant.GET_OVERDUE_ACTIONS_COUNT,
      entityTypeId: ENTITY_TYPE.FILE,
      appType: 1,
      totalCount: 0,
      count: 0,
      xhr: false,
      isActive: false,
      list: [],
      clickHandler: (ele, object)=>{
        this.processLeftNavClick(object);
      },
      labelLoading: this.labelLoading,
      loaded: false
    },
    dueToday: {
      label: this.util.lang.get('due'),
      secondaryLabel: this.util.lang.get('widget-files-today'),
      type: 'TODAY_DUE_ACTIONS_COUNT',
      disabled: this.subscriptionPlanId === AppConstant.KEY_LITE_SUBSCRIPTION_PLAN_ID,
      actionId: AppConstant.GET_TODAY_DUE_ACTIONS_COUNT,
      entityTypeId: ENTITY_TYPE.FILE,
      appType: 1,
      totalCount: 0,
      count: 0,
      xhr: false,
      isActive: false,
      list: [],
      clickHandler: (ele, object)=>{
        this.processLeftNavClick(object);
      },
      labelLoading: this.labelLoading,
      loaded: false
    },
    unreadaMessages: {
      label: this.util.lang.get('unread'),
      secondaryLabel: this.util.lang.get('comments-label'),
      type: 'UNREAD_MESSAGE_COUNT',
      actionId: AppConstant.GET_UNREAD_MESSAGE_COUNT,
      entityTypeId: ENTITY_TYPE.DISCUSSION,
      appType: 0,
      totalCount: 0,
      count: 0,
      xhr: false,
      isActive: false,
      list: [],
      hide: (window['isAmessageProjectSelected'] == true) ? false : true,
      clickHandler: (ele, object)=>{
        this.processLeftNavClick(object);
      },
      labelLoading: this.labelLoading,
      loaded: false
    },
    unreadReviews: {
      label: this.util.lang.get('unread'),
      secondaryLabel: this.util.lang.get('reviews'),
      type: 'UNREAD_REVIEWS_COUNT',
      actionId: AppConstant.GET_UNREAD_REVIEWS_COUNT,
      entityTypeId: 6,
      appType: 0,
      totalCount: 0,
      count: 0,
      xhr: false,
      isActive: false,
      list: [],
      hide: (window['isReviewEnableProjectSelected'] == true) ? false : true,
      clickHandler: (ele, object)=>{
        this.processLeftNavClick(object);
      },
      labelLoading: this.labelLoading,
      loaded: false
    }
  }

  /**
   * @description Filter params
   * @type {Filter}
   * @memberof DocumentsComponent
   */
  filter: Filter;

  /**
   * @description True when listing in full window
   * @memberof DocumentsComponent
   */
  maxWin: boolean = false;

  /**
   * @description Export params
   * @type {Export}
   * @memberof DocumentsComponent
   */
  export: Export = {
    filter: {
      action_id: AppConstant.EXPORT_FILTER_SEARCH_RESULT,
      jsonData: '',
      collectionType: AppConstant.FILE_LISTING,
      appType: this.appType,
      exportType: AppConstant.EXPORT_TYPE,
      controller: ApiConstant.SEARCH_FILTER_CONTROLLER,
    },
    data: {
      action_id: AppConstant.EXPORT_LISTING_DATA,
      appType: this.appType,
      exportType: AppConstant.EXPORT_TYPE,
      listingType: AppConstant.FILE_LISTING,
      controller: ApiConstant.EXPORT_CONTROLLER,
    },
    filterApplied: false
  }

  /**
   * @description Listing params
   * @type {*}
   * @memberof DocumentsComponent
   */
  listingParam: any = {};

  /**
   * @description Active listing data
   * @type {*}
   * @memberof DocumentsComponent
   */
  activeListing: any = {};

  /**
   * @description Folder permission data
   * @memberof DocumentsComponent
   */
  selectedFolderPermissionData: any = undefined;

  /**
   * @description Applied filter object
   * @memberof DocumentsComponent
   */
  filterParam = undefined;
  private _minMyTaskColWidth:number = 70;
  /**
   * @description Listing template to override default template
   * @memberof DocumentsComponent
   */

  /**
   * @description Contextmenu object
   * @type {ContextMenu}
   * @memberof DocumentsComponent
   */

  contextMenuItem: ContextMenu = {
    onInit: (data, event) => {
      this.createMSContextMenuData(data, event);
    }
  };

  private subContextMenuSubject = new Subject<() => Observable<any>>(); // Subject to hold tasks
  private subContextMenu$ = this.subContextMenuSubject.asObservable();
  private subContextMenuSubs: any;

  private contextMenuSubject = new Subject<() => Observable<any>>(); // Subject to hold tasks
  private contextMenu$ = this.contextMenuSubject.asObservable();
  private contextMenuSubs: any;

  @ViewChild('uploadDropDownBtn') uploadDropDownBtn: ElementRef;

  createMSContextMenuData(data, event) {
    this.actionsXhr = true;

    this.getMSContextMenuItems(data, (items) => {
      this.actionsXhr = false;
      this.contextMenuService.init(items, event);
    });
  }

  getMSContextMenuItems(data, callback) {
    this.setMSOfficeContextMenu(this.util.copy(this.contextMenuItemsMSOffice), data, callback);
    return;
  }

  contextMenuItemsMSOffice: Array<ContextMenuItem> = [
    {
      id: "editInBrowserApp",
      icon: "",
      title: "",
      tooltip: this.util.lang.get('tooltip-edit-file-in-microsoft-office-button'),
      callback: (data) => {
        this.fileFormUtilService.openInMSOffice(data,this.updateSelectedRowData.bind(this));
      },
      hidden: false,
      disable: false,
      data: []
    },
    {
      id: "discardChanges",
      icon: "",
      title: this.util.lang.get('discard-changes'),
      callback: (data) => {
        if(data.documentTypeId === DOC_TYPE_ID.O365_PLACEHOLDER) {
          this.fileFormUtilService.discardOfficeFileTemplate(data, this.updateSelectedRowData.bind(this));
          return;
        }
        this.fileFormUtilService.undoCheckoutMSOffice(data);
      },
      hidden: false,
      disable: false,
      data: []
    },
    {
      id: "publishChanges",
      icon: "",
      title: this.util.lang.get('publish-new-revision'),
      callback: (data) => {
        this.fileFormUtilService.publishRevisionMSFile(data,this.updateSelectedRowData.bind(this));
      },
      hidden: false,
      disable: false,
      data: []
    }
  ];

  async setMSOfficeContextMenu(items: Array<ContextMenuItem>, data, callback) {

    let selectedRows = this.tableListingComponent.getSelectionDataCurrentPage();

    const userPrivFileRightClick = await this.util.getUserAppPrivileges();

    this.util.getFolderPermissions([data], (permissionData) => {
      items.forEach((parent) => {
        parent.data = data;
        parent.removeInlinePadding = true;
        parent.removeInlineMargin = true;
        switch (parent.id) {
          case "editInBrowserApp":
            parent.disable = !(selectedRows.length === 0 || selectedRows.length === 1) || !(this.util.hasAccess(userPrivFileRightClick, 'ENABLE_MICROSOFT_OFFICE') && permissionData.projectADriveCDEAddonPurchaseStatus) || !this.checkFolderUploadPermission(permissionData, data.folderId) || !data.isActive;
            this.fileExtName = this.util.getExt(data.uploadFilename).toLowerCase();
            this.officeFileDetails = this.fileFormUtilService.getOfficeFileDetails(this.fileExtName);
            parent.title = this.officeFileDetails?.btnName;
            if(parent.disable){
              parent.disabledTooltip = this.util.lang.get('tooltip-edit-file-in-microsoft-office-button');
              parent.hideInfoIcon = true;
            }
            break;

          case "discardChanges":
            parent.disable = !(selectedRows.length === 0 || selectedRows.length === 1) || !(this.util.hasAccess(userPrivFileRightClick, 'ENABLE_MICROSOFT_OFFICE') && permissionData.projectADriveCDEAddonPurchaseStatus) || !(window['USP'].userID.split("$$")[0] == data.checkOutUserId) && !data.isUserFolderAdmin;
            break;

          case "publishChanges":
            parent.disable = !(selectedRows.length === 0 || selectedRows.length === 1) || !(this.util.hasAccess(userPrivFileRightClick, 'ENABLE_MICROSOFT_OFFICE') && permissionData.projectADriveCDEAddonPurchaseStatus) || !this.checkFolderUploadPermission(permissionData, data.folderId);
            break;
        }
      });
      callback(items);
    });
  }

  listingData: any = {
    itemHeader: [],
    allHeader: [],
    itemBody: [],
    allItem: [],
    sortOrder: null,
    sortField: null,
    sortFieldType: null,
    noRecordMsg: ""
  };

  listingTemplate = {
    img: (itemB, itemH, index) => {
      let title,img;
      if(itemH.fieldName == 'chkStatusImgName' && itemB.hasOfficeFile == true) {
        title = this.util.lang.get('checked-out-by') + ' ' + itemB.checkout_username + ', ' + itemB.checkout_orgname + ' ' + itemB.date_checked_out + '. ' + this.util.lang.get('this-file-is-edited-online-click-to-edit-discard-publish-revision');
      } else {
        title = this.tableListingComponent.tooltip(itemB, itemH);
      }

      if(this.activeListing.list.listingType === AppConstant.FILE_LISTING && itemB.documentTypeId === DOC_TYPE_ID.O365_PLACEHOLDER && itemH.fieldName == 'chkStatusImgName' ) {
        title = this.util.lang.get('this-file-is-edited-online-click-to-edit-discard-publish-revision');
      }

      if (itemH.fieldName == "fileType" && ((!itemB.hasOwnProperty('canView') || itemB.canView) && (!itemB.hasOwnProperty('canDownload') || itemB.canDownload))) {
        let checkout = this.listingData.listingType !== AppConstant.ASSOCIATED_FILE_LISTING;
        if (!checkout) {
          itemB.type = this.listingData.listingType;
        }

        let isDownloadDisable = false;
        if(itemB.documentTypeId === DOC_TYPE_ID.O365_PLACEHOLDER) {
          isDownloadDisable = true;
        }

        let component = this.util.initDynamicCompo(FileTypeComponent, {
          file: itemB,
          checkout: checkout,
          isDownloadDisable: isDownloadDisable,
          onLockForEditing: () => {
            this.tableListingComponent.onRowUpdated.emit([itemB]);
          }
        }, this._vcr).el;

        // compile action list component
        return this.$(component);
      }

      if (itemB[itemH.fieldName]) {
        if (itemH.fieldName == 'attachmentImageName' || itemH.fieldName == 'assocFormImgName') {
          img = `<div class="dropdown assoc-dd">
            <button class="btn btn-default dropdown-toggle no-border" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="0">
              <img alt="${title || itemH.fieldName}" title="${title}" src="/images/${itemB[itemH.fieldName]}" height=16 width=16 />
            </button>
            <div class="dropdown-menu">
              <div class="aLoader"></div>
            </div>
          </div>`
        } else {
          const altText = itemH.fieldName == 'chkStatusImgName' ? this.util.lang.get('checkout') : title || itemH.fieldName;
          let tabIndexValue = 0;
          if(itemB.documentTypeId === DOC_TYPE_ID.O365_PLACEHOLDER && itemH.fieldName == 'flagTypeImageName') {
            tabIndexValue = -1;
          }
          img = `<img alt="${altText}" title="${title}" src="/images/${itemB[itemH.fieldName]}" height=16 width=16 tabindex="${tabIndexValue}"/>`;
        }
      } else {
        img = '<span>--</span>';
      }

      img = this.$(img);
      if (itemH.fieldName == 'flagTypeImageName' || itemH.fieldName == 'attachmentImageName'
        || itemH.fieldName == 'chkStatusImgName' || itemH.fieldName == "attachmentImgName") {
        img.addClass('cursor-pointer');
      }

      if (itemH.fieldName == 'commentImgName' && itemH['function'] == "openCommentsDocument") {
        img.addClass('cursor-pointer');
        let commentTooltipLoaded = false;
        img.hover(() => {
          if (commentTooltipLoaded) {
            return;
          }

          commentTooltipLoaded = true;
          this.listing.getCommentMarkupToolTip(itemB, (tooltip) => {
            img.attr('title', tooltip)
          });
        });
      }

      if((itemH.fieldName === 'flagTypeImageName' || itemH.fieldName === 'commentImgName' || itemH.fieldName == 'chkStatusImgName') && itemB.documentTypeId === DOC_TYPE_ID.O365_PLACEHOLDER) {
        img.removeClass('cursor-pointer');
      }

      if(itemH.fieldName == 'chkStatusImgName' && itemB.hasOfficeFile == true && itemB.documentTypeId != DOC_TYPE_ID.O365_PLACEHOLDER) {
        let text;
        text = this.$(`<img alt="${title}" title="${title}" src="/images/Checkout-with-collaboration.svg" height="22" width="22" class="cursor-pointer">`)
        text.bind('click', (e) => {
          this.contextMenuItem.onInit(itemB, e);
        });
        return text;
      }

      (itemB[itemH.fieldName] || itemH.fieldName == 'commentImgName') && (!itemB.hasOwnProperty('canView') || itemB.canView) && img.bind('click keydown', (e) => {
        if(e.type == 'keydown' && e.key !== KeyCodes.ENTER){
          return;
        }
        if (itemH.fieldName == 'attachmentImageName' || itemH.fieldName == 'assocFormImgName') {
          setTimeout(() => {
            let $el = this.$(e.currentTarget);
            if ($el.hasClass('dropdown') && $el.hasClass('open')) {
              this.tableListingComponent.getAttachmentDetails($el, itemB);
            }
          }, 100);
        } else if (itemH.fieldName == 'flagTypeImageName' && itemB.documentTypeId !== DOC_TYPE_ID.O365_PLACEHOLDER) {
          this.onFlagChange({ data: [itemB], flagType: itemB.flagType == 0 ? 1 : 0, flagClicked: true });
        } else if (itemH.fieldName == 'chkStatusImgName' && itemB.hasOfficeFile != true) {
          window['undoCheckOutFilesRightClick'](itemB, [itemB]);
        } else if (itemH.fieldName == "attachmentImgName") {
          var hasOnlineViewerSupport = itemB.hasOnlineViewerSupportForAttachment;
          if (!hasOnlineViewerSupport && itemB.projectViewerId == '7') {
            itemB.hasOnlineViewerSupportForAttachment = (itemB.zipFileName) ? this.util.isZip(itemB.zipFileName) : false;
          }
          if (itemB.hasOnlineViewerSupportForAttachment && !itemB.hasHoopsViewerSupport && !itemB.has3DRepoViewerSupport) {
            window['openAttachFile'](this.util.getProjectId(itemB), itemB.attachmentId, itemB.attachmentImgName, '', itemB.revisionId, itemB);
          } else {
            window['attachDownload'](this.util.getProjectId(itemB), itemB.revisionId, itemB);
          }
        } else {
          if(itemH.fieldName === 'commentImgName' && itemB.documentTypeId === DOC_TYPE_ID.O365_PLACEHOLDER) {
            return;
          }
          let fn = this.listing[itemH["function"]];
          fn && fn.call(this.listing, itemB);
        }
      });

      return img;
    }
  };

  /**
   * @description ContextmenuItem object documents Listing
   * @type {Array<ContextMenuItem>}
   * @memberof DocumentsComponent
   */
  contextMenuItems: Array<ContextMenuItem> = [{
    id: "New",
    icon: "fa-circle-plus",
    title: this.util.lang.get('new'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [
      {
        id: "PublishRevision",
        icon: "",
        img: '/images/contextmenuImg/new-revision.svg',
        title: this.util.lang.get('revision'),
        disabledTooltip: this.util.lang.get('publish-revision-disabled-tooltip'),
        callback: (data) => {
          this.processOption('publish-revision');
        },
        hidden: false,
        disable: false,
        data: [],
      },
      {
        id: "StartDiscussion",
        icon: "adoddle-icon i-icon-comments",
        title: this.util.lang.get('start-discussion'),
        callback: (data) => {
          this.processOption('start-discussion');
        },
        hidden: false,
        disable: false,
        data: [],
      },
      {
        id: "CreateReview",
        icon: "adoddle-icon i-icon-comments",
        title: this.util.lang.get('create-review'),
        callback: (data) => {
          this.processOption('create-review');
        },
        hidden: true,
        disable: false,
        data: [],
      },
      {
        id: "NoReview",
        icon: "adoddle-icon i-icon-comments",
        title: this.util.lang.get('no-discussion'),
        callback: (data) => {
          this.processOption('no-review');
        },
        hidden: true,
        disable: false,
        data: [],
      },
      {
        id: "NoDiscussion",
        icon: "adoddle-icon i-no-discussion",
        title: this.util.lang.get('no-discussion'),
        callback: (data) => {
          this.processOption('no-discussion');
        },
        hidden: false,
        disable: false,
        data: [],
      },
      {
        id: "AssociateFiles",
        icon: "fa fa fa-sign-out",
        title: this.util.lang.get('project-form'),
        callback: () => {
          this.processOption('assoc-files');
        },
        hidden: false,
        disable: false,
        data: [],
      },
      {
        id: "FilePlaceholder",
        icon: "fa fa-square-o",
        title: this.util.lang.get('placeholder'),
        callback: (data) => {
          this.processOption('create-placeholder');
        },
        hidden: false,
        disable: false,
        data: [],
      },
      {
        id: "createModel",
        icon: "fa fa-plus",
        title: this.util.lang.get('create-model'),
        //disabledTooltip: this.util.lang.get('publish-revision-disabled-tooltip'),
        callback: (data) => {
          this.processOption('create-model');
        },
        hidden: false,
        disable: false,
        data: [],
      }
    ]
  },
  // newly added aDrive download functionality
  /*{
    id: "aSync",
    icon: "adoddle-icon i-icon-edit",
    title: this.util.lang.get('aSync'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [
      {
        id: "OpenFileEdit",
        icon: "fa fa-cloud-upload",
        title: this.util.lang.get('Edit'),
        callback: (data) => {
          this.processOption('open-file-edit');
        },
        hidden: false,
        disable: false,
        data: [],
      },
      {
        id: "CopyFilesaSync",
        icon: "fa fa-cloud-upload",
        title: this.util.lang.get('copy-files-aSync'),
        callback: (data) => {
          this.processOption('copy-files-aSync');
        },
        hidden: false,
        disable: false,
        data: [],
      }
    ]
  },*/
  {
    id: "Flag",
    icon: "",
    img: '/images/icons/flag_header_icon.png',
    title: this.util.lang.get('flag'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: []
  },
  {
    id: "Edit",
    icon: "",
    img: '/images/contextmenuImg/edit.svg',
    title: this.util.lang.get('edit'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [
      {
        id: "OpenFileEdit",
        icon: "fa fa-edit",
        title: this.util.lang.get('edit-file-locally'),
        callback: (data) => {
          this.processOption('open-file-edit');
        },
        hidden: false,
        disable: false,
        data: [],
      },
      {
        id: "OpenInMicrosoftOffice",
        icon: "",
        title: "",
        tooltip: this.util.lang.get('tooltip-edit-file-in-microsoft-office-button'),
        callback: (data) => {
          this.processOption('open-in-microsoft-office');
        },
        hidden: false,
        disable: false,
        data: [],
      },
      {
        id: "FileAttributes",
        icon: "fa fa-file-text-o",
        title: this.util.lang.get('attributes'),
        callback: (data) => {
          this.processOption('edit-file-attributes');
        },
        hidden: false,
        disable: false,
        data: [],
      },
      {
        id: "AttachFile",
        icon: "fa fa-file-o",
        title: this.util.lang.get('attach-file'),
        callback: (data) => {
          this.processOption('attach-file');
        },
        hidden: false,
        disable: false,
        data: [],
      },
      {
        id: "EditStatus",
        icon: "fa adoddle-icon i-status-change f16",
        title: this.util.lang.get('status'),
        callback: (data) => {
          this.processOption('status-change');
        },
        hidden: false,
        disable: false,
        data: [],
      },
      {
        id: "CustomizeStatus",
        icon: "fa adoddle-icon i-manage-doc-status f16",
        title: this.util.lang.get('custStatus'),
        callback: (data) => {
          this.processOption('custom-status');
        },
        hidden: false,
        disable: false,
        data: [],
      },
      {
        id: "BatchAssocition",
        icon: "iconoir-axes",
        title: this.util.lang.get('start-association'),
        callback: (data) => {
          this.processOption('batch-assocition');
        },
        hidden: false,
        disable: false,
        data: [],
      },
    ]
  },
  {
    id: "View",
    icon: "fa fa-eye",
    title: this.util.lang.get('view'),
    callback: (data) => {
      this.processOption('view-file');
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "DownloadFiles",
    icon: "fa fa-download",
    title: this.util.lang.get('downloadFiles'),
    callback: (data) => {
      this.processOption('download-files');
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "Visibility",
    icon: "fa fa-check",
    title: this.util.lang.get('visibility'),
    callback: (data) => {
      this.processOption('visibility-files');
    },
    hidden: false,
    disable: false,
    data: [],
  },
  {
    id: "StartWorkflow",
    icon: "",
    img: '/images/contextmenuImg/start-workflow.svg',
    title: this.util.lang.get('start-workflow'),
    callback: (data) => {
      this.processOption('start-workflow');
    },
    hidden: false,
    disable: false,
    data: [],
  },
  {
    id: "Checkout",
    icon: "adoddle-icon i-checkout",
    title: this.util.lang.get('checkout'),
    callback: (data) => {
      this.processOption('checkout');
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "Undocheckout",
    icon: "adoddle-icon i-undo-checkout",
    title: this.util.lang.get('undocheckout'),
    callback: (data) => {
      this.processOption('undo-checkout');
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "Compare",
    icon: "",
    img: '/images/contextmenuImg/compare.svg',
    title: this.util.lang.get('compare'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "CompareFiles",
      icon: "fa adoddle-icon i-comparefiles",
      title: this.util.lang.get('comparefiles'),
      callback: (data) => {
        this.processOption('compare-files');
      },
      hidden: false,
      disable: false,
      data: []
    }, {
      id: "CompareText",
      icon: "fa adoddle-icon i-comparetext",
      title: this.util.lang.get('comparetext'),
      callback: (data) => {
        this.processOption('compare-text');
      },
      hidden: false,
      disable: false,
      data: []
    }
    ]
  },
  {
    id: "Share",
    icon: "fa fa-send",
    title: this.util.lang.get('share'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [
      {
        id: "DistributeFile",
        icon: "",
        img: '/images/contextmenuImg/distribute-file.svg',
        title: this.util.lang.get('distribute-files'),
        callback: (data) => {
          this.processOption('distribute-files');
        },
        hidden: false,
        disable: false,
        data: [],
      }, {
        id: "CopyDirectLink",
        icon: "",
        img: '/images/contextmenuImg/share-link.svg',
        title: this.util.lang.get('share-link-right-click-menu'),
        callback: (data) => {
          this.processOption('copy-direct-link');
        },
        hidden: false,
        disable: false,
        data: [],
      }
    ]
  },
  {
    id: "MoreOptions",
    icon: "",
    img: '/images/contextmenuImg/more_horiz.svg',
    title: this.util.lang.get('more-options'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "Associations",
      icon: "",
      img: '/images/contextmenuImg/associations.svg',
      title: this.util.lang.get('associations'),
      callback: (data) => {
        this.processOption('associations');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "DeactivateFiles",
      icon: "fa fa-circle-o",
      title: this.util.lang.get('deactivate-files'),
      callback: (data) => {
        this.processOption('deactivate-files');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "ReactivateFiles",
      icon: "fa fa-circle",
      title: this.util.lang.get('reactivate-files'),
      callback: (data) => {
        this.processOption('reactivate-files');
      },
      hidden: false,
      disable: false,
      data: [],
    }]
  },
  {
    id: "History",
    icon: "",
    img: '/images/contextmenuImg/history.svg',
    title: this.util.lang.get('history'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "AllHistory",
      icon: "fa adoddle-icon i-fileallhistory",
      title: this.util.lang.get('all'),
      callback: (data) => {
        this.processOption('all-history');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "DistributionHistory",
      icon: "fa adoddle-icon i-filedistributionhistory",
      title: this.util.lang.get('distribution'),
      callback: (data) => {
        this.processOption('distribution-history');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "RevisionsHistory",
      icon: "fa adoddle-icon i-filerevisionhistory",
      title: this.util.lang.get('revisions'),
      callback: (data) => {
        this.processOption('revisions-history');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "StatusHistory",
      icon: "fa adoddle-icon i-filestatushistory",
      title: this.util.lang.get('status'),
      callback: (data) => {
        this.processOption('status-history');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "SignatoriesHistory",
      icon: "fa fa-pencil-square-o",
      title: this.util.lang.get('signatories'),
      callback: (data) => {
        this.processOption('signatories-history');
      },
      hidden: false,
      disable: false,
      data: [],
    }]
  },
  {
    id: "Copy",
    icon: "fa fa-clipboard",
    title: this.util.lang.get('copy'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "CopyFile",
      icon: "fa fa-file-text-o",
      title: this.util.lang.get('copy-files'),
      callback: (data) => {
        this.processOption('copy-files');
      },
      hidden: false,
      disable: false,
      data: [],
    },{
      id: "Name",
      icon: "fa adoddle-icon i-name f16",
      title: this.util.lang.get('name'),
      callback: (data) => {
        this.processOption('copy-name');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "CopyLocation",
      icon: "fa adoddle-icon i-location",
      title: this.util.lang.get('location'),
      callback: (data) => {
        this.processOption('copy-location');
      },
      hidden: false,
      disable: false,
      data: [],
    }]
  },
  {
    id: "StartWatching",
    icon: "",
    img: '/images/contextmenuImg/start-watching.svg',
    title: this.util.lang.get('startwatching'),
    callback: (data) => {
      this.processOption('start-watching');
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "Watch",
    icon: "adoddle-icon i-start-watching",
    title: this.util.lang.get('watch'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "StopWatching",
      icon: "",
      img: '/images/contextmenuImg/stop-watching.svg',
      title: this.util.lang.get('stopwatching'),
      callback: (data) => {
        this.processOption('stop-watching');
      },
      hidden: false,
      disable: false,
      data: []
    }, {
      id: "EditSettings",
      icon: "fa fa-cog",
      title: this.util.lang.get('edit_settings'),
      callback: (data) => {
        this.processOption('edit-watch-settings');
      },
      hidden: false,
      disable: false,
      data: []
    }]
  },
  {
    id: "LinkFile",
    icon: "",
    img: '/images/contextmenuImg/fa_link.svg',
    title: this.util.lang.get('link-document'),
    callback: (data) => {
      this.processOption('link-file');
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "MoveFiles",
    icon: "",
    img: '/images/contextmenuImg/mdi_file-document-move.svg',
    title: this.util.lang.get('move-files'),
    callback: (data) => {
      this.processOption('move-files');
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "DeleteFiles",
    icon: "",
    img: '/images/contextmenuImg/delete.svg',
    title: this.util.lang.get('delete'),
    callback: (data) => {
      this.processOption('delete-files');
    },
    hidden: false,
    disable: false,
    data: [],
  },
  {
    id: "Tasks",
    icon: "",
    img: '/images/contextmenuImg/icons8_tasks.svg',
    title: this.util.lang.get('tasks'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "ForInfo",
      icon: "adoddle-icon i-files-forinfo",
      title: this.util.lang.get('action_name_7'),
      callback: (data) => {
        this.processOption('task-info');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "ForAck",
      icon: "adoddle-icon i-for-ack-action",
      title: this.util.lang.get('action_name_37'),
      callback: (data) => {
        this.processOption('task-ack');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "ForCommentCoordination",
      icon: "adoddle-icon i-for-comment-coordination-action",
      title: this.util.lang.get('for-comment-coordination'),
      callback: (data) => {
        this.processOption('task-comment-coordination');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "ForCommentIncorporation",
      icon: "adoddle-icon i-for-comment-inc-action",
      title: this.util.lang.get('for-comment-incorporation'),
      callback: (data) => {
        this.processOption('task-comment-incorporation');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "ForAction",
      icon: "adoddle-icon i-for-action",
      title: this.util.lang.get('action_name_36'),
      callback: (data) => {
        this.processOption('task-action');
      },
      hidden: false,
      disable: false,
      data: [],
    }]
  },
  {
    id: "ActivityLocks",
    icon: "",
    img: '/images/contextmenuImg/activity-locks.svg',
    title: this.util.lang.get('activity-locks'),
    callback: (data) => {
      this.processOption('activity-locks');
    },
    hidden: false,
    disable: false,
    data: [],
  },
  {
    id: "SaveAsPDF",
    icon: "",
    img: '/images/contextmenuImg/save-file.svg',
    title: this.util.lang.get('Save-as-pdf'),
    callback: (data) => {
      this.processOption('save-as-pdf');
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "PrintFile",
    icon: "",
    img: '/images/contextmenuImg/print.svg',
    title: this.util.lang.get('print-file'),
    callback: (data) => {
      this.processOption('print-file');
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "ModelOptions",
    icon: "fa fa-clipboard",
    title: this.util.lang.get('model-options'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "ViewInModel",
      icon: "fa fa-eye",
      title: this.util.lang.get('view-in-model-viewer'),
      callback: (data) => {
        this.processOption('view-model-options');
      },
      hidden: false,
      disable: false,
      data: [],
    },
    {
      id: "AddToModel",
      icon: "fa fa-plus",
      title: this.util.lang.get('add-to-model'),
      callback: (data) => {
        this.processOption('Add-model-options');
      },
      hidden: false,
      disable: false,
      data: [],
    },
    {
      id: "RemoveFromModel",
      icon: "fa fa-trash",
      title: this.util.lang.get('remove-from-model'),
      callback: (data) => {
        this.processOption('Remove-model-options');
      },
      hidden: false,
      disable: false,
      data: [],
    },
    {
      id: "PushTo3DRepo",
      icon: "fa fa-cloud-upload ",
      title: this.util.lang.get('push-to-3d-repo'),
      callback: () => {
        this.processOption('push-to-3d-repo');
      },
      hidden: false,
      disable: false,
      data: [],
    }
    ]
  },
  {
    id:"SignWithDocuSign",
    icon: "",
    img: "/images/contextmenuImg/signing.svg",
    title: this.util.lang.get('sign-with-docusign'),
    callback: () => {
      this.processOption('sign-with-docusign');
    },
    hidden: false,
    disable: false,
    data: [],
  },
  {
    id: "ViewWithDocuSign",
    icon: "",
    img: "/images/contextmenuImg/signing.svg", // todo: update image once provided by UX
    title: this.util.lang.get('view-with-docusign'),
    callback: () => {
      this.processOption('view-with-docusign');
    },
    hidden: false,
    disable: false,
    data: [],
  },
  {
    id: "editInBrowserApp",
    icon: "",
    title: "",
    tooltip: this.util.lang.get('tooltip-edit-file-in-microsoft-office-button'),
    callback: (data) => {
      this.fileFormUtilService.openInMSOffice(data[0],this.updateSelectedRowData.bind(this));
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "discardChanges",
    icon: "",
    title: this.util.lang.get('discard-changes'),
    callback: (data) => {
      this.fileFormUtilService.discardOfficeFileTemplate(data[0], this.updateSelectedRowData.bind(this));
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "publishChanges",
    icon: "",
    title: this.util.lang.get('publish-new-revision'),
    callback: (data) => {
      this.fileFormUtilService.publishRevisionMSFile(data[0], this.updateSelectedRowData.bind(this));
    },
    hidden: false,
    disable: false,
    data: []
  }
  ];

  /**
   * @description ContextmenuItem object Transmittle Listing
   * @type {Array<ContextMenuItem>}
   * @memberof DocumentsComponent
   */
  contextMenuItemsTransmittle: Array<ContextMenuItem> = [{
    id: "ClearActions",
    icon: "adoddle-icon i-icon-clear-action f18",
    title: this.util.lang.get('clear-actions'),
    callback: (data) => {
      this.processOption('clear-actions');
    },
    hidden: false,
    disable: false,
    data: []
  }, {
    id: "DelegateActions",
    icon: "adoddle-icon i-icon-delegate-action f18",
    title: this.util.lang.get('delegate-actions'),
    callback: (data) => {
      this.processOption('delegate-actions');
    },
    hidden: false,
    disable: false,
    data: []
  }, {
    id: "DeactivateActions",
    icon: "adoddle-icon i-icon-deactivate-action f18",
    title: this.util.lang.get('deactivate-actions'),
    callback: (data) => {
      this.processOption('deactivate-actions');
    },
    hidden: false,
    disable: false,
    data: []
  }, {
    id: "ReactivateActions",
    icon: "adoddle-icon i-icon-reactivate-action f18",
    title: this.util.lang.get('reactivate-actions'),
    callback: (data) => {
      this.processOption('reactivate-actions');
    },
    hidden: false,
    disable: false,
    data: []
  }]

  /**
   * @description ContextmenuItem object aMessage Listing
   * @type {Array<ContextMenuItem>}
   * @memberof DocumentsComponent
   */
  contextMenuItemsMessage: Array<ContextMenuItem> = [{
    id: "New",
    icon: "",
    title: this.util.lang.get('new'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "Reply",
      icon: "adoddle-icon i-icon-03",
      title: this.util.lang.get('reply'),
      callback: (data) => {
        this.processOption('reply-discussion');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "AssociateDiscusions",
      icon: "fa fa-sign-out",
      title: this.util.lang.get('project-form'),
      callback: () => {
        this.processOption('assoc-discusion');
      },
      hidden: false,
      disable: false,
      data: [],
    },]
  }, {
    id: "View",
    icon: "adoddle-icon i-icon-view-discussion",
    title: this.util.lang.get('view'),
    callback: (data) => {
      this.processOption('open-discussion');
    },
    hidden: false,
    disable: false,
    data: []
  }, {
    id: "NavigateToFolder",
    icon: "",
    img: '/images/contextmenuImg/navigateToFolder.svg',
    title: this.util.lang.get('navigate-to-folder'),
    callback: (data) => {
      this.processOption('navigate-to-folder');
    },
    hidden: false,
    disable: false,
    data: []
  }, {
    id: "Copy",
    icon: "fa fa-clipboard",
    title: this.util.lang.get('copy'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "CopyMsgName",
      icon: "fa fa-clipboard",
      title: this.util.lang.get('name'),
      callback: (data) => {
        this.processOption('copy-msg-title');
      },
      hidden: false,
      disable: false,
      data: [],
    }, {
      id: "CopyLocation",
      icon: "fa adoddle-icon i-location",
      title: this.util.lang.get('location'),
      callback: (data) => {
        this.processOption('copy-location');
      },
      hidden: false,
      disable: false,
      data: [],
    }]
  }, {
    id: "MarkAsRead",
    icon: "adoddle-icon i-icon-read-discussion",
    title: this.util.lang.get('mark-as-read'),
    callback: (data) => {
      this.processOption('mark-discussion-as-read');
    },
    hidden: false,
    disable: false,
    data: []
  }]

  /**
   * @description Contextmenu object
   * @type {ContextMenu}
   * @memberof DocumentsComponent
   */
  contextMenu: ContextMenu = {
    onInit: (data, event) => {
      if (this.subscriptionPlanId === AppConstant.KEY_LITE_SUBSCRIPTION_PLAN_ID) {
        return;
      }

      if (!data.length || this.listingXhr) {
        return;
      }

      if (!this.hasViewerNotSupportedFileSelected(data)) {
        const apiCallData = {
          projectId: data[0].projectId,
        };

        this.checkProjectModelExistXhr = this.util.ajax({
          url: ApiConstant.CHECK_PROJECT_MODEL_EXIST,
          method: 'POST',
          data: apiCallData,
          success: (response) => {
            this.createContextMenuData(data, event, response?.body?.isProjectModelExist);
          },
          error: (err) => {
            this.checkProjectModelExistXhr = false;
            this.createContextMenuData(data, event);
          }
        });

        return;
      }

      this.createContextMenuData(data, event);
    }
  };

  /**
   * @description Following function is used to check for preventing to open context menu
   * @memberof DocumentsComponent
   */
  private shouldPreventContextMenu(data) {
    for(const item of data) {
      if(item.documentTypeId === DOC_TYPE_ID.O365_PLACEHOLDER) {
        return true;
      } 
    }
    return false;
  }

  /**
   * @description ContextmenuItem object
   * @type {Array<ContextMenuItem>}
   * @memberof DocumentsComponent
   */
  contextMenuItemsTree: Array<ContextMenuItem> = [{
    id: "New",
    icon: "",
    title: this.util.lang.get('new'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "AddFolder",
      icon: "fa fa-folder",
      title: this.util.lang.get('folder'),
      callback: (data) => {
        this.processFolderOption('add-new-folder', data);
      },
      hidden: false,
      disable: false,
      data: [],
    }]
  },
  {
    id: "EditProject",
    icon: "fa fa-edit",
    title: this.util.lang.get('edit-project'),
    callback: (data) => {
      window['projects'] && window['projects'].editProjectInit && window['projects'].editProjectInit({
        dcId: parseInt(data.dcId),
        projectID: this.util.getProjectId(data),
        isCloned: data.isCloned && JSON.parse(data.isCloned),
        parentTemplateId: data.parentTemplateId
      }, {
        onEditProjectSettings: (projectData) => {
          this.folderTreeComponent.setViewerEnabledSettings(projectData);
        }
      });
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "AddFavourite",
    icon: "fa fa-star-o",
    title: this.util.lang.get('addFavourite'),
    callback: (data) => {
      this.toggleProjectFav(data);
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "RemoveFavourite",
    icon: "fa fa-star",
    title: this.util.lang.get('remove-as-fav'),
    callback: (data) => {
      this.toggleProjectFav(data);
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "RolesUsers",
    icon: "fa fa-user-circle",
    title: this.util.lang.get('roles-users'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [
      {
        id: "UserAccess",
        icon: "fa adoddle-icon i-sidenav-manage-roles i-20",
        title: this.util.lang.get('user-roles-access'),
        callback: (data) => {
          //TODO: Remove below code and implement in Latest Angular version
          window['projects'] && window['projects'].manageRolesFromRightClick && window['projects'].manageRolesFromRightClick(data, AppConstant.PRIV_MANAGE_WORKSPACE_ROLES_USERS);
        },
        hidden: false,
        disable: false,
        data: []
      },
      {
        id: "RolePrivileges",
        icon: "fa adoddle-icon i-sidenav-role-privileges i-20",  //fa fa-user-o
        title: this.util.lang.get('role-privileges'),
        callback: (data) => {
          //TODO: Remove below code and implement in Latest Angular version
          window['projects'] && window['projects'].manageRolesFromRightClick && window['projects'].manageRolesFromRightClick(data, AppConstant.CAN_MANAGE_ROLE_PRIVILEGES);
        },
        hidden: false,
        disable: false,
        data: []
      },
      {
        id: "AppPermissions",
        icon: "fa adoddle-icon i-lh-role-form-permissions i-20",
        title: this.util.lang.get('app-permissions'),
        callback: (data) => {
          //TODO: Remove below code and implement in Latest Angular version
          window['projects'] && window['projects'].manageRolesFromRightClick && window['projects'].manageRolesFromRightClick(data, AppConstant.CAN_MANAGE_FORM_PRIVILEGES);
        },
        hidden: false,
        disable: false,
        data: []
      }
    ]
  },
  {
    id: "ReactivateFolders",
    icon: "fa fa-circle",
    title: this.util.lang.get('reactivate-folders'),
    callback: (data) => {
      this.reactivateFolders(data);
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "Copy",
    icon: "fa fa-clipboard",
    title: this.util.lang.get('copy'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "CopyName",
      icon: "fa adoddle-icon i-name f16",
      title: this.util.lang.get('name'),
      callback: (data) => {
        this._clipboardService.copyFromContent(data.projectName);
        this.util.notification.info({
          theClass: 'notification-sm',
          msg: this.util.lang.get('copied')
        });
      },
      hidden: false,
      disable: false,
      data: [],
    }]
  }]

  /**
   * @description Contextmenu object
   * @type {ContextMenu}
   * @memberof DocumentsComponent
   */
  contextMenuTree: ContextMenu = {
    onInit: (data, event) => {
      this.contextMenuSubs && this.contextMenuSubs.unsubscribe();
      if (this.subscriptionPlanId === AppConstant.KEY_LITE_SUBSCRIPTION_PLAN_ID || (data.isTemplate && !data.isAdminAccess) || this.util.getProjectId(data) == -1) {
        return;
      }

      let items = this.util.copy(this.contextMenuItemsTree);
      items.forEach((item) => {
        item.data = data;
        item.disable = true;
        item.hidden = false;
        item.subMenuItems && item.subMenuItems.forEach((subMenuItem) => {
          subMenuItem.data = data;
          subMenuItem.disable = true;
          item.hidden = false;
        });
      });

      if (!data) {
        return;
      }

      if(this.folderTreeComponent.isFirstTimeSubContextMenuCalled) {
        this.contextMenuSubs = this.contextMenu$.pipe(take(1)).subscribe((permissionData: any) => {
            this.contextMenuSetPermission(data, event, items, permissionData);
            this.contextMenuSubs.unsubscribe();
          }
        );
      } else {
        this.util.getProjectPermission(this.util.getProjectId(data), data.dcId, (permissionData) => {
          this.contextMenuSetPermission(data, event, items, permissionData);
        });
      }

    }
  };

  contextMenuSetPermission = (data, event, items, permissionData) => {
    items.forEach((item) => {
      switch (item.id) {
        case "New":
          item.disable = !this.util.hasAccess(permissionData.privileges, 'PRIV_CREATE_PARENT_FOLDERS');
          break;

        case "ReactivateFolders":
          item.disable = !this.util.hasAccess(permissionData.privileges, 'PRIV_AMEND_FOLDER_PERMISSIONS');
          break;

        case "Copy":
          item.disable = false;
          break;

        case "AddFavourite":
          if (data.isTemplate || (typeof data.isFavourite != 'undefined' && data.isFavourite) || (typeof data.isFavorite != 'undefined' && data.isFavorite)) {
            item.hidden = true;
          } else {
            item.disable = false;
          }
          break;

        case "RemoveFavourite":
          if (data.isTemplate || (typeof data.isFavourite != 'undefined' && !data.isFavourite) || (typeof data.isFavorite != 'undefined' && !data.isFavorite)) {
            item.hidden = true;
          } else {
            item.disable = false;
          }
          break;

        case "RolesUsers":
          item.disable = !this.util.hasAccess(permissionData.privileges, 'PRIV_MANAGE_WORKSPACE_ROLES_USERS') && !this.util.hasAccess(permissionData.privileges, 'CAN_MANAGE_ROLE_PRIVILEGES') && !this.util.hasAccess(permissionData.privileges, 'CAN_MANAGE_FORM_PRIVILEGES');
          item.hidden = data.isTemplate;
          break;

        case "EditProject":
          item.disable = !this.util.hasAccess(permissionData.privileges, 'PRIV_EDIT_PROJECT_DETAILS');
          item.hidden = data.isTemplate;
          break;
      }

      item.subMenuItems && item.subMenuItems.forEach((subMenuItem) => {
        switch (subMenuItem.id) {
          case "CopyName":
            subMenuItem.disable = false;
            break;

          case "AddFolder":
            subMenuItem.disable = !this.util.hasAccess(permissionData.privileges, 'PRIV_CREATE_PARENT_FOLDERS');
            break;

          case "UserAccess":
            subMenuItem.disable = !this.util.hasAccess(permissionData.privileges, 'PRIV_MANAGE_WORKSPACE_ROLES_USERS');
            subMenuItem.hidden = data.isTemplate;
            break;

          case "RolePrivileges":
            subMenuItem.disable = !this.util.hasAccess(permissionData.privileges, 'CAN_MANAGE_ROLE_PRIVILEGES');
            subMenuItem.hidden = data.isTemplate;
            break;

          case "AppPermissions":
            subMenuItem.disable = !this.util.hasAccess(permissionData.privileges, 'CAN_MANAGE_FORM_PRIVILEGES');
            subMenuItem.hidden = data.isTemplate;
            break;
        }
      });
    });

    this.contextMenuService.init(items, event);
  }

  /**
   * @description ContextmenuItem object for sub folder
   * @type {Array<ContextMenuItem>}
   * @memberof DocumentsComponent
   */
  subContextMenuItemsTree: Array<ContextMenuItem> = [{
    id: "New",
    icon: "",
    title: this.util.lang.get('new'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "AddSubFolder",
      icon: "fa fa-folder",
      title: this.util.lang.get('sub-folder'),
      callback: (data) => {
        window['addFolder']({
          projectId: this.util.getProjectId(data),
          folderId: data.folderId || 0,
          isWorkspace: data.folderId ? 0 : 1,
          hassubfold: data.hasSubFolder,
          dcId: data.dcId,
          isPFLocationTree: data.isPFLocationTree,
          isTemplate: data.project.isTemplate,
          onSuccess: () => {
            this.folderTreeComponent.refreshFolder(data);
          }
        });
      },
      hidden: false,
      disable: false,
      data: [],
    },
    {
      id: "CreateFile",
      icon: "fa fa-plus",
      title: this.util.lang.get('create-file'),
      callback: (data) => {
        this.processFolderOption('create-file', data);
      },
      hidden: false,
      disable: false,
      data: [],
    },
    {
      id: "AddFiles",
      icon: "fa fa-file-text",
      title: this.util.lang.get('addFiles'),
      callback: (data) => {
        this.processFolderOption('add-files', data);
      },
      hidden: false,
      disable: false,
      data: [],
    },
    {
      id: "AddPlaceholder",
      icon: "fa fa-square-o",
      title: this.util.lang.get('placeholder'),
      callback: (data) => {
        this.processFolderOption('add-placeholder', data);
      },
      hidden: false,
      disable: false,
      data: [],
    }]
  },
  {
    id: "EditFolder",
    icon: "fa fa-edit",
    title: this.util.lang.get('edit-folder'),
    callback: (data) => {
      window['editFolder']({
        projectId: data.projectId,
        folderId: data.folderId,
        dcId: data.dcId,
        parentLevelFolders: this.folderTreeComponent.getSiblingFolderName(data) || [],
        isParent: data.isWorkspace,
        isTemplate: data.project.isTemplate,
        isFavourite: data.isFavourite,
        isProjectReviewEnabled: data.project.enableCommentReview,
        hasSubFolder: data.hasSubFolder,
        treeElem: this.folderTreeComponent,
        onSuccess: (info) => {
          if (info && info.isDeactivate){
            this.folderTreeComponent.selectAndRefreshParentFolder(data)
          }else{
            this.folderTreeComponent.refreshParentFolder(data);
          }
        },
        onFavFolderChange:(isFavourite) => {
          data.isFavourite = isFavourite;
        },
      });
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "ViewFolderPermission",
    icon: "fa fa-eye",
    title: this.util.lang.get('view-folder-per'),
    callback: (data) => {
      window['openFolderPermissionDialog'](data.folderId, this.util.getProjectId(data), data.project.isTemplate, data.dcId, data.isFavourite, data.project.isTemplate)
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "AddFavourite",
    icon: "fa fa-star-o",
    title: this.util.lang.get('addFavourite'),
    callback: (data) => {
      this.toggleFolderTypeFav(data);
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "RemoveFavourite",
    icon: "fa fa-star",
    title: this.util.lang.get('remove-as-fav'),
    callback: (data) => {
      this.toggleFolderTypeFav(data);
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "Copy",
    icon: "fa fa-clipboard",
    title: this.util.lang.get('copy'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [
      {
        id: "CopyName",
        icon: "fa adoddle-icon i-name f16",
        title: this.util.lang.get('name'),
        callback: (data) => {
          this._clipboardService.copyFromContent(data.folder_title);
          this.util.notification.info({
            theClass: 'notification-sm',
            msg: this.util.lang.get('copied')
          });
        },
        hidden: false,
        disable: false,
        data: []
      },
      {
        id: "CopyLocation",
        icon: "fa adoddle-icon i-location",  //fa fa-user-o
        title: this.util.lang.get('location'),
        callback: (data) => {
          this._clipboardService.copyFromContent(data.folderPath);
          this.util.notification.info({
            theClass: 'notification-sm',
            msg: this.util.lang.get('copied')
          });
        },
        hidden: false,
        disable: false,
        data: []
      },
      {
        id: "CopyLink",
        icon: "fa adoddle-icon i-folderlink",
        title: this.util.lang.get('link'),
        callback: (data) => {
          this.util.getFolderDirectAccessLink(data, (resp) => {
            this._clipboardService.copyFromContent(resp.notificationLink);
            this.util.notification.info({
              theClass: 'notification-sm',
              msg: this.util.lang.get('copied')
            });
          })
        },
        hidden: false,
        disable: false,
        data: []
      },
      {
        id: "CopyFolderStructure",
        icon: "fa adoddle-icon i-folderstructure",
        title: this.util.lang.get('folder-structure'),
        callback: (data) => {
          this.processFolderOption('copy-folder-structure', data);
        },
        hidden: false,
        disable: false,
        data: []
      }
    ]
  },
  {
    id: "MoveFolder",
    icon: "fa fa fa-sign-out",
    title: this.util.lang.get('move-folder'),
    callback: (data) => {
      this.processFolderOption('move-folder', data);
    },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [],
  },
  {
    id: "StartWatching",
    icon: "",
    img: '/images/contextmenuImg/start-watching.svg',
    title: this.util.lang.get('startwatching'),
    callback: (data) => {
      window['subscribeWatch']({
        watchingFrom: 'folder',
        action: 'start',
        msg: data.folder_title,
        paramObj: {
          proid: this.util.getProjectId(data),
          foldID: data.folderId
        },
        isBatch: false
      });
      data.isWatching = true;
    },
    hidden: false,
    disable: false,
    data: []
  },
  {
    id: "Watch",
    icon: "adoddle-icon i-start-watching",
    title: this.util.lang.get('watch'),
    callback: (data) => { },
    hidden: false,
    disable: false,
    data: [],
    subMenuItems: [{
      id: "StopWatching",
      icon: "",
      img: '/images/contextmenuImg/stop-watching.svg',
      title: this.util.lang.get('stopwatching'),
      callback: (data) => {
        window['subscribeWatch']({
          watchingFrom: 'folder',
          action: 'stop',
          msg: data.folder_title,
          watchedByResourceTypeId: data.watchDetails.watchedByResourceTypeId,
          watchedId: data.watchDetails.watchedId,
          paramObj: {
            proid: this.util.getProjectId(data),
            foldID: data.folderId
          },
          isBatch: false
        });
        data.isWatching = false;
      },
      hidden: false,
      disable: false,
      data: []
    }, {
      id: "EditSettings",
      icon: "fa fa-cog",
      title: this.util.lang.get('edit_settings'),
      callback: (data) => {
        window['subscribeWatch']({
          watchingFrom: 'folder',
          action: 'getDetails',
          msg: data.folder_title,
          watchedByResourceTypeId: data.watchDetails.watchedByResourceTypeId,
          watchedId: data.watchDetails.watchedId,
          paramObj: {
            proid: this.util.getProjectId(data),
            foldID: data.folderId
          },
          isBatch: false
        });
      },
      hidden: false,
      disable: false,
      data: []
    }]
  }];

  /**
   * @description Sub folder contextmenu object
   * @type {ContextMenu}
   * @memberof DocumentsComponent
   */
  subContextMenuTree: ContextMenu = {
    onInit: (data, event) => {
      this.subContextMenuSubs && this.subContextMenuSubs.unsubscribe();

      if (this.subscriptionPlanId === AppConstant.KEY_LITE_SUBSCRIPTION_PLAN_ID || (data.project && data.project.isTemplate && !data.project.isAdminAccess)) {
        return;
      }

      let items = this.util.copy(this.subContextMenuItemsTree);
      items.forEach((item) => {
        item.data = data;
        item.disable = true;
        item.hidden = false;
        item.subMenuItems && item.subMenuItems.forEach((subMenuItem) => {
          subMenuItem.data = data;
          subMenuItem.disable = true;
          item.hidden = false;
        });
      });

      if (!data) {
        return;
      }

      if(this.folderTreeComponent.isFirstTimeSubContextMenuCalled) {
        this.subContextMenuSubs = this.subContextMenu$.pipe(take(1)).subscribe((permissionData: any) => {
            this.subContextMenuPermissionSet(data, event, items, permissionData);
            this.subContextMenuSubs.unsubscribe();
          }
        );
      } else {
        this.util.getFolderPermission(data, 'folder', (permissionData) => {
          this.subContextMenuPermissionSet(data, event, items, permissionData);
        });
      }

    }
  };

  subContextMenuPermissionSet = (data, event, items, permissionData) => {
    //TODO: Remove this code when move dialog is implemented in Angular
    window['SYSTEMPRIVILEGES'].userPrivileges[this.util.getProjectId(data)] = permissionData.privileges;

    items.forEach((item) => {
      switch (item.id) {
        case "New":
        case "Copy":
          item.disable = false;
          break;

        case "AddFavourite":
          if ((data.project && data.project.isTemplate) || (typeof data.isFavourite != 'undefined' && data.isFavourite) || (typeof data.isFavorite != 'undefined' && data.isFavorite)) {
            item.disable = true;
            item.hidden = true;
          } else {
            item.hidden = false;
            item.disable = false;
          }
          break;

        case "RemoveFavourite":
          if ((data.project && data.project.isTemplate) || (typeof data.isFavourite != 'undefined' && !data.isFavourite) || (typeof data.isFavorite != 'undefined' && !data.isFavorite)) {
            item.disable = true;
            item.hidden = true;
          } else {
            item.hidden = false;
            item.disable = false;
          }
          break;

        case "EditFolder":
          item.disable = !this.util.hasFolderPermission(permissionData.folderPermissionValues, data.folderId, 'FOLDER_ADMIN_PERMISSION');
          break;

        case "ViewFolderPermission":
          item.disable = !(this.util.hasFolderPermission(permissionData.folderPermissionValues, data.folderId, 'FOLDER_ADMIN_PERMISSION') || this.util.hasAccess(permissionData.privileges, 'PRIV_CAN_ACESS_AUDIT_INFO'));
          break;

        case "MoveFolder":
          item.disable = (data.project && data.project.isTemplate) || !this.util.hasFolderPermission(permissionData.folderPermissionValues, data.folderId, 'FOLDER_ADMIN_PERMISSION') || !((data.clonedFolderId == undefined || data.clonedFolderId == 0 || data.clonedFolderId == false) || (data.isFolderStructureInherited == undefined || data.isFolderStructureInherited == false || data.isFolderStructureInherited == 0));
          item.hidden = (data.project && data.project.isTemplate);
          break;

        case "StartWatching":
          item.disable = false;
          item.hidden = (data.project && data.project.isTemplate) || permissionData.watchDetails.isWatching;
          break;

        case "Watch":
          item.disable = false;
          item.hidden = (data.project && data.project.isTemplate) || !permissionData.watchDetails.isWatching;
          break;
      }

      item.subMenuItems && item.subMenuItems.forEach((subMenuItem) => {
        switch (subMenuItem.id) {
          case "CopyName":
          case "CopyLocation":
            subMenuItem.disable = false;
            break;

          case "CopyLink":
            subMenuItem.disable = false;
            subMenuItem.hidden = (data.project && data.project.isTemplate);
            break;

          case "CopyFolderStructure":
            subMenuItem.disable = (data.project && data.project.isTemplate) || !this.util.hasFolderPermission(permissionData.folderPermissionValues, data.folderId, 'FOLDER_ADMIN_PERMISSION') || !((data.clonedFolderId == undefined || data.clonedFolderId == 0 || data.clonedFolderId == false) || (data.isFolderStructureInherited == undefined || data.isFolderStructureInherited == false || data.isFolderStructureInherited == 0));
            subMenuItem.hidden = (data.project && data.project.isTemplate);
            break;

          case "StopWatching":
          case "EditSettings":
            data.watchDetails = permissionData.watchDetails;
            subMenuItem.disable = false;
            subMenuItem.hidden = (data.project && data.project.isTemplate);
            break;

          case "AddSubFolder":
            subMenuItem.disable = !this.util.hasFolderPermission(permissionData.folderPermissionValues, data.folderId, 'FOLDER_ADMIN_PERMISSION') || !((data.clonedFolderId == undefined || data.clonedFolderId == 0 || data.clonedFolderId == false) || (data.isFolderStructureInherited == undefined || data.isFolderStructureInherited == false || data.isFolderStructureInherited == 0));
            break;

          case "CreateFile":
          case "AddFiles":
            subMenuItem.hidden = (data.project && data.project.isTemplate);
            subMenuItem.disable = (data.project && data.project.isTemplate) || !(this.util.hasFolderPermission(permissionData.folderPermissionValues, data.folderId, 'FOLDER_ADMIN_PERMISSION') ||
              this.util.hasFolderPermission(permissionData.folderPermissionValues, data.folderId, 'FOLDER_PUBLISH_AND_LINK_PERMISSION') ||
              this.util.hasFolderPermission(permissionData.folderPermissionValues, data.folderId, 'FOLDER_PUBLISH_PERMISSION'));
            if(!subMenuItem.disable && subMenuItem.id == "CreateFile"){
              subMenuItem.disable = !this.util.hasAccess( permissionData.privileges,'CAN_CREATE_FILES_FROM_DOCUMENT_TEMPLATES');
            }
            break;

          case "AddPlaceholder":
            subMenuItem.hidden = (data.project && data.project.isTemplate);
            subMenuItem.disable = (data.project && data.project.isTemplate) || !((this.util.hasFolderPermission(permissionData.folderPermissionValues, data.folderId, 'FOLDER_ADMIN_PERMISSION') ||
              this.util.hasFolderPermission(permissionData.folderPermissionValues, data.folderId, 'FOLDER_PUBLISH_AND_LINK_PERMISSION') ||
              this.util.hasFolderPermission(permissionData.folderPermissionValues, data.folderId, 'FOLDER_PUBLISH_PERMISSION')) &&
              (this.util.hasAccess(permissionData.privileges, 'PRIV_MANAGE_PROJECT_PLACEHOLDERS') ||
                this.util.hasAccess(permissionData.privileges, 'PRIV_MANAGE_ORGANIZATION_PLACEHOLDERS')))

            break;
        }
      });
    });

    this.contextMenuService.init(items, event);
  }

  /**
   * @description Data of selected node in tree
   * @type {*}
   * @memberof DocumentsComponent
   */
  treeSelection: any = {};



  /**
   * @description Switch between Files & Distribution & aMessages
   * @type {*}
   * @memberof DocumentsComponent
   */
  multipleListings: any = {
    selected: {},
    list: [{
      title: this.util.lang.get('files'),
      id: AppConstant.FILE_LISTING
    },
    {
      title: this.util.lang.get('distribution'),
      id: AppConstant.FILE_TRANSMITTAL_LISTING
    },
    {
      title: this.util.lang.get('comments-label'),
      id: AppConstant.DISCUSSION_LISTING
    }]
  }

  /**
   * Global project link function data
   *
   * @private
   * @memberof DocumentsComponent
   */
  private prjLinkFunParamObj;

  /**
   * @description constructor method of DocumentsComponent
   * @memberof DocumentsComponent
   */

  routerExtraDataFromModelsTab: any;

  constructor(private routeData: RouteDataService, private router: ActivatedRoute, private translate: TranslateService, private _vcr: ViewContainerRef,
    public dialog: AdoddleCommonDialogService, private util: CommonUtilService, private listing: ListingApiService, private zone: NgZone, private broadcast: BroadcastService,
    private contextMenuService: ContextMenuService, private actions: ActionCompleteService, private activity: ActivityService, private validate: ValidationService,private location:Location,
    private _clipboardService: ClipboardService, private placeholderService: PlaceholderService, private fileFormUtilService: FileFormUtilService, private tourService:TourService, private cognitiveCdeService: CognitiveCdeService) {
    translate.setTranslation((<any>window).USP.languageId, (<any>window).Language.getLangObj());
    translate.setDefaultLang((<any>window).USP.languageId);

    window['updateSelectedRowDataForm'] = (detail, refreshAll) => {
      this.zone.run(() => {
        let rows;
        if (!refreshAll && detail && (detail[1] || detail[2])) {
          rows = this.tableListingComponent.getListingDataFromUniqId(detail[1] || detail[2]);
        }
        this.updateSelectedRowData(refreshAll, rows);
      });
    };
    window['updateProjectFormLeftNav'] = () => {
      this.zone.run(() => {
        this.getLeftNavCounts();
      });
    };
    window['completeDelegateAction'] = (data) => {
      this.zone.run(() => {
        this.completeDelegateAction(data);
      });
    };

    this.cognitiveCDESelectedMessageSubscriber = cognitiveCdeService.getSelectedMsgData().subscribe((data)=>{
      if(data){
        this._showCognitiveCDERefs = true;
        let formattedData = this.cognitiveCdeService.prepareCognitiveDataForHighlightAndListing(data);
        window['cognitiveCDEMessageData'] = formattedData[0];
        this.cognitiveCDEReferenceData = Object.entries(formattedData[1]).map(([revisionId, relevancy]) => ({
          revisionId,
          relevancy
        }));
        this.fetchCognitiveCDEListing();
        // refresh the table listing
      } else {
        this._showCognitiveCDERefs = false;
        this._showCognitiveCDERefsLoader = false;
      }
    });


    this.cognitiveCdeService.getSelectedProject().subscribe((projectData)=>{
      if(!projectData){
        this._showCognitiveCDERefs = false;
        this._showCognitiveCDERefsLoader = false;
      }
      this.vectorizedProjectData = projectData;
      this._isCognitiveProjectvectorized = (projectData?.vectorizationStatus == VECTORIZATION_STATES.COMPLETED && !!projectData?.projectName);
    });

    /**
     * User clicks on Open in Webapp option from aSync app, after successful web-socket connection, we will get
     * rquired values here and we are using those values to open file in new tab(same as View) functionality
     */
    this.openInWebApp = this.broadcast.on('OPEN_IN_WEBAPP').subscribe((revisionData: any) => {
      if (revisionData['notify-user'] != window['USP'].userID) {
        return;
      }

      revisionData['notify-data'].forEach(function (file) {
        file.projectId = file.projectID;
        file.folderId = file.folderID;
        file.revisionId = file.revisionID;
        file.documentId = file.documentID;

        file.documentTypeId = file.documentTypeId.split('$$')[0];
        file.hasOnlineViewerSupport = file.hasOnlineViewerSupport.split('$$')[0];
        file.viewerId = file.viewerId.split('$$')[0];
        file.dcId = file.dcId.split('$$')[0];

        delete file.projectID;
        delete file.folderID;
        delete file.revisionID;
        delete file.documentID;
      });
      if (!revisionData['notify-data'][0].revisionId || !revisionData["notify-data"][0].projectId || !revisionData["notify-data"][0].folderId || !revisionData["notify-data"][0].documentId) {
        return;
      }
      console.log('notify data 1717:- ', revisionData['notify-data'][0]);
      this.listing.openDocumentTab(revisionData['notify-data'][0]);
    });

    // recever for async app notifications for files.
    this.broadcast.on('ASYNC_FILE_PARTIAL_REFRESH').subscribe((revisionData: any) => {
      if (revisionData['notify-user'] != window['USP'].userID) {
        return;
      }

      revisionData['notify-data'].forEach(function (file) {
        file.dcId = 1;
        file.projectId = file.projectID;
        file.folderId = file.folderID;
        file.revisionId = file.revisionID;
      });

      // refresh whole file list refresh specific file contect
      // this.updateRowData(revisionData['notify-data'] || []);
      this.updateSelectedRowData(true);
    });

    window['updateSelectedRowData'] = this.updateSelectedRowData.bind(this);
  }

  prepareDataForRefresh(revisionData) {
    let rowList = [];

    return rowList;
  }

  routerExtraData: any = {};

  lastLoginFilterData: any = {};

  // after clicking on Open File Edit in newly added aDrive option, we are getting data in this variable
  aSyncData: any = {};

  /**
   * @description Identify the cognitive cde tab reference listing is visible or not
   * @memberof DocumentsComponent
   */
  _showCognitiveCDERefs = false;
  _showCognitiveCDERefsLoader = false;
  
  /**
   * @description Identify the Cognitive CDE Tab is Active or not
   * @memberof DocumentsComponent
   */
  _cognitiveCdeTabActive = false;

  /**
   * @description Hold the available project tree data
   * @memberof DocumentsComponent
   */
  _projectTreeData = [];

  _isCognitiveProjectvectorized = false;

  private vectorizedProjectData = {};

  /**
   * @description Hold the sort order of cognitiveCDE tab
   * @memberof DocumentsComponent
   */
  private cognitiveCDESortOrder = '';

  /**
   * @description Hold the reference data of cognitive cde message
   * @memberof DocumentsComponent
   */
  private cognitiveCDEReferenceData = [];

  /**
   * @description Hold the cognitive cde select message subject subscriber
   * @memberof DocumentsComponent
   */
  private cognitiveCDESelectedMessageSubscriber;

  private selectedFilterId  = 0;

  /**
   * @description implementation method of DocumentsComponent
   * @memberof DocumentsComponent
   */
  ngOnInit() {
    window.document.title = this.util.lang.get('files');
    // Used to directly open the Cognitive CDE tab when a project is selected from the Cognitive CDE tab
    window['isCognitiveTabActive'] = !!window['isCognitiveTabOpened'];
    this.loadCognitiveCDETabBasedOnPermission();

    let queryParamsSubscriber = this.router.queryParams.subscribe(params => {
      if(params?.filterId){
        this.isSharedFilterLink = window['isFilterAppliedViaQR'] = true;
        this.selectedFilterId = params.filterId;
        this.location.replaceState(ApiConstant.DOCUMENT_ROUTE_URL+'?action_id=1');
        queryParamsSubscriber.unsubscribe();
      }
    });

    this.appType = this.router.snapshot.data.appType;
    this.multipleListings.selected = this.multipleListings.list[0];
    this.export.data.appType = this.appType;
    this.export.filter.appType = this.appType;

    this.routerExtraData = this.routeData['routeData'];
    this.routeData.clear();

    let assocDocByFormIdData = null;
    try {
      assocDocByFormIdData = JSON.parse(localStorage.getItem("assocDocByFormId"));
    } catch (e) { }
    if (assocDocByFormIdData && assocDocByFormIdData.options) {
      this.routerExtraData = assocDocByFormIdData;
    }

    this.prjLinkFunParamObj = this.$("a#navfiles").data();
    this.$("a#navfiles").removeData();

    this.setFlags();
    this.getLeftNavCounts();
    if(!window['isCognitiveTabActive']){ // verify behaviour of not selected project
      this.setActiveTab(this.leftNavJsonData.all, true);
    }

    if (this.routerExtraData && this.routerExtraData.options) {
      if (this.routerExtraData.options.action === 'NAVIGATE_TO_FOLDER') {
        this.targetFolderId = this.routerExtraData.options.folderId;
        this.targetProjectId = this.routerExtraData.options.projectId;
      } else if (this.routerExtraData.options.action === 'SHOW_IN_FILE_ASSOCATION') {
        this.targetProjectId = this.routerExtraData.options.projectId;
      }
    } else if (this.directAccess) {
      this.targetFolderId = this.directAccess.folderId;
      this.targetProjectId = this.directAccess.projectId;
    }

    switch (this.directAccess && this.directAccess.nlTypeId) {
      case 1:
        this.processLeftNavClick(this.leftNavJsonData.overdue, true);
        break;
      case 2:
        this.processLeftNavClick(this.leftNavJsonData.incomplete, true);
        break;
      case 3:
        this.processLeftNavClick(this.leftNavJsonData.unreadaMessages, true);
        break;
      default:
        break;
    }

    if (this.routerExtraData?.model_id) {
      this.routerExtraDataFromModelsTab = this.routerExtraData;
      this.targetProjectId = this.routerExtraData?.projectId;
    }

    this.directAccess = undefined;
    window['ADODDLE'].directAccessQueryString = undefined;
    this.routerExtraData = undefined;

    //TODO: Replace this code with new Angular when Add/Edit folder component will be created into Angular
    window['addEditFolderInitialize']();

    this.initUploader();
    this.initPublishRevisionUploader();
    this.util.getOfficeActionUrl();

    this.tourService.getTourStatus(TOUR_SCREENS_ENUM.TOUR_FILE_SEARCH);

  }

  /**
   * @description Assign data to _projectTreeData variable
   * @memberof DocumentsComponent
   */
  _setAllProjectData(data) {
    this._projectTreeData = data;
  }

  _openCognitiveCdeSettings() {
    let dialogRef = this.dialog.open(CognitiveSettingsComponent, {
      panelClass:'cognitive-cde-settings-panel',
      disableClose: true,
      maxWidth: '600px',
      minWidth: '600px',
      data: this.vectorizedProjectData
    });
    
    dialogRef.beforeClosed().subscribe((isRefresh?) => {
      if(isRefresh){
        this.cognitiveCdeService.setSelectedProject(null);
        this.cognitiveCdeService.setSelectedMsgData(null);
      }
    });
  }

  /**
   * @description Fetch the permission and based on that show cognitive cde tab
   * @private
   * @memberof DocumentsComponent
   */
  private loadCognitiveCDETabBasedOnPermission() {
    if(window['USP'].isProxyUserWorking){
      return; // Not show cognitive cde tab for proxy user.
    }
    this.util.ajax({
      url: ApiConstant.USER_APP_PRIVILEGES,
      method: 'POST',
      data: window["USP"]?.userID?.split('$$')[0],
      contentType: 'application/json',
      responseType: 'text',
      success: (response) => {
        if(!response.body){ return; }
        let isAdmin = window['CAN_ACCESS_ADMIN_COGNITIVE_CDE'] = this.util.hasAccess(response.body, 'CAN_ACCESS_ADMIN_COGNITIVE_CDE');  
        if(this.util.hasAccess(response.body, 'CAN_ACCESS_COGNITIVE_CDE') || isAdmin) {
          this.multipleListings.list.push({
            title: this.util.lang.get('cognitive-cde'),
            id: AppConstant.COGNITIVE_CDE_LISTING_ACTION,
            svg: '/images/cognitive_cde_tab_icon.svg'
          });

          this.$(window).trigger('resize.filter'); // resize the filter for fix UI of filter row
          
          if(window['isCognitiveTabActive']){ // handle direct show cognitive tab while project not selected
            document.body.classList.add('cognitive-cde-tab-active');
            this._cognitiveCdeTabActive = true;
            this.multipleListings.selected = this.multipleListings.list[this.multipleListings.list.length - 1];
            delete window['isCognitiveTabOpened']
          }
        }
      },
      error: (err) => { }
    });
  }

  private prepareCognitiveCDEListingData(data, order?) {
   return data.sort((a,b)=>{ return order == 'asc' ? (a.relevancy - b.relevancy) : ((b.relevancy - a.relevancy)) });
  }

  /**
   * @description Fetch the listing data for cognitive cde references
   * @param data {undefined || object}
   * @private
   * @memberof DocumentsComponent
   */
  private fetchCognitiveCDEListing(data?) {
    if(!data){
      this.cognitiveCDESortOrder = 'desc';
      this.cognitiveCDEReferenceData = this.prepareCognitiveCDEListingData(this.cognitiveCDEReferenceData, this.cognitiveCDESortOrder);
      data = {};
    }
    if(data?.sortOrder && data.sortOrder != this.cognitiveCDESortOrder){
      this.cognitiveCDESortOrder = data.sortOrder;
      this.cognitiveCDEReferenceData = this.prepareCognitiveCDEListingData(this.cognitiveCDEReferenceData, this.cognitiveCDESortOrder);
    }
    document.body.classList.add('cognitive-cde-tab-active');

    this._cognitiveCdeTabActive = true;

    this.listingXhr && this.listingXhr.unsubscribe();

    this.listingParam.currentPageNo = data.currentPageNo || 1;
    this.listingParam.recordStartFrom = data.recordStartFrom || 0;
    delete data.currentPageNo;
    delete data.recordStartFrom;

    let params = Object.assign({
      currentPageNo: 1,
      recordStartFrom: 0,
      action_id: AppConstant.ADODDLE_LISTING_ACTION,
      controller: ApiConstant.LISTING_CONTROLLER,
      recordBatchSize: this.listing.lazyLoadBatchSize || 21,
      listingType: this.multipleListings.selected.id,
      appType: this.appType
    }, data,  this.listingParam);

    params.recordStartFrom = 0;

    delete params.projectId;
    delete params.folderId;

    params['sortField'] = 'aiRelevancy';
    params['sortFieldType'] = 'text';
    params['sortOrder'] = this.cognitiveCDESortOrder;

    params['sourceRevisionsJson'] = JSON.stringify(this.cognitiveCDEReferenceData.slice(this.listingParam.recordStartFrom, this.listingParam.recordStartFrom + params.recordBatchSize));

    let dcId = params._dcId;
    this.listingXhr = this.util.ajax({
      url: ApiConstant.LISTING_CONTROLLER,
      data: params,
      _dcId: dcId,
      method: 'POST',
      success: (response) => {
        response.body.recordStartFrom = this.listingParam.recordStartFrom;
        response.body.totalDocs = this.cognitiveCDEReferenceData.length;
        this.onListingResponse(response.body, data?.lazyLoaded);
      },
      error: (err) => {
        this.listingXhr = false;
      }, offline: () => {
        this.listingXhr = false;
      }
    });
  }

  /**
   * @description implementation method of DocumentsComponent
   * @memberof DocumentsComponent
   */
  ngOnDestroy() {
    this.listingXhr && this.listingXhr.unsubscribe && this.listingXhr.unsubscribe();
    this.openInWebApp && this.openInWebApp.unsubscribe && this.openInWebApp.unsubscribe();
    delete window['updateSelectedRowDataForm'];
    delete window['updateProjectFormLeftNav'];
    delete window['completeDelegateAction'];
    delete window['isCognitiveTabActive'];
    delete window['isProjectVectorizationStatusChanged'];
    delete window['cognitiveCDEMessageData'];
    document.body.classList.remove('cognitive-cde-tab-active');
    this.cognitiveCDESelectedMessageSubscriber && this.cognitiveCDESelectedMessageSubscriber.unsubscribe();

    this.partialRefreshTimers && this.partialRefreshTimers.forEach((timeHandler) => {
      clearTimeout(timeHandler);
    });
    this.partialRefreshTimers = [];
    window['isFileContentSearchOnly'] = false;
  }

  /**
   * @description Set user available flags
   * @memberof DocumentsComponent
   */
  setFlags() {
    this.util.ajax({
      url: ApiConstant.HOME_CONTROLLER,
      method: 'POST',
      data: { action_id: AppConstant.GET_USER_FLAG },
      _cdnUrl: this.util.getTabServiceUrl(this.util.TabServices.FILES),
      success: (response) => {
        if (!response.body) {
          return;
        }

        let userFlags = response.body;
        let flagMenu: any = this.contextMenuItems.filter((val) => {
          return val.id == "Flag"
        })[0];
        for (const flag in userFlags) {
          if (userFlags.hasOwnProperty(flag)) {
            let flagInt = parseInt(flag);
            if (flagInt === 0) {
              continue;
            }
            flagMenu.subMenuItems.push({
              id: flagInt,
              icon: "",
              img: `/images/flag_type/flag_${flagInt}.png`,
              title: userFlags[flagInt],
              callback: (data: any) => {
                this.updateFlag(data, flagInt);
              },
              hidden: false,
              disable: false,
              data: []
            })
          }
        }
        flagMenu.subMenuItems.push({
          id: 0,
          img: '/images/flag_type/flag_0.png',
          icon: "",
          title: this.util.lang.get("clear-flag"),
          callback: (data: any) => {
            this.updateFlag(data, 0);
          },
          hidden: false,
          disable: false,
          data: []
        });
      }
    });
  }

  /**
   * @description Update flag for Selected Data
   * @param {*} data
   * @param {*} flag
   * @memberof DocumentsComponent
   */
  updateFlag(data, flag) {
    let projectViseObj = {};

    let obj: any = {
      action_id: AppConstant.UPDATE_FLAG,
      paramDetail: {
        flagTypeId: flag,
        entityTypeId: ENTITY_TYPE.FILE,
        userId: window['USP'].userID,
        flagVO: [],
      }
    }

    data.forEach((element) => {
      !projectViseObj[element['projectId']] && (projectViseObj[element['projectId']] = []);
      element['revisionId'] && projectViseObj[element['projectId']].push(element['revisionId']);
    });

    for (const projectId in projectViseObj) {
      if (projectViseObj.hasOwnProperty(projectId)) {
        const element = projectViseObj[projectId];
        obj.paramDetail.flagVO.push({
          projectId: projectId,
          ids: element.join(',')
        });
      }
    }
    obj.paramDetail = JSON.stringify(obj.paramDetail);
    this.actionsXhr = true;
    this.util.ajax({
      url: ApiConstant.HOME_CONTROLLER,
      method: 'POST',
      data: obj,
      _cdnUrl: this.util.getTabServiceUrl(this.util.TabServices.FILES),
      _dcId: data[0].dcId,
      responseType: 'text',
      success: (response) => {
        this.actionsXhr = false;
        if (response.body != 'Success') {
          return;
        }

        data.forEach((item) => {
          item.flagType = flag;
          item.flagTypeImageName = `flag_type/flag_${flag}.png`;
          this.tableListingComponent.setUpdatedRowData(item);
        });

        let fragTag = this.util.lang.get("no-flag");
        if (flag === 1) {
          fragTag = this.util.lang.get("high");
        } else if (flag === 2) {
          fragTag = this.util.lang.get("medium");
        } else if (flag === 3) {
          fragTag = this.util.lang.get("low");
        }

        this.util.notification.success({
          theClass: 'notification-sm',
          msg: this.util.lang.get("flag-updated-successfully").replace("{0}", fragTag)
        });
      },
      error: (err) => {
        this.actionsXhr = false;
      }
    });
  }

  /**
   * @description Fetch context menu data to show the menu items
   * @param {*} data
   * @param {*} event
   * @memberof DocumentsComponent
   */
  createContextMenuData(data: any, event: any, isProjectModelExist: boolean = false) {

    this.actionsXhr = true;
    this.isProjectModelExist = isProjectModelExist;

    this.getContextMenuItems(data, (items) => {
      this.actionsXhr = false;
      this.contextMenuService.init(items, event);
    });
  }

  /**
   * @description Filter and modify contextmenu items
   * @param {*} data
   * @memberof DocumentsComponent
   */
  getContextMenuItems(data, callback) {
    if (this.subscriptionPlanId === AppConstant.KEY_LITE_SUBSCRIPTION_PLAN_ID) {
      return;
    }

    if (this.multipleListings.selected.id == AppConstant.FILE_LISTING || this.multipleListings.selected.id == AppConstant.COGNITIVE_CDE_LISTING_ACTION) {
      this.setFilesListingContextMenu(this.util.copy(this.contextMenuItems), data, callback);
      return;
    }

    if (this.multipleListings.selected.id == AppConstant.FILE_TRANSMITTAL_LISTING) {
      this.setTransmittleListingContextMenu(this.util.copy(this.contextMenuItemsTransmittle), data, callback);
      return;
    }

    if (this.multipleListings.selected.id == AppConstant.DISCUSSION_LISTING) {
      this.setaMessageListingContextMenu(this.util.copy(this.contextMenuItemsMessage), data, callback);
      return;
    }

  }

  /**
   * @description Filter and modify contextmenu items for Distribution
   * @param {Array<ContextMenuItem>} items
   * @param {*} selectedRowData
   * @memberof DocumentsComponent
   */
  setTransmittleListingContextMenu(items: Array<ContextMenuItem>, selectedRows, callback) {
    items.forEach((parent) => {
      parent.data = selectedRows;
      parent.disable = true;
    });

    if (!selectedRows.length || this.hasMultipleProjects(selectedRows)) {
      callback(items);
      return;
    }

    this.util.getProjectPermission(this.util.getProjectId(selectedRows[0]), selectedRows[0].dcId, (data) => {
      let isPrivateData = false;
      for (let i = 0; i < selectedRows.length; i++) {
        if (selectedRows[i].isPrivate) {
          isPrivateData = true;
          break;
        }
      }
      items.forEach((parent) => {
        switch (parent.id) {
          case "ClearActions":
            if (this.hasClearDelegateTransAction({
              status: AppConstant.ACTION_INCOMPLETE,
              priv: data.privileges,
              selectedFileData: selectedRows,
              isClearPriv: true
            })) {
              if (this.util.hasAccess(data.privileges, "CAN_EDIT_VISIBILITY_OF_OBJECTS") && data.isObjectPrivacyEnable == "true" && isPrivateData) {
                parent.enabledTooltip = this.util.lang.get('visibility-remove-note');
              }
              parent.disable = false;
            }
            break;
          case "DelegateActions":
            if (this.hasClearDelegateTransAction({
              status: AppConstant.ACTION_INCOMPLETE,
              priv: data.privileges,
              selectedFileData: selectedRows
            })) {
              parent.disable = false;
            }
            break;
          case "DeactivateActions":
            if (this.hasDEREActiveTransAction({
              "ownOrg": this.util.hasAccess(data.privileges, 'CAN_DEACTIVATE_DOCUMENT_ACTIONS_OWN_ORG'),
              "allOrg": this.util.hasAccess(data.privileges, 'CAN_DEACTIVATE_DOCUMENT_ACTIONS_ALL_ORG'),
              "status": AppConstant.ACTION_INACTIVE,
              "invert": true,
              selectedFileData: selectedRows,
            })) {
              if (this.util.hasAccess(data.privileges, "CAN_EDIT_VISIBILITY_OF_OBJECTS") && data.isObjectPrivacyEnable == "true" && isPrivateData) {
                parent.enabledTooltip = this.util.lang.get('visibility-remove-note');
              }
              parent.disable = false;
            }
            break;
          case "ReactivateActions":
            if (this.hasDEREActiveTransAction({
              "ownOrg": this.util.hasAccess(data.privileges, 'CAN_REACTIVATE_DOCUMENT_ACTIONS_OWN_ORG'),
              "allOrg": this.util.hasAccess(data.privileges, 'CAN_REACTIVATE_DOCUMENT_ACTIONS_ALL_ORG'),
              "status": AppConstant.ACTION_INACTIVE,
              selectedFileData: selectedRows,
            })) {
              parent.disable = false;
            }
            break;
        }
      });
      callback(items);
    });

  }

  /**
   * @description Filter and modify contextmenu items for aMessages
   * @param {Array<ContextMenuItem>} items
   * @param {*} selectedRowData
   * @memberof DocumentsComponent
   */
  setaMessageListingContextMenu(items: Array<ContextMenuItem>, selectedRows, callback) {

    items.forEach((parent) => {
      parent.data = selectedRows;
      parent.disable = true;
      parent.subMenuItems && parent.subMenuItems.forEach((subMenuItem) => {
        subMenuItem.data = selectedRows;
        subMenuItem.disable = true;
      });
    });

    if (!selectedRows.length) {
      callback(items);
      return;
    }

    this.util.getLockedActivities({
      revisonId: selectedRows[0].revisionId,
      projectId: this.util.getProjectId(selectedRows[0]),
      dcId: selectedRows[0].dcId,
      success: (response) => {
        let isArchivedProjectSelected = this.hasArchivedProjectSelected(selectedRows);
        let isEnableCommentReview = (selectedRows[0]?.customObjectTemplateGroupId?.split("$$")[0] == 2 ? true : false);
        items.forEach((parent) => {
          switch (parent.id) {
            case "New":
            case "MarkAsRead":
              parent.disable = isArchivedProjectSelected;
              break;
            case "View":
            case "NavigateToFolder":
            case "Copy":
              parent.disable = selectedRows.length != 1;
              break;
          }

          parent.subMenuItems && parent.subMenuItems.forEach((subMenuItem) => {
            switch (subMenuItem.id) {
              case "CopyMsgName":
              case "CopyLocation":
                subMenuItem.disable = false;
                break;
              case "Reply":
                subMenuItem.disable = response.comment || selectedRows.length != 1 || isArchivedProjectSelected;
                subMenuItem.hidden = isEnableCommentReview;
                break;
              case "AssociateDiscusions":
                subMenuItem.disable = isArchivedProjectSelected;
                break;
            }
          });
        });
        callback(items);
      },
      error: (err) => {
        callback(items);
      }
    });

  };

  /**
   * @description Filter and modify contextmenu items for file listing
   * @param {Array<ContextMenuItem>} items
   * @param {*} selectedRowData
   * @param {*} callback
   * @memberof DocumentsComponent
   */
  async setFilesListingContextMenu(items: Array<ContextMenuItem>, selectedRowData, callback) {
    items.forEach((parent) => {
      parent.data = selectedRowData;
      parent.disable = true;
      parent.subMenuItems && parent.subMenuItems.forEach((subMenuItem) => {
        subMenuItem.data = selectedRowData;
        subMenuItem.disable = true;
      });
    });

    if (!selectedRowData.length) {
      callback(items);
      return;
    }

    // Permission for User Privileges for Right Click
    const userPrivFileRightClick = await this.util.getUserAppPrivileges();

    this.util.getFolderPermissions(selectedRowData, (permissionData) => {
      this.selectedFolderPermissionData = permissionData;
      window['permissionValues'] = window['permissionValues'] || {};
      selectedRowData.forEach((row) => {
        let folderId = row.folderId.split('$$')[0];
        window['permissionValues'][row.folderId] = permissionData.folderPermissionValues[folderId];
      });

      let actionCompleteStatus = this.getActionCompleteStatusData(selectedRowData);
      let lockedObjects = this.util.getLockedActivitiesObject(permissionData.lockedObjectIds);
      let isMultiProjectSelected = this.hasMultipleProjects(selectedRowData);
      let isArchivedProjectSelected = this.hasArchivedProjectSelected(selectedRowData);
      let isAnyActiveDocSelected = this.hasAnyDocIsActive(selectedRowData);
      let areAllWorkflowRunningOrPlaceholder = this.checkAllFilesAreActiveWorkflowOrPlacholder(selectedRowData);

      if (this.util.hasAccess(permissionData.privileges, 'PRIV_ASSIGN_DOCUMENT_ATTRIBUTES')) {
        this.disabledFilesList = [];
      } else {
        this.disabledFilesList = this.getDisabledFileList(selectedRowData) || [];
      }

      const hasViewerDisabledFilesSelected = this.hasViewerDisabledProjectFilesSelected(selectedRowData);
      const hasViewerNotSupportedFile = this.hasViewerNotSupportedFileSelected(selectedRowData);
      const hasOnly3DRepoViewerSupportedFile = this.hasOnly3DRepoViewerSupportedFileSelected(selectedRowData);
      const hasOnlyHoopsViewerEnabledFilesSelected = this.hasOnlyHoopsViewerEnabledProjectFilesSelected(selectedRowData);
      const hasOnly3DRepoViewerEnabledFilesSelected = this.hasOnly3DRepoViewerEnabledProjectFilesSelected(selectedRowData);
      const hasOnlyValidSuportFiles = this.hasOnlyValidSuportFilesSelected(selectedRowData)

      items.forEach((parent) => {
        switch (parent.id) {
          case "New":
          case "Flag":
          case "Edit":
          case "Share":
          case "MoreOptions":
          case "aSync":
          case "Tasks":
            parent.disable = false;
            break;
          case "DownloadFiles":
            parent.disable = !this.hasPermissionToDownload(selectedRowData, permissionData);
            break;
          case "Checkout":
            parent.disable = !this.util.hasAccess(permissionData.privileges, 'PRIV_CAN_DOWNLOAD_DOCUMENTS');
            parent.hidden = !this.hasPermissionToCheckoutFile(selectedRowData, permissionData.folderPermissionValues);
            break;
          case "Undocheckout":
            if(selectedRowData.length == 1 && selectedRowData[0].hasOfficeFile){
              this.disableUndoCheckout = !(this.util.hasAccess(userPrivFileRightClick, 'ENABLE_MICROSOFT_OFFICE') && permissionData.projectADriveCDEAddonPurchaseStatus);
            }
            parent.title = (!this.disableUndoCheckout || selectedRowData[0].hasOfficeFile) &&  selectedRowData[0].hasOfficeFile ?  this.util.lang.get('discard-changes') : this.util.lang.get('undocheckout');
            parent.disable = this.disableUndoCheckout || selectedRowData.length > 1 ? selectedRowData.some(row => row.hasOfficeFile) : false;
            parent.hidden = !this.hasPermissionToUndoCheckoutFile(selectedRowData) || (selectedRowData.length > 1 ? selectedRowData.some(row => row.hasOfficeFile) : false);
            let allHaveOfficeFile = selectedRowData.every(function(obj) {
              return obj.hasOwnProperty('hasOfficeFile') && obj.hasOfficeFile === true;
            });

            if(allHaveOfficeFile) {
              parent.hidden = !this.hasPermissionToUndoCheckoutFile(selectedRowData) || false;
            }
            break;
          case "View":
            parent.disable = selectedRowData.length != 1;
            break;
          case "Copy":
            parent.disable = false;
            break;
          case "History":
            parent.disable = selectedRowData.length != 1 || !this.util.hasAccess(permissionData.privileges, 'PRIV_CAN_ACESS_AUDIT_INFO');
            break;
          case "Compare":
            parent.disable = selectedRowData.length != 2;

            if (selectedRowData.length == 2) {
              let notSupportedType = this.viewerTypeNoServer.split(',') || [];
              let notSupportedList = [];
              notSupportedType.forEach((type) => {
                notSupportedList.push('.' + type.toUpperCase());
              });
              let rows0FileExt = selectedRowData[0].uploadFilename.substr(selectedRowData[0].uploadFilename.lastIndexOf('.'));
              let rows1FileExt = selectedRowData[1].uploadFilename.substr(selectedRowData[1].uploadFilename.lastIndexOf('.'));
              if ((rows0FileExt && notSupportedList.indexOf(rows0FileExt.toUpperCase()) != -1) || (rows1FileExt && notSupportedList.indexOf(rows1FileExt.toUpperCase()) != -1)) {
                parent.hidden = true;
              }
            }
            break;
          case "StartWatching":
            parent.disable = false;
            parent.hidden = permissionData.watchDetails.isWatching;
            break;
          case "Watch":
            parent.disable = false;
            parent.hidden = !permissionData.watchDetails.isWatching;
            break;
          case "ActivityLocks":
            parent.title = (selectedRowData.length == 1 && selectedRowData[0].lockCount > 0) ? this.util.lang.get('activity-locks') + " (" + selectedRowData[0].lockCount + ") " : this.util.lang.get('activity-locks');
            parent.disable = isArchivedProjectSelected || isMultiProjectSelected || (selectedRowData.length == 1 && !this.util.hasFolderPermission(permissionData.folderPermissionValues, selectedRowData[0].folderId, 'PRIV_WORKSPACE_ADMIN'));
            break;
          case "SaveAsPDF":
            parent.disable = isMultiProjectSelected || (permissionData.folderPermissionValues.Project_viewer_id != 5 && permissionData.folderPermissionValues.Project_viewer_id != 7) || !this.hasPermissionToDownload(selectedRowData, permissionData);
            //parent.hidden = permissionData.folderPermissionValues.Project_viewer_id == 7;
            break;
          case "SignWithDocuSign":
          case "ViewWithDocuSign":
            parent.disable =  selectedRowData.length != 1 || !this.hasSupportForDocusign(selectedRowData[0]) || !this.util.hasAccess(permissionData.privileges, 'PRIV_CAN_SEND_TO_DOCUSIGN');
            parent.hidden = this.selectedFolderPermissionData.enableDocusignIntegration ? false : true;
            break;
          case "MoveFiles":
            parent.disable = isMultiProjectSelected || !this.hasPermissionToMoveFile(selectedRowData, permissionData);
            break;
          case "DeleteFiles":
            const isFileDeleteBtnDisabled = this.isFileDeleteBtnDisable(isMultiProjectSelected, userPrivFileRightClick, selectedRowData[0].ownerOrgId);
            parent.disable = isFileDeleteBtnDisabled;
            parent.hidden = isFileDeleteBtnDisabled;
            break;
          case "LinkFile":
            parent.disable = isArchivedProjectSelected || isMultiProjectSelected || !this.hasPermissionToLinkDoc(selectedRowData, permissionData.folderPermissionValues);
            break;
          case "PrintFile":
            parent.disable = isMultiProjectSelected || !this.hasPermissionToPrint(selectedRowData,permissionData);
            break;
          case "Visibility":
            parent.disable = selectedRowData.length > 1 || !this.util.hasAccess(permissionData.privileges, 'CAN_EDIT_VISIBILITY_OF_OBJECTS');
            parent.hidden = selectedRowData.length != 1 || !selectedRowData[0].isPrivate || !permissionData.isObjectPrivacyEnable;
            break;
          case "StartWorkflow":
            parent.disable = isMultiProjectSelected || areAllWorkflowRunningOrPlaceholder || !this.util.hasAccess(permissionData.privileges, 'PRIV_MANAGE_WORKFLOW_RULES') || !isAnyActiveDocSelected;
            break;
          case "ModelOptions":
            parent.hidden = isArchivedProjectSelected || hasViewerDisabledFilesSelected || hasViewerNotSupportedFile || !hasOnlyValidSuportFiles;
            parent.disable = parent.hidden;
            break;
          case "editInBrowserApp":
            parent.hidden = !permissionData.projectADriveCDEAddonPurchaseStatus || selectedRowData[0].documentTypeId !== DOC_TYPE_ID.O365_PLACEHOLDER;
            parent.disable = !(selectedRowData.length === 1) || 
                !this.util.hasAccess(userPrivFileRightClick, 'ENABLE_MICROSOFT_OFFICE') || 
                (selectedRowData.length > 0 && (
                    !this.checkFolderUploadPermission(permissionData, selectedRowData[0].folderId) || 
                    !selectedRowData[0].isActive
                ));
            this.fileExtName = this.util.getExt(selectedRowData[0].uploadFilename).toLowerCase();
            this.officeFileDetails = this.fileFormUtilService.getOfficeFileDetails(this.fileExtName);
            parent.title = this.officeFileDetails?.btnName;
            if(parent.disable){
              parent.disabledTooltip = this.util.lang.get('tooltip-edit-file-in-microsoft-office-button');
              parent.hideInfoIcon = true;
            }
            break;
          case "discardChanges":
            parent.hidden = !permissionData.projectADriveCDEAddonPurchaseStatus || selectedRowData[0].documentTypeId !== DOC_TYPE_ID.O365_PLACEHOLDER;
            parent.disable = !(selectedRowData.length === 1) || 
                !this.util.hasAccess(userPrivFileRightClick, 'ENABLE_MICROSOFT_OFFICE') || 
                (selectedRowData.length > 0 && 
                    !(window['USP'].userID.split("$$")[0] == selectedRowData[0].checkOutUserId) && 
                    !selectedRowData[0].isUserFolderAdmin
                );
            break;
          case "publishChanges":
            parent.hidden = !permissionData.projectADriveCDEAddonPurchaseStatus || selectedRowData[0].documentTypeId !== DOC_TYPE_ID.O365_PLACEHOLDER;
            parent.disable = !(selectedRowData.length === 1) || 
                !this.util.hasAccess(userPrivFileRightClick, 'ENABLE_MICROSOFT_OFFICE') || 
                (selectedRowData.length > 0 && !this.checkFolderUploadPermission(permissionData, selectedRowData[0].folderId));
            break;
        }

        if(selectedRowData[0].documentTypeId === DOC_TYPE_ID.O365_PLACEHOLDER && parent.id !== "editInBrowserApp" && parent.id !== "discardChanges" && parent.id !== "publishChanges") {
          parent.hidden = selectedRowData[0].documentTypeId === DOC_TYPE_ID.O365_PLACEHOLDER ? true : parent.hidden;
        }

        parent.subMenuItems && parent.subMenuItems.forEach((subMenuItem) => {
          switch (subMenuItem.id + '') {
            case "Name":
            case "CopyLocation":
            case "AllHistory":
            case "DistributionHistory":
            case "RevisionsHistory":
            case "StatusHistory":
              subMenuItem.disable = selectedRowData.length != 1;
              break;
            case "SignatoriesHistory":
              subMenuItem.disable = selectedRowData.length != 1 || !this.hasSupportForDocusign(selectedRowData[0]) || !this.util.hasAccess(permissionData.privileges, 'PRIV_CAN_SEND_TO_DOCUSIGN');
              subMenuItem.hidden = !this.selectedFolderPermissionData.enableDocusignIntegration;
              break;
            case '0':
            case '1':
            case '2':
            case '3':
              subMenuItem.disable = isArchivedProjectSelected;
              break;
            case 'PublishRevision':
              const publishRevisionData = this.hasPermissionToPublishRevision(selectedRowData, permissionData);
              const pubilshMSRevision = this.hasPermissionToPublishMSRevision(selectedRowData,userPrivFileRightClick,permissionData);
              subMenuItem.disable = !publishRevisionData.hasPermission || !pubilshMSRevision;
              subMenuItem.disabledTooltip = publishRevisionData.disabledTooltip;
              break;
            case "DistributeFile":
              subMenuItem.disable = (lockedObjects.distribution && selectedRowData.length == 1) || isArchivedProjectSelected || isMultiProjectSelected || !isAnyActiveDocSelected;
              break;
            case "CopyDirectLink":
              subMenuItem.disable = selectedRowData.length != 1 || isArchivedProjectSelected || !isAnyActiveDocSelected || !this.util.hasAccess(permissionData.privileges, 'PRIV_CAN_DOWNLOAD_DOCUMENTS') || this.hasOnlyViewAccess(selectedRowData, permissionData);
              break;
            case "CompareFiles":
              subMenuItem.disable = selectedRowData.length != 2;
              break;
            case "CompareText":
              subMenuItem.disable = selectedRowData.length != 2 || selectedRowData[0].projectViewerId == 7 || (this.util.ieVer() != 0 && <number>this.util.ieVer() < 11);
              subMenuItem.hidden = selectedRowData[0].projectViewerId == 7;
              break;
            case "StopWatching":
            case "EditSettings":
              subMenuItem.disable = false;
              subMenuItem.hidden = !permissionData.watchDetails.isWatching;
              selectedRowData.forEach((row) => {
                row.watchDetails = permissionData.watchDetails;
              });
              break;
            case "ForInfo":
              subMenuItem.disable = actionCompleteStatus[DOC_ACTIONS.FOR_INFORMATION];
              break;
            case "ForAck":
              subMenuItem.disable = actionCompleteStatus[DOC_ACTIONS.FOR_ACKNOWLEDGEMENT];
              break;
            case "ForCommentCoordination":
              subMenuItem.disable = actionCompleteStatus[DOC_ACTIONS.FOR_COMMENT_COORD];
              break;
            case "ForCommentIncorporation":
              subMenuItem.disable = actionCompleteStatus[DOC_ACTIONS.FOR_COMMENT_INCORP];
              break;
            case "ForAction":
              subMenuItem.disable = actionCompleteStatus[DOC_ACTIONS.FOR_ACTION];
              break;
            case "StartDiscussion":
            case "NoDiscussion":
              subMenuItem.hidden = this.selectedFolderPermissionData.enableCommentReview ? true : false;
              subMenuItem.disable = (lockedObjects.comment && selectedRowData.length == 1) || !this.util.hasAccess(permissionData.privileges, 'PRIV_CAN_CREATE_COMMENT') || !isAnyActiveDocSelected || isMultiProjectSelected;
              break;
            case "CreateReview":
            case "NoReview":
              subMenuItem.hidden = this.selectedFolderPermissionData.enableCommentReview ? false : true;
              subMenuItem.disable = (lockedObjects.comment && selectedRowData.length == 1) || !this.util.hasAccess(permissionData.privileges, 'PRIV_CAN_CREATE_COMMENT') || !isAnyActiveDocSelected || isMultiProjectSelected;
              break;
            case "Associations":
              subMenuItem.disable = selectedRowData.length != 1;
              break;
            case "DeactivateFiles":
              subMenuItem.disable = isMultiProjectSelected || !this.enableCheckForActiveDeactiveDoc(selectedRowData, permissionData, false);
              break;
            case "ReactivateFiles":
              subMenuItem.disable = isMultiProjectSelected || !this.enableCheckForActiveDeactiveDoc(selectedRowData, permissionData, true);
              break;
            case "FileAttributes":
              subMenuItem.disable = ((lockedObjects.attributes) && selectedRowData.length == 1) || isArchivedProjectSelected || !isAnyActiveDocSelected || isMultiProjectSelected;
              if (!subMenuItem.disable) {
                if (!this.util.hasAccess(permissionData.privileges, 'PRIV_ASSIGN_DOCUMENT_ATTRIBUTES') && this.disabledFilesList.length === selectedRowData.length) {
                  subMenuItem.disable = true;
                }
              }

              break;
            case "BatchAssocition":
              if(!permissionData.enableFileAssociation){
                subMenuItem.hidden = true;
              } else {
                subMenuItem.disable = ((lockedObjects.attributes) && selectedRowData.length == 1) || isArchivedProjectSelected || !isAnyActiveDocSelected || isMultiProjectSelected || selectedRowData.findIndex((row) => (row.isLink && (row.linkType == 'Dynamic' || row.linkType == 'Static'))) > -1;
                if (!subMenuItem.disable) {
                  if (!this.util.hasAccess(permissionData.privileges, 'PRIV_ASSIGN_DOCUMENT_ATTRIBUTES') && this.disabledFilesList.length === selectedRowData.length) {
                    subMenuItem.disable = true;
                  }
                }
              }

              break;
            case "AttachFile":
              subMenuItem.disable = selectedRowData.length != 1 || lockedObjects.attributes || selectedRowData[0].isLink || isArchivedProjectSelected || !isAnyActiveDocSelected;
              if (!subMenuItem.disable) {
                if (!this.util.hasAccess(permissionData.privileges, 'PRIV_ASSIGN_DOCUMENT_ATTRIBUTES') && this.disabledFilesList.length === selectedRowData.length) {
                  subMenuItem.disable = true;
                }
              }
              break;
            case "EditStatus":
              if (!this.isAllPlaceholderDoc(selectedRowData) && isAnyActiveDocSelected && !isArchivedProjectSelected && (selectedRowData.length > 1 || !lockedObjects.status) && !this.hasMultipleProjects(selectedRowData)) {
                if (this.util.hasAccess(permissionData.privileges, 'PRIV_CHANGE_STATUS')) {
                  subMenuItem.disable = false;
                }
                else {
                  let hasIncompleteStatusChangeAction = false;
                  selectedRowData.some((file) => {
                    file.actions && file.actions.some((action) => {
                      if (action.actionStatus === 0 && action.actionId == DOC_ACTIONS.FOR_STATUS_CHANGE) {
                        hasIncompleteStatusChangeAction = true;
                        return true;
                      }
                    });
                    return hasIncompleteStatusChangeAction;
                  });

                  if (hasIncompleteStatusChangeAction) {
                    subMenuItem.disable = false;
                  }
                }
              }
              break;
            case "CustomizeStatus":
              subMenuItem.disable = selectedRowData.length != 1 || isArchivedProjectSelected || !this.util.hasAccess(permissionData.privileges, 'PRIV_MANAGE_PROJECT_DOCUMENT_STATUS');
              break;
            case "AssociateFiles":
              subMenuItem.disable = isArchivedProjectSelected;
              break;
            case "FilePlaceholder":
              subMenuItem.disable = (lockedObjects.revisionUpload && selectedRowData.length == 1) || !this.hasPermissionToCreateFilePlaceholder(selectedRowData, permissionData);
              break;
            case "OpenInMicrosoftOffice":
              this.fileExtName = this.util.getExt(selectedRowData[0].uploadFilename).toLowerCase();
              this.officeFileDetails = this.fileFormUtilService.getOfficeFileDetails(this.fileExtName);             
              this.enableMicrosoftOfficeBtn = !(this.fileExtName === "docx" || this.fileExtName === "xlsx" || this.fileExtName === "pptx" || this.fileExtName === "wopitest");
              subMenuItem.hidden = selectedRowData.length != 1 || this.enableMicrosoftOfficeBtn || !(this.util.hasAccess(userPrivFileRightClick, 'ENABLE_MICROSOFT_OFFICE') && permissionData.projectADriveCDEAddonPurchaseStatus);
              let  checkoutFileDisable = this.hasPermissionToCheckoutFile(selectedRowData, permissionData.folderPermissionValues) || selectedRowData[0].hasOfficeFile ? false : true;
              let checkFileSize = this.officeFileDetails?.msFileSizeInMB > this.convertKBtoMB(selectedRowData[0].fileSize.split(' ')[0]);
              subMenuItem.enabledTooltip = !checkFileSize ? this.util.lang.get("the-file-size-exceeds-the-specified-microsoft-document-size") : "";
              subMenuItem.disable = !checkFileSize || selectedRowData[0].isLink || !selectedRowData[0].isLatest || !this.checkFolderUploadPermission(permissionData, selectedRowData[0].folderId) || checkoutFileDisable || !selectedRowData[0].isActive;
              subMenuItem.img = this.officeFileDetails?.fileTypeIcon;
              subMenuItem.title = this.officeFileDetails?.btnName;
              subMenuItem.disabledTooltip = !checkFileSize ? this.util.lang.get("the-file-size-exceeds-the-specified-microsoft-document-size") : "";
              subMenuItem.warningIcon = !checkFileSize ? "fa-exclamation-triangle" : '';
              if(subMenuItem.disable && checkFileSize){
                subMenuItem.disabledTooltip = this.util.lang.get('tooltip-edit-file-in-microsoft-office-button');
                subMenuItem.hideInfoIcon = true;
              }
              break;
            case "OpenFileEdit":
              //subMenuItem.disable = false;
              // disable on batch-selection(multiple file selection), if not has permission to publish, if user checkout file
              const isNotValidFile = !this.isValidFileToWork(selectedRowData);
              subMenuItem.hidden = !(userPrivFileRightClick.split(',').includes(`${window['SYSTEMPRIVILEGES'].privileges.CAN_ACCESS_ADRIVE_DESKTOP_APP}`) || permissionData.enableAsync);
              subMenuItem.disable = selectedRowData.length != 1 || !selectedRowData[0].isActive || !this.hasPermissionToDownload(selectedRowData, permissionData) || !this.hasPermissionToPublishRevision(selectedRowData, permissionData).hasPermission || this.isAllPlaceholderDoc(selectedRowData) || !this.hasPermissionToCheckoutFile(selectedRowData, permissionData.folderPermissionValues) || isNotValidFile || !permissionData.enableAdriveDesktop || !permissionData.projectADriveCDEAddonPurchaseStatus;
              if (!permissionData.enableAdriveDesktop) {
                subMenuItem.disabledTooltip = this.util.lang.get("disabled-adrive-desktop-opted-out");
              } else if (!permissionData.projectADriveCDEAddonPurchaseStatus) {
                subMenuItem.disabledTooltip = this.util.lang.get("disabled-adrive-desktop-project-access-control");
              } else if (isNotValidFile) {
                subMenuItem.disabledTooltip = this.util.lang.get("file-path-length-more-than-259-char");
              }
              subMenuItem.hideInfoIcon = true;
              break;
            case "CopyFilesaSync":
              subMenuItem.disable = false;
              break;

            case "createModel":
              subMenuItem.hidden = isArchivedProjectSelected || hasViewerDisabledFilesSelected || hasViewerNotSupportedFile || !hasOnlyValidSuportFiles;
              subMenuItem.disable = isMultiProjectSelected || isArchivedProjectSelected || !this.canManageProjectModels(permissionData) || hasViewerDisabledFilesSelected || hasViewerNotSupportedFile || !hasOnlyValidSuportFiles || this.hasAnyFilePasswordProtected(selectedRowData);
              break;

            case "AddToModel":
              subMenuItem.disable = !this.isProjectModelExist || isMultiProjectSelected || isArchivedProjectSelected || !this.canManageProjectModels(permissionData) || hasViewerDisabledFilesSelected || hasViewerNotSupportedFile || !hasOnlyValidSuportFiles || this.hasAnyFilePasswordProtected(selectedRowData);
              break;

            case "ViewInModel":
              subMenuItem.disable =  !this.isProjectModelExist || isMultiProjectSelected || isArchivedProjectSelected || hasViewerDisabledFilesSelected || hasViewerNotSupportedFile || this.isNotAddedToModel(selectedRowData);
              break;

            case "PushTo3DRepo":
              subMenuItem.hidden = isArchivedProjectSelected || hasOnlyHoopsViewerEnabledFilesSelected;
              subMenuItem.disable = isArchivedProjectSelected || !hasOnly3DRepoViewerSupportedFile || !hasOnly3DRepoViewerEnabledFilesSelected || this.hasAnyFilePasswordProtected(selectedRowData);
              break;

            case "RemoveFromModel":
              subMenuItem.disable = !this.isProjectModelExist || isMultiProjectSelected || isArchivedProjectSelected || !this.canManageProjectModels(permissionData) || hasViewerDisabledFilesSelected || hasViewerNotSupportedFile || this.isNotAddedToModel(selectedRowData);
              break;

            case "CopyFile":
              subMenuItem.disable = !this.hasPermissionToCopyFile(selectedRowData, permissionData);
              break;
          }
        });
      });

      callback(items);
    });
  };

  /**
   * @descrription Check if all selected files are have workflow running or placeholder files
   * @param {*} data
   * @memberof DocumentsComponent
   */
  checkAllFilesAreActiveWorkflowOrPlacholder(data) {
    return data.every(d => 
      (d.workflowStatusVO && d.workflowStatusVO.workflowStatusId === 1) || 
      d.documentTypeId === DOC_TYPE_ID.PLACEHOLDER
    );
  }

  /**
   * @description Toggle favorite for project
   * @param {*} data
   * @memberof DocumentsComponent
   */
  toggleProjectFav(data) {
    let param = {
      action_id: AppConstant.UPDATE_PROJECT_FAV_FLAG,
      dcWiseProjectIds: JSON.stringify({
        [data.dcId]: this.util.getProjectId(data)
      }),
      setFavorite: data.isFavorite == true ? 0 : 1,
    };

    this.favTreeXhr = this.util.ajax({
      url: ApiConstant.PROJECTS_CONTROLLER,
      method: 'POST',
      data: param,
      responseType: 'text',
      _dcId: data.dcId,
      success: (response) => {
        this.favTreeXhr = false;
        if (response.body != "Success") {
          return;
        }
        data.isFavorite = param.setFavorite == 0 ? false : true;
      },
      error: (err) => {
        this.favTreeXhr = false;
      }
    });
  }

  favTreeXhr = false;

  /**
   * @description Toggle favorite for Folder
   * @param {*} data
   * @memberof DocumentsComponent
   */
  toggleFolderTypeFav(data) {
    this.favTreeXhr = this.util.ajax({
      url: ApiConstant.HOME_CONTROLLER,
      method: 'POST',
      data: {
        action_id: AppConstant.ADD_REMOVE_FAVOURITE_FOLDER,
        projectID: this.util.getProjectId(data),
        folderId: data.folderId,
        isFavourite: data.isFavourite,
        callFrom: 'FolderTree'
      },
      _cdnUrl: this.util.getTabServiceUrl(this.util.TabServices.FILES),
      _dcId: data.dcId,
      success: (response) => {
        this.favTreeXhr = false;
        response.body && (data.isFavourite = !data.isFavourite);
      },
      error: (err) => {
        this.favTreeXhr = false;
      }
    });
  }

  /**
   * @description Set initial filter params
   * @memberof DocumentsComponent
   */
  setInitFilterParam() {
    let currentListing = this.multipleListings?.selected?.id;
    if (currentListing == AppConstant.DISCUSSION_LISTING) {
      let tempProjectId = this.treeSelection?.projectID || this.treeSelection?.projectId;
      if((tempProjectId && tempProjectId.split('$$')[0] == '-1' && window['isReviewEnableProjectSelected'] == true) || (tempProjectId == undefined && window['isReviewEnableProjectSelected'] == true)){
        currentListing = AppConstant.REVIEW_LISTING;
      }else if(tempProjectId && tempProjectId.split('$$')[0] != '-1'){
        currentListing = this.getCommentListingType(this.treeSelection);
      }
    }

    this.filter = {
      opts: {
        listingType: AppConstant.FILE_LISTING,
        setAppliedFilter: false,
        enableShareFilter: true,
        isQuickSearchOptimized: true,
        appType: this.appType,
        allAppTypes: `${ACTIVE_APP_TYPE.FILES},${ACTIVE_APP_TYPE.TRANSMITTALS},${ACTIVE_APP_TYPE.DISCUSSIONS}`,
        allListingTypes: `${AppConstant.FILE_LISTING},${AppConstant.FILE_TRANSMITTAL_LISTING},${AppConstant.DISCUSSION_LISTING}`,
        supportQRCodeShare: true,
        selectedListingTypeId: currentListing || AppConstant.FILE_LISTING
      },
      selectedFilterId: this.isSharedFilterLink ? this.selectedFilterId : 0,
      list: undefined,
      useOptimisedSearch: true
    };
    if(this.isSharedFilterLink){
      this.selectedFilterId = 0;
      this.isSharedFilterLink = false;
    }
  }

  /**
   * @description Get left nav counts
   * @param {*} data
   * @memberof DocumentsComponent
   */
  getCounts(data) {
    if(data.hide){
      return;
    }
    data.xhr && data.xhr.unsubscribe && data.xhr.unsubscribe();
    data.xhr = false;
    data.xhr = this.util.ajax({
      url: ApiConstant.ACTIONS_CONTROLLER,
      method: 'POST',
      data: {
        action_id: data.actionId,
        entityTypeId: data.entityTypeId,
        appType: data.appType,
        dcId: window['LOCAL_DC_ID']
      },
      _cdnUrl: this.util.getTabServiceUrl(this.util.TabServices.FILES),
      success: (response) => {
        data.xhr = false;
        let displayCount = parseInt(response.body, 10) || 0;
        data.count = displayCount > 99 ? '99+' : displayCount;
        data.totalCount = displayCount;
        data.tooltip = this.util.lang.get('total') + ": " + displayCount + '\n';
        if(data.actionId === AppConstant.GET_INCOMPLETE_ACTIONS_COUNT && data.entityTypeId === ENTITY_TYPE.FILE){
          data.tooltip += this.util.lang.get('incomplete-actions');
        }else if(data.actionId === AppConstant.GET_OVERDUE_ACTIONS_COUNT && data.entityTypeId === ENTITY_TYPE.FILE){
          data.tooltip += this.util.lang.get('overdue-actions');
        }else if(data.actionId === AppConstant.GET_TODAY_DUE_ACTIONS_COUNT && data.entityTypeId === ENTITY_TYPE.FILE){
          data.tooltip += this.util.lang.get('duetoday');
        }else if(data.actionId === AppConstant.GET_UNREAD_MESSAGE_COUNT && data.entityTypeId === ENTITY_TYPE.DISCUSSION){
          data.tooltip += this.util.lang.get('unread-comments');
        }else if(data.actionId === AppConstant.GET_UNREAD_REVIEWS_COUNT && data.entityTypeId === 6){
          data.tooltip += this.util.lang.get('unread-reviews');
        }
        data.countTooltip = data.tooltip;
        data.loaded = true;
      },
      error: (err) => {
        data.xhr = false;
        data.loaded = true;
      }
    });
  }

  /**
   * @description Reset left navigation criteria
   * @param {*} data
   * @memberof DocumentsComponent
   */
  resetLeftNav(data) {
    if (this.activeListing) {
      this.activeListing.active = false;
    }

    data.isActive = true;
    this.activeListing = data;
    this.listingParam.currentPageNo = 1;
    this.listingParam.recordStartFrom = 0;
    delete this.listingParam.projectId;
    delete this.listingParam.folderId;
    this.filterParam = undefined;
    this.filter = undefined;
  }

  /**
   * @description Left navigation click callback handle
   * @param {*} data
   * @memberof DocumentsComponent
   */
  setActiveTab(data, applitDefaultFilter?) {
    this.resetLeftNav(data);
    this.onlyContainSearchApplied = false;
    this.routerExtraDataFromModelsTab = undefined;
    let externalData = (this.routerExtraData || {}).options;
    let excludenlTypeIds = [1, 2, 3];
    if (this.directAccess && excludenlTypeIds.indexOf(this.directAccess.nlTypeId) == -1) {
      externalData = this.directAccess;
    }

    setTimeout(() => {
      this.setInitFilterParam();
      if (applitDefaultFilter) {
        this.filter.opts.setAppliedFilter = true;
      }
      if (Object.keys(this.treeSelection).length && this.treeSelection.projectId !== '-1') {
        this.folderTreeComponent.selectRootNode();
        this.manageAddNewFileActionStatus({});
      }
      let data: any = {}
      if (externalData && externalData.projectId) {
        this.filter.opts.setAppliedFilter = false;

        data.projectId = externalData.projectId;
        if (externalData.folderId) {
          data.folderId = externalData.folderId;
        }
      }

      if (externalData && externalData.discipline) {
        this.filter.opts.setAppliedFilter = false;
        this.tableListingComponent.disablePagination();
        this.listingXhr = this.util.ajax({
          url: ApiConstant.DASHBOARD_CONTROLLER,
          method: 'POST',
          data: {
            action_id: AppConstant.DESCIPLINE_WISE_FILE_LISTING,
            currentPageNo: 1,
            recordStartFrom: 0,
            disciplineType: externalData.discipline
          },
          _cdnUrl: this.util.getTabServiceUrl(this.util.TabServices.FILES),
          success: (response) => {
            this.onListingResponse(response.body);
          },
          error: () => {
            this.listingXhr = false;
          }
        });
        return;
      }

      if (externalData && externalData.interval === 'LAST LOGIN') {
        this.lastLoginFilterData = externalData;
        this.filter.opts.setAppliedFilter = false;
        this.loadLastLoginFileListing(externalData);
        return;
      }

      if (externalData && (externalData.name || externalData.interval || externalData.action_id)) {
        this.filter.opts.setAppliedFilter = false;
        this.refreshList(externalData.name || externalData.interval, externalData.action_id, data, { legendIndex: externalData.legendIndex, isFrom: externalData?.isFrom });
        return;
      }

      if (externalData && externalData.action === 'SHOW_IN_FILE_ASSOCATION') {
        this.filter.opts.setAppliedFilter = false;
        this.tableListingComponent.addQueries({
          listingTypeId: AppConstant.FILE_LISTING,
          for: "SHOW_IN_FILE_ASSOCATION",
          assocDocData: externalData
        }, data);
        this.util.removeLocalStorageForAllDc("assocDocByFormId");
        this.$("a#navfiles").removeData();
      } else if (externalData && externalData.action === 'APPLIED_FILTER') {
        this.filter.selectedFilterId = externalData.filterid;
        this.filter.opts.setAppliedFilter = false;
        this.filter.opts.dashboardData = externalData;
      } else if (externalData && externalData.action === 'FILTER') {
        this.filter.opts.setAppliedFilter = false;
        this.tableListingComponent.addQueries({
          listingTypeId: this.multipleListings.selected.id,
          for: externalData.subAction,
          dashboardData: externalData
        }, data);
      } else if (externalData && externalData.action === 'NAVIGATE_TO_FOLDER') {
        this.filter.opts.setAppliedFilter = false;
        const addQueries = ()=>{
          this.tableListingComponent.addQueries({
            listingTypeId: this.multipleListings.selected.id,
            for: externalData.action
          }, data);
        }
        this.listingXhr = true;
        this.util.fetchFilterPreference((isFilterPrefRemember)=>{ 
          if(!isFilterPrefRemember){
            this.listingXhr = false;
            addQueries();
            return;
          }
          this.listingXhr = this.util.ajax({
            url: ApiConstant.SEARCH_FILTER_CONTROLLER,
            method: 'POST',
            data: {
              action_id: AppConstant.GET_USER_SAVED_FILTER_DETAILS,
              collectionType: this.filter.opts.allListingTypes,
              appType: this.filter.opts.allAppTypes
            },
            _cdnUrl: this.util.getTabServiceUrl(this.util.TabServices.FILES),
            success: (response) => {
              this.listingXhr = false;
              if (!response.body || !response.body.filterVOs || !response.body.filterVOs.length) {
                addQueries();
              } else {
                this.tableListingComponent.selectAppliedFilter(response.body.filterVOs[0], data);
              }
            }, error: () => {
              this.listingXhr = false;
              addQueries();
              return true;
            }
          });
        });
      } else if (this.prjLinkFunParamObj && this.prjLinkFunParamObj.prjLinkFunParamObj && this.prjLinkFunParamObj.prjLinkFunParamObj.defaultData) {
        this.filter.opts.setAppliedFilter = false;
        this.tableListingComponent.addQueries({}, this.prjLinkFunParamObj.rowData, this.prjLinkFunParamObj.prjLinkFunParamObj.defaultData);
      }  else if (this.routerExtraDataFromModelsTab?.model_id) {
        this.loadFileListing(this.routerExtraDataFromModelsTab);
      } else {
        this.loadFileListing(externalData);
      }
    });
  }


  loadLastLoginFileListing(routeData, data?) {
    data = data || {};
    this.listingXhr = this.util.ajax({
      url: ApiConstant.DASHBOARD_CONTROLLER,
      method: 'POST',
      data: {
        param1: routeData.interval,
        action_id: AppConstant.DOCUMENT_LISTING_FOR_SPECIFIC_DAYS,
        currentPageNo: this.listingParam.currentPageNo,
        recordStartFrom: this.listingParam.recordStartFrom,
        lastLoginTime: routeData.lastLoginTime,
        recordBatchSize: data.recordBatchSize || this.listing.lazyLoadBatchSize
      },
      _cdnUrl: this.util.getTabServiceUrl(this.util.TabServices.FILES),
      success: (response) => {
        this.onListingResponse(response.body, data.lazyLoaded);
      },
      error: () => {
        this.listingXhr = false;
      }
    });
  }

  /**
   * @description On Left Nav Click load data
   * @param {*} data
   * @memberof DocumentsComponent
   */
  processLeftNavClick(data, silent?: boolean) {
    this.resetLeftNav(data);
    //Switch to message tab
    if (data.type === 'UNREAD_MESSAGE_COUNT' || data.type == 'UNREAD_REVIEWS_COUNT') {
      this.multipleListings.selected = this.multipleListings.list[2];
    } else {
      this.multipleListings.selected = this.multipleListings.list[0];
    }

    let directAccess = this.directAccess;
    setTimeout(() => {
      this.setInitFilterParam();
      !silent && this.folderTreeComponent.selectRootNode();
      this.manageAddNewFileActionStatus({});
      setTimeout(() => {
        this.refreshList(null, null, directAccess);
      });
    })
  }

  /**
   * @description Get left nav counts server call
   * @memberof DocumentsComponent
   */
  getLeftNavCounts() {
    for (const key in this.leftNavJsonData) {
      if (key != 'all') {
        this.getCounts(this.leftNavJsonData[key]);
      }
    }
  }

  /**
   * @description Set Export params
   * @param {*} listingType
   * @memberof DocumentsComponent
   */
  setExportParams(listingType) {
    this.export.data.listingType = listingType;
    this.export.filter.collectionType = listingType;
  }

  listingXhr: any = false;

  /**
   * @description Listing change callback, get data from server
   * @param {*} [data]
   * @returns
   * @memberof DocumentsComponent
   */
  loadFileListing(data?) {
    this.export.filterApplied = false;

    if (!data || (data.filterId && data.jsonData && (!data.jsonData.filterQueryVOs || !data.jsonData.filterQueryVOs.length))) {
      this.filterParam = undefined;
      data = {};
    }

    if (data.listingTypeChanged) {
      // if vectorization status is changed at project level so refresh the folder tree and listing.
      if(window['isProjectVectorizationStatusChanged']){
        delete window['isProjectVectorizationStatusChanged'];
        setTimeout(() => { this.folderTreeComponent.reloadFolder('', ''); });
        data = {};
        if(this.filterParam){ // Reset the filter selected project and folder ids
          this.filterParam.selectedProjectIds = '-1';
          this.filterParam.selectedFolderIds = '-1';
          if(this.filterParam?.jsonData){
            this.filterParam.jsonData.selectedFolderIds = '-1';
            this.filterParam.jsonData.selectedProjectIds = '-1';
          }
        }
      }

      this._cognitiveCdeTabActive = window['isCognitiveTabActive'] = (data.listingType == AppConstant.COGNITIVE_CDE_LISTING_ACTION); // change listingType to cognitive listingtype
      if(!window['isCognitiveTabActive']){
        document.body.classList.remove('cognitive-cde-tab-active');
        this._showCognitiveCDERefs = false;
        this._showCognitiveCDERefsLoader = false;
      } else {
        this.listingXhr && this.listingXhr.unsubscribe && this.listingXhr.unsubscribe();
        document.body.classList.add('cognitive-cde-tab-active');
        return;
      }
      this.lastLoginFilterData =  undefined;
      this.setInitFilterParam();
      this.setExportParams(data.listingType);
    }

    if(window['isCognitiveTabActive']){
      this.fetchCognitiveCDEListing(data);
      return;
    }

    if (data.filterId) {
      this.filterParam = data;
    } else if (this.filterParam) {
      this.filterParam.action_id = AppConstant.SEARCH_FILTER_DATA;
    }

    this.listingParam.currentPageNo = data.currentPageNo || 1;
    this.listingParam.recordStartFrom = data.recordStartFrom || 0;
    delete data.currentPageNo;
    delete data.recordStartFrom;

    let filterTreeParam: any = {};
    let projectId = data.projectId;
    if (projectId && projectId != '-1') {
      this.listingParam.projectId = projectId;
      filterTreeParam.selectedProjectIds = projectId;
      filterTreeParam.selectedFolderIds = data.folderId || '-1';

      if (data.folderId) {
        this.listingParam.folderId = data.folderId;
      } else {
        delete this.listingParam.folderId;
      }
      this.listingParam.currentPageNo = 1;
      this.listingParam.recordStartFrom = 0;
    } else {
      if (projectId == '-1') {
        filterTreeParam.selectedProjectIds = '-1';
        filterTreeParam.selectedFolderIds = '-1';
        delete data.projectId;
        delete data.folderId;
        delete this.listingParam.projectId;
        delete this.listingParam.folderId;
      }
    }

    if (data._dcId) {
      this.listingParam._dcId = data._dcId;
    }

    this.lazyLoading = data.lazyLoaded || false;
    if(!this.lazyLoading) {
      this.tableListingComponent.disablePagination();
    }

    if (this.filterParam) {
      this.lastLoginFilterData =  undefined;
      data._dcId = this.listingParam._dcId;
      if(window['isFileContentSearchOnly'] != undefined && this.tableListingComponent?.multipleListing?.selected?.id == AppConstant.FILE_LISTING){
        this.filterParam.isMetadataSearchOnly = !window['isFileContentSearchOnly'];
        this.export.data.isMetadataSearchOnly = !window['isFileContentSearchOnly'];
        this.export.filter.isMetadataSearchOnly = !window['isFileContentSearchOnly'];
      }
      else {
        delete this.filterParam.isMetadataSearchOnly;
        delete this.export.data.isMetadataSearchOnly;
        delete this.export.filter.isMetadataSearchOnly;
      }
      if (data.sortField && this.filterParam) {
        Object.assign(this.filterParam, data);
      }

      Object.assign(this.filterParam, filterTreeParam);
      Object.assign(this.filterParam.jsonData, filterTreeParam);

      this.onlyContainSearchApplied = this.filterParam.jsonData.filterQueryVOs.length == 1 && this.filterParam.jsonData.filterQueryVOs[0].fieldName === 'summary';

      if (this.routerExtraDataFromModelsTab && this.onlyContainSearchApplied) {
        if (!data.model_id) {
          data['model_id'] = this.routerExtraDataFromModelsTab.model_id;
        }
       }else {
        this.routerExtraDataFromModelsTab = undefined;
       }

      this.applyFilter(data);
      return;
    }

    if (this.routerExtraDataFromModelsTab) {
      if (!data.model_id) {
        if( this.onlyContainSearchApplied) {
          data['model_id'] = this.routerExtraDataFromModelsTab.model_id;
          this.onlyContainSearchApplied = false;
        } else {
          this.routerExtraDataFromModelsTab = undefined;
        }
      }
     }

    if(this.lastLoginFilterData && this.lastLoginFilterData.interval === "LAST LOGIN"){
      this.loadLastLoginFileListing(this.lastLoginFilterData, data);
      return;
    }

    let params = Object.assign({
      currentPageNo: 1,
      recordStartFrom: 0,
      action_id: AppConstant.ADODDLE_LISTING_ACTION,
      controller: ApiConstant.LISTING_CONTROLLER,
      recordBatchSize: this.listing.lazyLoadBatchSize,
      listingType: this.multipleListings.selected.id,
      appType: this.appType
    }, data, this.listingParam);

    delete params.lazyLoaded;

    if (params.listingType == AppConstant.DISCUSSION_LISTING) {
      let tempProjectId = this.treeSelection.projectID || this.treeSelection.projectId;

      if((tempProjectId && tempProjectId.split('$$')[0] == '-1' && window['isReviewEnableProjectSelected'] == true) || (tempProjectId == undefined && window['isReviewEnableProjectSelected'] == true)){
        params.listingType = AppConstant.REVIEW_LISTING;
      }else if(tempProjectId && tempProjectId.split('$$')[0] != '-1'){
        params.listingType = this.getCommentListingType(this.treeSelection);
      }
      this.setExportParams(params.listingType);
    }

    this.listingXhr.unsubscribe && this.listingXhr.unsubscribe();

    let dcId = params._dcId;
    delete params._dcId;
    this.listingXhr = this.util.ajax({
      url: ApiConstant.LISTING_CONTROLLER,
      data: params,
      _cdnUrl: this.util.getTabServiceUrl(this.util.TabServices.FILES),
      _dcId: dcId,
      method: 'POST',
      success: (response) => {
        this.onListingResponse(response.body, data.lazyLoaded);
      },
      error: (err) => {
        this.listingXhr = false;
      }
    });
  }

  /**
   * @description Prepare list of table and render it based on data is lazy loaded or not
   * @param {*} data
   * @param {*} lazyLoaded
   * @returns
   * @memberof DocumentsComponent
   */
  onListingResponse(data, lazyLoaded?) {
    this.tableListingComponent.enablePagination();
    if (!data) {
      this.listingXhr = false;
      return;
    }

    let taskHeaderIndex = data.columnHeader.findIndex((a)=>a.colType == 'multiobjectwithcount' && a.fieldName =='actions#actionName#actionTime')
    if(taskHeaderIndex != -1){
      data.columnHeader[taskHeaderIndex].minWidthOfColumn = this._minMyTaskColWidth;
    }

    this.lazyLoading = false;

    if (lazyLoaded) {
      let newItems = this.setUniqueId(data);
      let newData = newItems.data || [];
      this.activeListing.list.recordStartFrom = newItems.recordStartFrom;
      let newUniqueRows = [];
      newData.forEach((newRow) => {
        let id1 = this.tableListingComponent.getUniqueId(newRow);
        let rowMatched = false;
        this.activeListing.list.data.some((row) => {
          let id2 = this.tableListingComponent.getUniqueId(row);
          if (id1 == id2) {
            console.warn('Duplicate item found: ', newRow);
            rowMatched = true;
            return true;
          }
        });
        if (!rowMatched) {
          newUniqueRows.push(newRow)
          this.activeListing.list.data.push(newRow);
        }
      });

      setTimeout(() => {
        this.tableListingComponent.addNewRows(newUniqueRows);
        this.listingXhr = false;
      });
    } else {
      this.activeListing.list = this.setUniqueId(data);
      setTimeout(() => {
        this.tableListingComponent.init();
        this.listingXhr = false;
        this._cognitiveCdeTabActive && (this._showCognitiveCDERefsLoader = true);
      });
    }
  }

  /**
   * @description Add uniqe key in listing data (distListId|recipientId|actionId)
   * @param {*} data
   * @returns
   * @memberof DocumentsComponent
   */
  setUniqueId(data) {
    if (this.multipleListings.selected.id == AppConstant.FILE_LISTING ||
      this.multipleListings.selected.id == AppConstant.DISCUSSION_LISTING || this.multipleListings.selected.id == AppConstant.COGNITIVE_CDE_LISTING_ACTION) {
      return data;
    }

    data.data && data.data.forEach((row) => {
      this.setUniqueIdRow(row);
    });
    return data;
  }

  /**
   * @description Set unique id after partial refresh call
   * @param {*} data
   * @returns
   * @memberof DocumentsComponent
   */
  setUniqueIdRow(data) {
    if (this.multipleListings.selected.id == AppConstant.FILE_LISTING ||
      this.multipleListings.selected.id == AppConstant.DISCUSSION_LISTING || 
      this.multipleListings.selected.id == AppConstant.COGNITIVE_CDE_LISTING_ACTION) {
      return;
    }

    data.distUniqueId = `${data.actions[0].distListId}|${data.actions[0].recipientId}|${data.actions[0].actionId}`;
  }

  downloadFilesDisabled = true;
  /**
   * Invokes whenever row selection is changed in table listing
   * @param {*} params
   * @memberof DocumentsComponent
   */

  private lastPrivCall: any;
  onRowSelectionChanged(selectedRows) {
    if (!selectedRows || selectedRows.length == 0 || this.multipleListings.selected.id !== AppConstant.FILE_LISTING) {
      this.downloadFilesDisabled = true;
      return;
    }

    this.lastPrivCall && this.lastPrivCall.unsubscribe();
    this.downloadFilesDisabled = true;
    this.lastPrivCall = this.util.getFolderPermissions(selectedRows, (permissionData) => {
      this.selectedFolderPermissionData = permissionData;
      this.downloadFilesDisabled = !this.hasPermissionToDownload(selectedRows, permissionData);
      this.lastPrivCall = undefined;
    });
  };

  /**
   * Invokes when flag is changed
   * @param {*} params
   * @memberof DocumentsComponent
   */
  onFlagChange(params) {
    this.updateFlag(params.data, params.flagType);
  }

  /**
   * @description Get applied filter data from server
   * @returns {*}
   * @memberof DocumentsComponent
   */
  applyFilter(data?): any {
    if (!data) {
      data = {};
    }
    this.export.filterApplied = true;

    let currentListing = this.multipleListings.selected.id;
    let tempProjectId = this.treeSelection.projectID || this.treeSelection.projectId;
    if (currentListing == AppConstant.DISCUSSION_LISTING) {
      if((tempProjectId && tempProjectId.split('$$')[0] == '-1' && window['isReviewEnableProjectSelected'] == true) || (tempProjectId == undefined && window['isReviewEnableProjectSelected'] == true)){
        currentListing = AppConstant.REVIEW_LISTING;
      }else if(tempProjectId && tempProjectId.split('$$')[0] != '-1'){
        currentListing = this.getCommentListingType(this.treeSelection);
      }
    }

    if(tempProjectId && tempProjectId.split('$$')[0] == '-1' && this.multipleListings.selected.id == AppConstant.DISCUSSION_LISTING){
      if(this.activeListing.type == "UNREAD_REVIEWS_COUNT"){
        currentListing = AppConstant.REVIEW_LISTING;
      }else if(this.activeListing.type == "UNREAD_MESSAGE_COUNT"){
        currentListing = AppConstant.DISCUSSION_LISTING;
      }
    }

    this.setExportParams(currentListing);

    this.filterParam.collectionType = currentListing;
    this.filterParam.jsonData.listingTypeId = currentListing;

    let queries = this.filterParam.jsonData.filterQueryVOs;

    queries && queries.forEach((query) => {
      query.listingTypeId = currentListing;
    });

    let params = Object.assign({
      currentPageNo: this.listingParam.currentPageNo || 1,
      recordStartFrom: this.listingParam.recordStartFrom || 0,
      recordBatchSize: data.recordBatchSize || this.listing.lazyLoadBatchSize
    }, this.filterParam);

    if (params.listingTypeIds) {
      let commentListingTypeId = AppConstant.DISCUSSION_LISTING;

      if((tempProjectId && tempProjectId.split('$$')[0] == '-1' && window['isReviewEnableProjectSelected'] == true) || (tempProjectId == undefined && window['isReviewEnableProjectSelected'] == true)){
        commentListingTypeId = AppConstant.REVIEW_LISTING;
      }else if(tempProjectId && tempProjectId.split('$$')[0] != '-1'){
        commentListingTypeId = this.getCommentListingType(this.treeSelection);
      }

      if(this.activeListing.type == "UNREAD_MESSAGE_COUNT"){
        commentListingTypeId = AppConstant.DISCUSSION_LISTING;
      }

      if(this.activeListing.type == "UNREAD_REVIEWS_COUNT" || commentListingTypeId == AppConstant.REVIEW_LISTING){
        params.listingTypeIds = params.listingTypeIds.replace(AppConstant.DISCUSSION_LISTING, AppConstant.REVIEW_LISTING);
      }

      if(currentListing == AppConstant.REVIEW_LISTING){
        params.appType = this.leftNavJsonData.unreadReviews.appType;
      }
    }

    let queryParam;
    if(params.action_id == AppConstant.SEARCH_FILTER_DATA || params.action_id == AppConstant.UNSAVED_USER_SEARCH_FILTER || params.action_id == AppConstant.SEARCH_DEFAULT_FIELDS_RESULT) {
      queryParam = this.util.computeQueryParamsForFilter(params);
    }

    params.jsonData = JSON.stringify(params.jsonData);

    this.listingXhr.unsubscribe && this.listingXhr.unsubscribe();

    let dcId = params._dcId;
    delete params._dcId;
    this.listingXhr = this.util.ajax({
      url: (queryParam) ? ApiConstant.SEARCH_FILTER_CONTROLLER + "?" + queryParam : ApiConstant.SEARCH_FILTER_CONTROLLER,
      data: params,
      _cdnUrl: this.util.getTabServiceUrl(this.util.TabServices.FILES),
      _dcId: dcId,
      method: 'POST',
      success: (response) => {
        if (!response.body) {
          return;
        }
        this.listingXhr = false;
        if (response.body.columnHeader) {
          this.onListingResponse(response.body, data.lazyLoaded);
        } else {
          this.onListingResponse(response.body.filterData, data.lazyLoaded);
          if (response.body.filterResponse.filterVOs && Array.isArray(response.body.filterResponse.filterVOs)) {
            this.filter.list = response.body.filterResponse.filterVOs;
            this.tableListingComponent.prepareFilters(this.filter.list);
          }
        }

        if(this.export.filter.collectionType == AppConstant.DISCUSSION_LISTING || this.export.filter.collectionType == AppConstant.REVIEW_LISTING){
          this.export.filter.jsonData = this.tableListingComponent.getSelectedFilter();
          let exportFilterQueries = this.export.filter.jsonData['filterQueryVOs'];
          exportFilterQueries && exportFilterQueries.forEach((query) => {
            query.listingTypeId = this.export.filter.collectionType;
          });
          this.export.filter.jsonData = JSON.stringify(this.export.filter.jsonData);
        }else{
          this.export.filter.jsonData = JSON.stringify(this.tableListingComponent.getSelectedFilter());
        }
      },
      error: (err) => {
        this.listingXhr = false;
      }
    });
  }

  /**
   * @description Open publish revision dialog
   * @param {*} selectedRows
   * @param {*} file
   * @memberof DocumentsComponent
   */
  publishRevision(selectedRows, file?) {
    // Check if alteast one row has publishin task
    let found = selectedRows.find(row => {
      if (!row.actions || !row.actions.length) {
        return false;
      }

      return row.actions.find(action => {
        return action.actionStatus == 0 && action.actionId == DOC_ACTIONS.FOR_PUBLISHING;
      });
    });

    // Open file browse for publish revision screen when multiple documents are selected
    this.activity.addActivity({
      type: 'Upload',
      options: {
        params: {
          projectId: this.util.getProjectId(selectedRows[0]),
          folderId: selectedRows[0].folderId,
          folderPath: selectedRows[0].folderPath,
          dcId: selectedRows[0].dcId,
          selectedFiles: selectedRows,
          fileToPublish: file,
          hasPublishingAction: found ? true : false
        },
        maxCharLength : true,
        excelImport: true,
        publishRevision: true,
        folderHasNoUploadPermission: !this.checkFolderUploadPermission(this.selectedFolderPermissionData, selectedRows[0].folderId),
        onComplete: (obj) => {
          this.updateSelectedRowData(true);
        }
      }
    });
  }

  checkProjectLvlFolderUploadPermission(data: any) {
    this.util.getProjectId(data) !== '-1' && this.util.getProjectPermission(this.util.getProjectId(data), data.dcId, (permissionData) => {
      if(this.folderTreeComponent.isFirstTimeSubContextMenuCalled) {
        this.contextMenuSubject.next(permissionData);
      }
      this.hasFolderUploadPerm = this.util.hasAccess(permissionData.privileges, 'PRIV_CREATE_PARENT_FOLDERS');

    })
  }
  /**
   * @description Update Listing on tree selection
   * @param {*} data
   * @memberof DocumentsComponent
   */
  updateFileListing(e) {
    let { item, silent } = e;

    if(this.lastLoginFilterData && !silent){
      this.lastLoginFilterData = undefined;
    }

    this.manageAddNewFileActionStatus(item);
    let paramObj: any = {};
    let projectId = this.util.getProjectId(item);
    paramObj.projectId = projectId;
    paramObj._dcId = item.dcId;

    window['PROJECTID'] = projectId; // TODO: remove this after all code converted
    window['FOLDERID'] = undefined;

    paramObj.isWorkspace = item.isWorkspace || 0;
    if (item.folderId) {
      paramObj.folderId = item.folderId || 0;

      window['FOLDERID'] = paramObj.folderId; // TODO: remove this after all code converted
    }

    if (paramObj.projectId && paramObj.projectId != -1) {
      this.export.filter.selectedProjectIds = paramObj.projectId;
      this.export.data.projectId = paramObj.projectId;

      if (item.dcId) {
        this.export.data.dcId = item.dcId;
        this.export.filter.dcId = item.dcId;
      }

    } else {
      this.export.filter.selectedProjectIds = -1;
      delete paramObj.isWorkspace;
      delete this.export.data.projectId;
    }

    if (item.folderId) {
      this.export.filter.selectedFolderIds = item.folderId;
      this.export.data.folderId = item.folderId;
    } else {
      this.export.filter.selectedFolderIds = -1;
      delete this.export.data.folderId;
    }

    if (silent) {
      return;
    }
    //update lazyLoadBatchSize value
    this.tableListingComponent.setLazyLoadBatchSize();
    this.loadFileListing(paramObj);
  }

  /**
   * @description Manage status of add new files quick action menu button and drag and drop files based on selected folder type and permission
   * @param {*} data - Selected item of folder tree
   * @memberof DocumentsComponent
   */
  checkFolderUploadPermission(permissionData, folderId) {
    return (this.util.hasFolderPermission(permissionData.folderPermissionValues, folderId, 'FOLDER_ADMIN_PERMISSION') ||
      this.util.hasFolderPermission(permissionData.folderPermissionValues, folderId, 'FOLDER_PUBLISH_AND_LINK_PERMISSION') ||
      this.util.hasFolderPermission(permissionData.folderPermissionValues, folderId, 'FOLDER_PUBLISH_PERMISSION'));
  };

  /**
   * @description Manage status of add new files quick action menu button and drag and drop files based on selected folder type and permission
   * @param {*} data - Selected item of folder tree
   * @memberof DocumentsComponent
   */
  hasUploadPerm: boolean = false;

  /**
   *
   * Stores value of whether the user has folder upload permission or not.
   * @type {boolean}
   * @memberof DocumentsComponent
   */
  hasFolderUploadPerm: boolean = false;

  /**
   *
   * Stores value of whether the user has create file permission or not.
   * @type {boolean}
   * @memberof DocumentsComponent
   */
  hasCreateFilePerm: boolean = false;

  manageAddNewFileActionStatus(data) {
    if (!data.folderId) {
      this.hasUploadPerm = false;
      let project = this.folderTreeComponent.getParentProject(data);
      if (project && project.isTemplate) {
        this.hasFolderUploadPerm = false;
        return;
      }
      //Parent level folder upload permission check
      this.checkProjectLvlFolderUploadPermission(data);
      return;
    }

    let project = this.folderTreeComponent.getParentProject(data);
    if (project && project.isTemplate) {
      this.hasUploadPerm = false;
      this.hasFolderUploadPerm = false;
      return;
    }

    this.util.getFolderPermission(data, 'folder', (permissionData) => {
      if(this.folderTreeComponent.isFirstTimeSubContextMenuCalled) {
        this.subContextMenuSubject.next(permissionData);
      }
      this.hasUploadPerm = this.checkFolderUploadPermission(permissionData, data.folderId);
      //Subfolder permission check added
      this.hasFolderUploadPerm = !(!this.util.hasFolderPermission(permissionData.folderPermissionValues, data.folderId, 'FOLDER_ADMIN_PERMISSION') || !((data.clonedFolderId == undefined || data.clonedFolderId == 0 || data.clonedFolderId == false) || (data.isFolderStructureInherited == undefined || data.isFolderStructureInherited == false || data.isFolderStructureInherited == 0)));
      this.hasCreateFilePerm = this.util.hasAccess(permissionData.privileges,'CAN_CREATE_FILES_FROM_DOCUMENT_TEMPLATES');
    });
  };


  /**
   * @description More option click callback for folder
   * @param {*} type
   * @memberof DocumentsComponent
   */
  processFolderOption(type, data,isFromDropDown?:boolean) {
    if(isFromDropDown){
      HtmlOperationService.setActiveItemClass(this.uploadDropDownBtn?.nativeElement)
    }
    switch (type) {
      case 'add-new-folder':
        window['addFolder']({
          projectId: this.util.getProjectId(data),
          folderId: data.folderId || 0,
          isWorkspace: data.folderId ? 0 : 1,
          hassubfold: data.hasSubFolder,
          dcId: data.dcId,
          isPFLocationTree: data.isPFLocationTree,
          isTemplate: data.isTemplate,
          onSuccess: () => {
            this.folderTreeComponent.refreshFolder(data);
          }
        });
        break;
      case 'move-folder':
        window['moveFolder'].updateMoveFolderData({
          folderName: data.folder_title,
          projectId: this.util.getProjectId(data),
          folderId: data.folderId,
          dcId: data.dcId,
          projectName: data.folderPath.split('\\')[0],
          watching: data.isWatching,
          onSuccess: () => {
            this.folderTreeComponent.reloadFolder(this.util.getProjectId(data), data.folderId);
          }
        });
        window['moveFolder'].init();
        break;
      case 'copy-folder-structure':
        window['CopyfolderStructure'].init({
          folder_title: data.folder_title,
          folderId: data.folderId,
          projectId: this.util.getProjectId(data),
          projectName: data.project && data.project.projectName,
          dcId: data.dcId,
          hasSubFolder: data.hasSubFolder,
          onSuccess: () => {
            this.folderTreeComponent.reloadFolder(this.util.getProjectId(data), data.folderId);
          }
        });
        break;
      case 'add-placeholder':
        this.placeholderService.start({
          params: {
            projectId: this.util.getProjectId(data),
            folderId: data.folderId,
            dcId: data.dcId,
            documentFolderPath: data.folderPath
          },
          onComplete: () => {
            this.updateSelectedRowData(true);
          }
        });
        break;
      case 'add-files':
        this.activity.addActivity({
          type: 'Upload',
          options: {
            params: {
              projectId: this.util.getProjectId(data),
              folderId: data.folderId,
              dcId: data.dcId
            },
            maxCharLength : true,
            excelImport: true,
            onComplete: (obj) => {
              if (!this.activity.isOpen()) {
                return;
              }

              if (obj && obj.options && obj.options.params && this.treeSelection.folderId && obj.options.params.folderId != this.treeSelection.folderId) {
                return;
              }

              this.multipleListings.selected = this.multipleListings.list[0];
              this.updateSelectedRowData(true);
            }
          }
        });
        break;
      case 'create-file':
        let dialogData = {
          dialogRef: undefined,
          projectId: this.treeSelection.projectId,
          folderId: this.treeSelection.folderId
        };
        const dialogRef: MatDialogRef<any> = this.dialog.open(DocTemplateSelectorComponent, {
          panelClass: 'document-template',
          data: dialogData
        });

        dialogData.dialogRef = dialogRef;
        break;
      default:
        console.error('Invalid folder action type is passed. Action type is: ', type);
        break;
    }
  }

  isValid(folderName: string) {
    let validateObj = this.validate.folderNameValidation(folderName);
    return validateObj["valid"];
  }

  checkValidFolderName(folderName: string) {
    // Check valid folder name
    let newName = this.validate.trimAndRemoveExtraSpace(folderName);
    return this.isValid(newName)
  }

  folderArrayValidation(folderArray: Array<string>) {
    for (var k = 0; k < folderArray.length; k++) {
      if (!this.checkValidFolderName(folderArray[k])) {
        //Show error message.
        this.util.notification.error({
          theClass: 'notification-sm',
          msg: this.util.lang.get('folder-name-includes-invalid-character') + '<br><br>' + this.util.myJoin(folderArray, "/", null, k)
        });
        return false
      }
    }
    return true
  }

  filesPicked(files: Array<any>) {
    var isFolderNameError = false;
    var filesArray = files
    var tempObj = {};
    var rootFolderObj = {};
    var allFiles = [];
    var rootFolderArray = []
    for (var i = 0; i < filesArray.length; i++) {
      allFiles.push(filesArray[i]);
      // Last element is /
      //Adding Root Folder to Object
      let splitFolderArray = filesArray[i].webkitRelativePath.split(filesArray[i].name)[0].split("/")
      splitFolderArray.length && splitFolderArray.splice(splitFolderArray.length - 1, 1)


      if (splitFolderArray.length) {
        if (!this.folderArrayValidation(splitFolderArray)) {
          isFolderNameError = true;
          this.folderUpload.nativeElement.value = ''
          break;
        }

        for (var j = splitFolderArray.length - 1; j >= 0; j--) {
          //Adding Root Folder to Object
          if (j == 0) {
            // Root Folder files.
            if (!tempObj[splitFolderArray[0]]) {
              tempObj[splitFolderArray[0]] = { "name": splitFolderArray[0], "parent": this.treeSelection.folder_title || "", "level": 1, "parentLevel": 0, relativeFolderPath: splitFolderArray[0] }
              rootFolderArray.push(splitFolderArray[0])
            }
            // Folder exists. Add files.
            filesArray[i].size && splitFolderArray.length === 1 && (tempObj[splitFolderArray[0]].files ? tempObj[splitFolderArray[0]].files.push(filesArray[i]) : tempObj[splitFolderArray[0]].files = [filesArray[i]]);

            continue
          } else if (j === splitFolderArray.length - 1) {
            if (!tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)]) {
              tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)] = { "name": splitFolderArray[j], "parent": splitFolderArray[j - 1], "level": j + 1, "parentLevel": j, relativeFolderPath: this.util.myJoin(splitFolderArray, "/", null, j) }
            }
            // Folder exists. Add files.
            filesArray[i].size && (tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)].files ? tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)].files.push(filesArray[i]) : (tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)].files = [filesArray[i]]));
            continue
          } else {
            if (!tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)]) {
              tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)] = { "name": splitFolderArray[j], "parent": splitFolderArray[j - 1], "level": j + 1, "parentLevel": j, relativeFolderPath: this.util.myJoin(splitFolderArray, "/", null, j) }
            }
          }
        }
      }
    }

    if (isFolderNameError) {
      return
    }

    if (rootFolderArray.length) {
      this.folderUpload.nativeElement.value = '';
      this.checkForDuplicateFolderNameExist(rootFolderArray, tempObj, rootFolderObj, allFiles);
    }

  }

  private getDisabledFileList(selectedRows) {
    let blockedFilesList = [];
    window['permissionValues'] = window['permissionValues'] || {};
    selectedRows.forEach((rowData) => {
      if (window['permissionValues'][rowData.folderId] !== AppConstant.FOLDER_ADMIN_PERMISSION && rowData.publisherId !== window['USP'].userID) {
        blockedFilesList.push(rowData.uploadFilename);
      }
    });

    return blockedFilesList;
  }

  /**
   * @description When user confirms that he wants to deactivate model file
   * @memberof DocumentsComponent
   */
  continueToDeactivateModels() {
    window['modelDeactivateConfirmHandler'] && window['modelDeactivateConfirmHandler']();
  }

  /**
   * @description When user declines deactivate model file
   * @memberof DocumentsComponent
   */
  declineToDeactivateModels() {
    window['modelDeactivateDeclineHandler'] && window['modelDeactivateDeclineHandler']();
  }

  actionsXhr = false;
  /**
   * @description More option click callback for files
   * @param {*} type
   * @memberof DocumentsComponent
   */
  processOption(type) {
    //TODO: Remove below code and implement in Latest Angular version
    let selectedRows = this.tableListingComponent.getSelectionDataCurrentPage();
    switch (type) {
      case 'open-file-edit':
        // checkout functionality, added separate function in common.js
        window['checkOutOnly'](this.util.getProjectId(selectedRows[0]), selectedRows, (resp) => {
          this.fileFormUtilService.callASyncNofitication(selectedRows[0]);
        });
        break;
      case 'clear-actions':
        this.actionsXhr = true;
        this.actions.clearAction(selectedRows, () => {
          this.updateSelectedRowData(true, selectedRows);
          this.actionsXhr = false;
        });
        break;
      case 'delegate-actions':
        this.util.getProjectPermission(this.util.getProjectId(selectedRows[0]), selectedRows[0].dcId, (data) => {
          window['delegateFileTransActions'](selectedRows, data.privileges);
        });
        break;
      case 'deactivate-actions':
        this.actionsXhr = true;
        this.actions.deactiveAction(selectedRows, () => {
          this.updateSelectedRowData(undefined, selectedRows);
          this.actionsXhr = false;
        });
        break;
      case 'reactivate-actions':
        this.actionsXhr = true;
        this.actions.reactiveAction(selectedRows, () => {
          this.updateSelectedRowData(undefined, selectedRows);
          this.actionsXhr = false;
        });
        break;

      case 'start-discussion':
        let commentSelectedRowData = this.checkValidCommentFile(selectedRows);
        if (!commentSelectedRowData.length) {
          return;
        }
        window['showAction']({
          data: {
            entityDetail: commentSelectedRowData,
            commentType: COMMENT_TYPE.NORMAL,
            actionId: DOC_ACTIONS.FOR_COMMENT
          },
          onComplete: (data) => {
            this.updateSelectedRowData(undefined, selectedRows);
          }
        });
        break;
      case 'no-discussion':
        let noCommentSelectedRowData = this.checkValidCommentFile(selectedRows);
        if (!noCommentSelectedRowData.length) {
          return;
        }
        window['showAction']({
          data: {
            entityDetail: noCommentSelectedRowData,
            commentType: COMMENT_TYPE.NOCOMMENT,
            actionId: DOC_ACTIONS.FOR_COMMENT
          },
          onComplete: (data) => {
            this.updateSelectedRowData(undefined, selectedRows);
          }
        });
        break;
      case 'create-review':
        let reviewSelectedRowData = this.checkValidCommentFile(selectedRows);
        if (!reviewSelectedRowData.length) {
          return;
        }
        window['showAction']({
          data: {
            entityDetail: reviewSelectedRowData,
            commentType: COMMENT_TYPE.NORMAL,
            actionId: DOC_ACTIONS.FOR_COMMENT,
            enableCommentReview: true,
            permissionData: this.selectedFolderPermissionData
          },
          onComplete: (data) => {
            this.updateSelectedRowData(undefined, selectedRows);
          }
        });
        break;
      case 'no-review':
        let noReviewSelectedRowData = this.checkValidCommentFile(selectedRows);
        if (!noReviewSelectedRowData.length) {
          return;
        }
        window['showAction']({
          data: {
            entityDetail: noReviewSelectedRowData,
            commentType: COMMENT_TYPE.NOCOMMENT,
            actionId: DOC_ACTIONS.FOR_COMMENT,
            enableCommentReview: true,
            permissionData: this.selectedFolderPermissionData
          },
          onComplete: (data) => {
            this.updateSelectedRowData(undefined, selectedRows);
          }
        });
        break;
      case 'assoc-files':
        this.listing.assocFilesFromFileTab(selectedRows);
        break;

      case 'assoc-discusion':
        (this.getCommentListingType(this.treeSelection) == AppConstant.REVIEW_LISTING) ? this.listing.assocReviewsFromDiscussionTab(selectedRows) : this.listing.assocDiscussionsFromDiscussionTab(selectedRows);
        break;
      case 'create-placeholder':
        let folderIds = [];
        let revisionIds = [];
        selectedRows.forEach((row) => {
          if (folderIds.indexOf(row.folderId) == -1) {
            folderIds.push(row.folderId);
          }
          revisionIds.push(row.revisionId);
        });

        this.placeholderService.start({
          params: {
            projectId: this.util.getProjectId(selectedRows[0]),
            dcId: selectedRows[0].dcId,
            folderId: folderIds.join(','),
            revisionIds: revisionIds.join(','),
            documentFolderPath: selectedRows[0].documentFolderPath.slice(0, selectedRows[0].documentFolderPath.lastIndexOf('\\')) 
          },
          onComplete: () => {
            this.updateSelectedRowData(true);
          }
        });

        break;
      case 'publish-revision':

        if(selectedRows.length == 1 && selectedRows[0].hasOfficeFile) {
          this.fileFormUtilService.publishRevisionMSFile(selectedRows[0],this.updateSelectedRowData.bind(this));
          return;
        }

        // Open native file explorer when only one document is selected
        if (selectedRows.length == 1) {
          this.publishRevisionFileInput.nativeElement.click();
          return;
        }

        this.publishRevision(selectedRows);
        break;
      case 'edit-file-attributes':
        let paramsObj = {
          userId: window['USP'].userID,
          folderIds: '',
          revisionId: '',
          revisionIds: '',
          projectId: undefined,
          dcId: '',
          selectedRevIds: []
        };
        let selected_dcId, selectedRevIds = [];
        selectedRows.forEach((allData) => {
          selectedRevIds.push(allData.revisionId);
          //NOODLE-29139-Edit attributes disabled when deactivated documents
          if (allData.isActive && (!allData.lockActivityIds || allData.lockActivityIds.indexOf(AppConstant.ACTIVITY_KEY_EDIT_ATTRIBUTES) == -1) && (this.disabledFilesList.indexOf(allData.uploadFilename) == -1)) {
            paramsObj.revisionId += allData.revisionId + ",";
            if (paramsObj.folderIds.indexOf(allData.folderId + ",") == -1)
              paramsObj.folderIds += allData.folderId + ",";
            if (paramsObj.projectId == undefined) {
              paramsObj.projectId = allData.projectId;
              selected_dcId = allData.dcId;
            }
          }

        });

        if (paramsObj.revisionId == '') {
          this.util.notification.warn({
            theClass: 'notification-sm',
            msg: this.util.lang.get('activity-locked-for-the-selected-files-note')
          });
          return;
        }

        paramsObj.dcId = selected_dcId;
        paramsObj.revisionIds = paramsObj.revisionId;
        paramsObj.selectedRevIds = selectedRevIds;

        window['showEditAttributes']({
          params: paramsObj,
          onComplete: () => {
            this.updateSelectedRowData(undefined, selectedRows);
          }
        });
        break;
      case 'batch-assocition':
        window['openBatchAssociation'](selectedRows, (selectData)=>{ this.updateSelectedRowData(undefined, selectData); });
        break;
      case 'attach-file':
        this.activity.addActivity({
          type: 'Upload',
          options: {
            params: {
              projectId: this.util.getProjectId(selectedRows[0]),
              folderId: selectedRows[0].folderId,
              documentId: selectedRows[0].documentId,
              dcId: selectedRows[0].dcId,
              revisionId: selectedRows[0].revisionId
            },
            maxCharLength : true,
            singleUpload: true,
            attachment: true,
            title: this.util.lang.get('attachment'),
            onComplete: (obj) => {
              if (!this.activity.isOpen()) {
                return;
              }

              if (obj && obj.options && obj.options.params && this.treeSelection.folderId && obj.options.params.folderId != this.treeSelection.folderId) {
                return;
              }

              this.multipleListings.selected = this.multipleListings.list[0];
              this.updateSelectedRowData(undefined, selectedRows);
            }
          }
        });
        break;
      case 'status-change':
        if (selectedRows.length > 1) {
          window['fileBatchStatus'].openBatchStatusChange(selectedRows, () => {
            this.updateSelectedRowData(undefined, selectedRows);
          });
          return;
        }

        window['showAction']({
          data: {
            entityDetail: selectedRows,
            actionId: DOC_ACTIONS.FOR_STATUS_CHANGE
          },
          onComplete: () => {
            this.updateSelectedRowData(undefined, selectedRows);
          }
        });
        break;

      case 'custom-status':
        this.util.ajax({
          url: ApiConstant.HOME_CONTROLLER,
          _cdnUrl: this.util.getTabServiceUrl(this.util.TabServices.FILES),
          _dcId: selectedRows[0].dcId,
          method: 'POST',
          data: {
            projectId: this.util.getProjectId(selectedRows[0]),
            folderId: 0,
            isWorkspace: 1,
            action_id: AppConstant.ADODDLE_WORKSPACE_TREE
          },
          success: (response) => {
            if (!response.body) {
              return;
            }
            let status = (selectedRows[0].status == '---') ? "" : selectedRows[0].status;
            window['projects'].manageStatusesDocRightClick(response.body[1], status, () => {
              this.updateFileListing({ item: this.treeSelection });
            });
          }
        });
        break;

      case 'view-file':
        if (selectedRows[0].documentTypeId == DOC_TYPE_ID.FEDERATED_MODEL || selectedRows[0].documentTypeId == DOC_TYPE_ID.SINGLE_MODEL) {
          this.listing.openModelDocument(selectedRows[0]);
          return;
        }
        this.listing.openDocumentTab(selectedRows[0]);
        break;

      case 'open-in-microsoft-office':
        this.fileFormUtilService.openInMSOffice(selectedRows[0],this.updateSelectedRowData.bind(this));
        break;

      case 'download-files':
        if (!this.listingXhr && !this.downloadFilesDisabled && !this.lastPrivCall) {
          let downloadUtil1 = new window['downloadUtil']();
          downloadUtil1.downloadFileOnFileListing(false, selectedRows, false, () => {
            this.updateSelectedRowData(undefined, selectedRows);
          });
        }
        break;

      case 'checkout':
        //Multiple file selected then show Download modal in checkout option.
        //param1 - downloadAllFiles is true or false. param2 - pass selectedRows data. param3 - checked and editable lock for editing.
        if (selectedRows.length > 1) {
          let downloadUtil1 = new window['downloadUtil']();
          downloadUtil1.downloadFileOnFileListing(false, selectedRows, true, () => {
            this.updateSelectedRowData(undefined, selectedRows);
          });
          return;
        }
        window['checkOutFilesFromFileRightClick'](this.util.getProjectId(selectedRows[0]), selectedRows);
        break;
      case 'undo-checkout':
        if(selectedRows[0].hasOfficeFile) {
          this.fileFormUtilService.undoCheckoutMSOffice(selectedRows[0]);
        } else {
          window['undoCheckOutFilesRightClick'](selectedRows[0], selectedRows);
        }
        break;

      case 'compare-files':
        window['compareFiles'](selectedRows);
        break;
      case 'compare-text':
        window['compareTexts'](selectedRows);
        break;

      case 'distribute-files':
        window['showAction']({
          data: {
            entityDetail: selectedRows,
            actionId: DOC_ACTIONS.FOR_DISTRIBUTION
          },
          onComplete: (data) => {
            this.updateSelectedRowData(undefined, selectedRows);
          }
        });
        break;
      case 'copy-direct-link':
        let docPath = selectedRows[0].documentFolderPath || "";
        docPath = docPath.substring(0, docPath.lastIndexOf('\\'));
        let fileDetail = {
          projectId: this.util.getProjectId(selectedRows[0]),
          selectedRowData: selectedRows[0],
          docPath: docPath,
        }
        this.dialog.open(ShareLinkUploadComponent, {
          data: fileDetail
        });
        break;

      case 'visibility-files':
        window['showAction']({
          data: {
            entityDetail: selectedRows,
            actionId: DOC_ACTIONS.FOR_VISIBILITY,
            resourceTypeId: 2
          },
          onComplete: (data) => {
            this.updateSelectedRowData(undefined, selectedRows);
          }
        });
        break;
      case 'start-workflow':
        window['openStartWorkFlowPopup'](selectedRows, () => {
          this.updateSelectedRowData(undefined, selectedRows);
        })
        break;
      case 'associations':
        this.listing.openDocument(selectedRows[0], "ASSOC_ATTACH");
        break;
      case 'deactivate-files':
        this.util.getProjectPermission(this.util.getProjectId(selectedRows[0]), selectedRows[0].dcId, (permissionData) => {
          window['openDeactivateReactivateDialog']("deactivate", selectedRows, permissionData.privileges, () => {
            this.updateSelectedRowData(true);
          });
        });
        break;
      case 'reactivate-files':
        this.util.getProjectPermission(this.util.getProjectId(selectedRows[0]), selectedRows[0].dcId, (permissionData) => {
          window['openDeactivateReactivateDialog']("reactivate", selectedRows, permissionData.privileges, () => {
            this.updateSelectedRowData(true);
          });
        });
        break;
      case 'delete-files':
        this.fileFormUtilService.openFileDeleteConfirmation(selectedRows,this.deleteFileCallback);
        break;
      case 'all-history':
        selectedRows[0].extra = { historyType: HISTORY_TYPE.ALL };
        this.listing.openDocument(selectedRows[0], "History");
        break;
      case 'distribution-history':
        selectedRows[0].extra = { historyType: HISTORY_TYPE.DISTRIBUTION };
        this.listing.openDocument(selectedRows[0], "History");
        break;
      case 'revisions-history':
        selectedRows[0].extra = { historyType: HISTORY_TYPE.REVISIONS };
        this.listing.openDocument(selectedRows[0], "History");
        break;
      case 'status-history':
        selectedRows[0].extra = { historyType: HISTORY_TYPE.STATUS };
        this.listing.openDocument(selectedRows[0], "History");
        break;
      case 'signatories-history':
        selectedRows[0].extra = { historyType: HISTORY_TYPE.SIGNATORIES };
        this.listing.openDocument(selectedRows[0], "History");
        break;
      case 'copy-name':
        this._clipboardService.copyFromContent(selectedRows[0].uploadFilename);
        this.util.notification.info({
          theClass: 'notification-sm',
          msg: this.util.lang.get('copied')
        });
        break;
      case 'copy-msg-title':
        let title = (this.getCommentListingType(this.treeSelection) == AppConstant.REVIEW_LISTING) ? selectedRows[0].reviewTitle : selectedRows[0].oriCommentTitle;
        this._clipboardService.copyFromContent(title);
        this.util.notification.info({
          theClass: 'notification-sm',
          msg: this.util.lang.get('copied')
        });
        break;
      case 'copy-location':
        this._clipboardService.copyFromContent(selectedRows[0].documentFolderPath);
        this.util.notification.info({
          theClass: 'notification-sm',
          msg: this.util.lang.get('copied')
        });
        break;
      case 'copy-files':
        window['startCopyFiles']({
          selectedRows: selectedRows,
          permissions: this.selectedFolderPermissionData,
          onComplete: (obj) => {
            if (!this.activity.isOpen()) {
              return;
            }
            this.multipleListings.selected = this.multipleListings.list[0];
            this.updateSelectedRowData(true);
          }
        });
        break;
      case 'start-watching':
        window['subscribeWatch']({
          watchingFrom: 'file',
          action: 'start',
          paramObj: selectedRows,
          isBatch: selectedRows.length > 1
        });
        break;
      case 'stop-watching':
        window['subscribeWatch']({
          watchingFrom: 'file',
          watchedByResourceTypeId: selectedRows[0].watchDetails.watchedByResourceTypeId,
          watchedId: selectedRows[0].watchDetails.watchedId,
          action: 'stop',
          paramObj: selectedRows,
          isBatch: selectedRows.length > 1
        });
        break;
      case 'edit-watch-settings':
        window['subscribeWatch']({
          watchingFrom: 'file',
          action: 'getDetails',
          watchedByResourceTypeId: selectedRows[0].watchDetails.watchedByResourceTypeId,
          watchedId: selectedRows[0].watchDetails.watchedId,
          paramObj: selectedRows,
          isBatch: selectedRows.length > 1
        });
        break;

      case 'link-file':
        window['linkDocument'].init(selectedRows);
        break;
      case 'move-files':
        this.$('#selectMoveFileTargetFolderModal').data("projectName", selectedRows[0].projectName);
        window['moveFiles'].init(selectedRows, () => {
          this.updateSelectedRowData(true);
        });
        break;
      case 'activity-locks':
        new window['ActivityLock'](selectedRows, () => {
          this.updateSelectedRowData(undefined, selectedRows);
        });
        break;

      case 'task-info':
        window['FilesBatchOperations'].forInformation(selectedRows, this.activeListing.list, () => {
          this.updateSelectedRowData(undefined, selectedRows);
        });
        break;
      case 'task-ack':
        window['showAction']({
          data: {
            type: 'DOC',
            listData: this.util.copy(this.activeListing.list),
            entityDetail: selectedRows,
            actionId: DOC_ACTIONS.FOR_ACKNOWLEDGEMENT
          },
          onComplete: (data) => {
            this.updateSelectedRowData(undefined, selectedRows);
          }
        });
        break;
      case 'task-comment-coordination':
        window['showAction']({
          data: {
            entityDetail: selectedRows,
            actionId: DOC_ACTIONS.FOR_COMMENT_COORDINATION
          },
          onComplete: (data) => {
            this.updateSelectedRowData(undefined, selectedRows);
          }
        });
        break;
      case 'task-comment-incorporation':
        window['showAction']({
          data: {
            entityDetail: selectedRows,
            listData: this.util.copy(this.activeListing.list),
            actionId: DOC_ACTIONS.FOR_COMMENT_INCORP
          },
          onComplete: (data) => {
            this.updateSelectedRowData(true);
          }
        });
        break;
      case 'task-action':
        window['showAction']({
          data: {
            type: 'DOC',
            entityDetail: selectedRows,
            listData: this.util.copy(this.activeListing.list),
            actionId: DOC_ACTIONS.FOR_ACTION
          },
          onComplete: (data) => {
            this.updateSelectedRowData(undefined, selectedRows);
          }
        });
        break;

      case 'save-as-pdf':
        let downloadUtilObj = new window['downloadUtil']();
        if (selectedRows.length > 1) {
          downloadUtilObj.downloadFileOnFileListing(false, selectedRows, false, () => {
            this.updateSelectedRowData(undefined, selectedRows);
          });
          return;
        }

        let singleFile = selectedRows[0];
        let extraJson = {
          "isIncludeMarkup": true,
          "isParentDoc": false,
          "isXREF": false,
          "isRenameWithDocRef": false,
          "isAppenedRevNo": false,
          "isAppendDocTitle": false,
          "isAppenedIssNo": false,
          "fromSaveAsPDF": true,
          "revIds": [singleFile.revisionId.split('$$')[0]]
        };
        /** also saveAsPDF from UWV project. */
        if (singleFile.projectViewerId && singleFile.projectViewerId == 7) {
          extraJson["projectViewerId"] = singleFile.projectViewerId;
          extraJson["comingFrom"] = 'saveAsPDF';
        }

        downloadUtilObj.setProjectRevisionID(singleFile);
        this.util.downloadBatch(ApiConstant.DOWNLOAD_BATCH_DOCUMENT_CONTROLLER, {
          projectID: singleFile.projectId,
          saveAsPDF: true,
          extra: JSON.stringify(extraJson).replace(/"/g, "\'"), 
          userID: window['USP'].userID
        }, singleFile.dcId, "POST", singleFile.projectId, {
          'revisions':[{
            revisionId:singleFile.revisionId,
            dcId:singleFile.dcId,
            projectId:singleFile.projectId,
            isLock:singleFile.isLock
          }]
        }, () => {
          this.updateSelectedRowData(undefined, selectedRows);
        });
        break;

      case 'print-file':
        window['printFileIncludingAll'].start(selectedRows, this.tableListingComponent.getListingData(), () => {
          this.updateSelectedRowData(undefined, selectedRows);
        });
        break;

      case 'reply-discussion':
          this.listing.openDiscussions(selectedRows[0], 'reply');
        break;
      case 'open-discussion':
        let parentProject = this.folderTreeComponent.getParentProject(selectedRows[0]);
        if (parentProject && parentProject.enableCommentReview) {
          this.listing.openReview(selectedRows[0]);
        } else {
          this.listing.openDiscussions(selectedRows[0]);
        }
        break;
      case 'sign-with-docusign':
      case 'view-with-docusign':
        const isViewMode = type === 'view-with-docusign';
        this.initiateSigningProcess(selectedRows[0], isViewMode);
        break;
      case 'navigate-to-folder':
        this.multipleListings.selected = this.multipleListings.list[0];
        this.routerExtraData = {
          options: {
            action: 'NAVIGATE_TO_FOLDER',
            projectId: this.util.getProjectId(selectedRows[0]),
            folderId: selectedRows[0].folderId,
          }
        }
        this.folderTreeComponent.reloadFolder(this.routerExtraData.options.projectId, this.routerExtraData.options.folderId);
        this.setActiveTab(this.leftNavJsonData.all);
        this.routerExtraData = undefined;
        break;
      case 'mark-discussion-as-read':
        window['discussionBatchOperations'].start(selectedRows, this.activeListing.list);
        this.$("#myModal-batchforRead").modal('show');
        break;
      case 'Add-model-options':
      case 'view-model-options':
      case 'Remove-model-options':
        let actionType = null;
        if (type) {
          const splitStr = type.split('-');
          actionType = splitStr[0];
        }
        window['showAction']({
          data: {
            entityDetail: selectedRows,
            actionId: DOC_ACTIONS.FOR_MODEL_OPTIONS,
            actionType: actionType
          },
          onComplete: (data) => {
            this.updateSelectedRowData(true, selectedRows);
          }
        });
        break;
      
      case 'push-to-3d-repo':
        
        const revisions = this.get3DRepoRevisionsDetail(selectedRows);
        if(revisions.length) {
          const pushTo3DRActivities = this.activity.getActivitiesByType('PushTo3DR');
          if(pushTo3DRActivities.length) {
            (window as any).pushNew3DRActivities(revisions);
          } else {
            this.activity.addActivity({
              type: 'PushTo3DR',
              options: {
                isFromModel: false,
                revisions: revisions
              }
            });
          }

          if(!this.activity.isOpen()) {
            this.activity.open();
          }
        }
        break;

      case 'create-model':
        if (selectedRows.length > 1) {
          if (selectedRows.every((val, i, arr) => val.projectId === arr[0].projectId)) {
            window['setSelectedModelFiles']([...selectedRows]);
            window['startCreateModel'](selectedRows, (rowData) => {
              this.addRemoveModel(window['getSelectedModelFiles'](), true, rowData);
            }, () => {
              this.util.notification.error({
                theClass: 'notification-sm',
                msg: this.util.lang.get('error-while-processing-your-request')
              });
            });
          } else {
            const dialogRef: MatDialogRef<any> = this.dialog.open(WarnNotificationComponent, {
              backdropClass: 'backgroundclass',
              panelClass: ['adoddle-notification-wrapper'],
              data: {
                title: this.util.lang.get('merge-conflicts'),
                msg: this.util.lang.get('merge-conflicts-different-project'),
                btnLabel: this.util.lang.get('ok'),
                type:"warn",
                autoHideTime: "5000"
              }
            });
          }
        } else {
          window['setSelectedModelFiles']([...selectedRows]);
          window['startCreateModel'](selectedRows, (rowData) => {
            this.addRemoveModel(window['getSelectedModelFiles'](), true, rowData);
          }, () => {
            this.util.notification.error({
              theClass: 'notification-sm',
              msg: this.util.lang.get('error-while-processing-your-request')
            });
          });
        }
        break;
      default:
        console.error('Unknown action type is passed. Action type is : ' + type);
    }
  }


  /**
   * Determines whether sync error a
   * @param res
   */
  aSyncError(res) {
    res.error = [
      {
        type: 1,
        title: 'User not logged in',
        msg: 'Please login in aDrive app and try again!'
      },
      {
        type: 2,
        title: 'Different user account',
        msg: 'You are login with different user in aDrive and adoddle. Please login with same user in aDrive app.'
      }
    ]

    if (res.error && res.error[1].type == 2) {
      this.util.notification.error({
        theClass: 'notification-sm',
        title: res.error[1].title,
        msg: res.error[1].msg
      });
    }
  }

  // checkOut only when click on Edit File (related to aSync)
  /**
   * Checks out only
   * @param data
   */
  checkOutOnly(data) {
    let replacedVal = ApiConstant.ASYNC_CHECKOUT_ONLY.replace('{projectId}', data.projectId);
    replacedVal = replacedVal.replace('{revisionId}', data.revisionId)
    this.util.ajax({
      url: replacedVal,
      method: 'POST',
      _dcId: data.dcId,
      _cdnUrl: 'https://dmsqa2ak.asite.com',
      contentType: 'application/x-www-form-urlencoded',
      acceptEncoding: 'gzip',
      success: (response) => {
        console.log("this is response of checkout only:- ", response)
        if (!response.body) {
          return;
        }
      },
      error: (err) => {
        console.log("this is error of checkout only:-", err)
      }
    });
  }

  /**
   * Check validate select file for comment action on file
   * @private
   * @param {*} selectedRows
   * @param {*} action
   * @returns Array
   * @memberof DocumentsComponent
   */
  private checkValidCommentFile(selectedRows) {
    let commentSelectedRowData = [];
    let invalidFound = false;
    for (let i = 0; i < selectedRows.length; i++) {
      let row = selectedRows[i];
      let isLockForComment = row.lockActivityIds && row.lockActivityIds.indexOf(AppConstant.ACTIVITY_KEY_ADD_COMMENT) > -1;
      if (row.isActive && (this.checkPendingAction(row, DOC_ACTIONS.FOR_COMMENT) || !isLockForComment)) {
        commentSelectedRowData.push(row);
      } else if (isLockForComment) {
        invalidFound = true;
      }
    }
    if (!commentSelectedRowData.length && invalidFound) {
      this.util.notification.warn({
        theClass: 'notification-sm',
        msg: this.util.lang.get('activity-locked-note-comment')
      });
      return commentSelectedRowData;
    }
    return commentSelectedRowData;
  }

  /**
   * Check for pending for comment action on file
   * @private
   * @param {*} data
   * @param {*} action
   * @returns {boolean}
   * @memberof DocumentsComponent
   */
  private checkPendingAction(data, action): boolean {
    if (data.actions && data.actions.length) {
      for (let i = 0; i < data.actions.length; i++) {
        let a = data.actions[i];
        if (a.actionId == action && a.actionStatus == 0) {
          return true;
        }
      }
    }
    return false;
  }


  /**
   * @description Flags to Enable/Disable more actions option
   * @memberof DocumentsComponent
   */
  moreOptions = {
    files: {
      fileActions: {
        startDiscussion: true,
        createReview: true,
        distributeFiles: true,
        publishFilesPlaceholder: true,
        publishFilesRevision: true,
        statusChange: true,
        assocForm: true,
        editAttributes: true,
        batchAssociation: true,
        copyFiles: true,
        linkFile: true,
        moveFiles: true,
        printFile: true,
        deactivateFiles: true,
        reactivateFiles: true,
        deleteFiles: true,
        startWorkflow: true
      },
      fileActionsDisabledTooltip: {
        publishFilesRevision: ''
      },
      folders: {
        newFolder: true,
        moveFolder: true,
        copyFolderStructure: true,
        createPlaceholder: true
      },
      tasks: {
        forInformation: true,
        forAcknowledgement: true,
        forCommentCoordination: true,
        forCommentIncorporation: true,
        forAction: true
      }
    },
    distribution: {
      clearAction: true,
      delegateAction: true,
      deactivateAction: true,
      reactivateAction: true,
      clearEnabledTooltip: "",
      deactiveEnabledTooltip: ""
    },
    discusions: {
      assocForm: true,
      markAsRead: true
    }
  }

  /**
   * @description Enable/Disable more options based on privledge
   * @param {*} isOpen
   * @returns
   * @memberof DocumentsComponent
   */

  private isFileDeleteBtnDisable(isMultipleProjectSelected,userPriv,ownerOrgId) {
    return (isMultipleProjectSelected || window['USP'].isProxyUserWorking || !this.util.hasAccess(userPriv, 'CAN_DELETE_RECORDS') || !(window['USP'].orgID == ownerOrgId));
  }

  moreOptionToggle(isOpen) {
    for (let key in this.moreOptions) {
      if (key === 'files') {
        for (let action in this.moreOptions[key]) {
          for (let actionName in this.moreOptions[key][action]) {
            this.moreOptions[key][action][actionName] = action === 'fileActionsDisabledTooltip' ? '' : true;
          }
        }
      } else {
        for (let action in this.moreOptions[key]) {
          this.moreOptions[key][action] = true;
        }
      }
    }

    if (!isOpen) {
      return;
    }

    if (this.multipleListings.selected.id == AppConstant.FILE_LISTING) {
      let projectId = this.util.getProjectId(this.treeSelection);
      if (projectId == -1) {
        this.moreOptions.files.folders.newFolder = true;
        this.moreOptions.files.folders.moveFolder = true;
        this.moreOptions.files.folders.copyFolderStructure = true;
        this.moreOptions.files.folders.createPlaceholder = true;
      } else if (this.treeSelection.isWorkspace) {
        this.util.getProjectPermission(this.util.getProjectId(this.treeSelection), this.treeSelection.dcId, (permissionData) => {
          this.moreOptions.files.folders.newFolder = !this.util.hasAccess(permissionData.privileges, 'PRIV_CREATE_PARENT_FOLDERS');
        });
        this.moreOptions.files.folders.moveFolder = true;
        this.moreOptions.files.folders.copyFolderStructure = true;
        this.moreOptions.files.folders.createPlaceholder = true;
      } else {

        this.util.getFolderPermission(this.treeSelection, 'folder', (permissionData) => {
          //TODO: Remove this code when move dialog is implemented in Angular
          window['SYSTEMPRIVILEGES'].userPrivileges[this.util.getProjectId(this.treeSelection)] = permissionData.privileges;

          let project = this.folderTreeComponent.getParentProject(this.treeSelection);

          this.moreOptions.files.folders.newFolder = !(this.util.hasFolderPermission(permissionData.folderPermissionValues, this.treeSelection.folderId, 'FOLDER_ADMIN_PERMISSION') && ((this.treeSelection.clonedFolderId == undefined || this.treeSelection.clonedFolderId == 0 || this.treeSelection.clonedFolderId == false) || (this.treeSelection.isFolderStructureInherited == undefined || this.treeSelection.isFolderStructureInherited == false || this.treeSelection.isFolderStructureInherited == 0)));

          this.moreOptions.files.folders.moveFolder = (project && project.isTemplate) || !(this.util.hasFolderPermission(permissionData.folderPermissionValues, this.treeSelection.folderId, 'FOLDER_ADMIN_PERMISSION') && ((this.treeSelection.clonedFolderId == undefined || this.treeSelection.clonedFolderId == 0 || this.treeSelection.clonedFolderId == false) || (this.treeSelection.isFolderStructureInherited == undefined || this.treeSelection.isFolderStructureInherited == false || this.treeSelection.isFolderStructureInherited == 0)));

          this.moreOptions.files.folders.copyFolderStructure = (project && project.isTemplate) || !(this.util.hasFolderPermission(permissionData.folderPermissionValues, this.treeSelection.folderId, 'FOLDER_ADMIN_PERMISSION') && ((this.treeSelection.clonedFolderId == undefined || this.treeSelection.clonedFolderId == 0 || this.treeSelection.clonedFolderId == false) || (this.treeSelection.isFolderStructureInherited == undefined || this.treeSelection.isFolderStructureInherited == false || this.treeSelection.isFolderStructureInherited == 0)));

          this.moreOptions.files.folders.createPlaceholder = (project && project.isTemplate) || !((this.util.hasFolderPermission(permissionData.folderPermissionValues, this.treeSelection.folderId, 'FOLDER_ADMIN_PERMISSION') ||
            this.util.hasFolderPermission(permissionData.folderPermissionValues, this.treeSelection.folderId, 'FOLDER_PUBLISH_AND_LINK_PERMISSION') ||
            this.util.hasFolderPermission(permissionData.folderPermissionValues, this.treeSelection.folderId, 'FOLDER_PUBLISH_PERMISSION')) &&
            (this.util.hasAccess(permissionData.privileges, 'PRIV_MANAGE_PROJECT_PLACEHOLDERS') ||
              this.util.hasAccess(permissionData.privileges, 'PRIV_MANAGE_ORGANIZATION_PLACEHOLDERS')));
        });
      }

    }

    let selectedRows = this.tableListingComponent.getSelectionDataCurrentPage();
    if (selectedRows.length === 0) {
      return;
    }

    let isArchivedProjectSelected = this.hasArchivedProjectSelected(selectedRows);
    if (this.multipleListings.selected.id == AppConstant.DISCUSSION_LISTING) {
      if (!isArchivedProjectSelected) {
        this.moreOptions.discusions.assocForm = false;
        this.moreOptions.discusions.markAsRead = false;
      }
      return;
    }

    let isPrivateData = false;
    for (let i = 0; i < selectedRows.length; i++) {
      if (selectedRows[i].isPrivate) {
        isPrivateData = true;
        break;
      }
    }
    this.moreOptions.distribution.deactiveEnabledTooltip = "";
    this.moreOptions.distribution.clearEnabledTooltip = "";
    let isMultiProjectSelected = this.hasMultipleProjects(selectedRows);
    let isAnyActiveDocSelected = this.hasAnyDocIsActive(selectedRows);
    if (this.multipleListings.selected.id == AppConstant.FILE_TRANSMITTAL_LISTING) {
      if (!isMultiProjectSelected && !isArchivedProjectSelected) {
        this.util.getProjectPermission(this.util.getProjectId(selectedRows[0]), selectedRows[0].dcId, (data) => {
          if (this.hasClearDelegateTransAction({
            status: AppConstant.ACTION_INCOMPLETE,
            priv: data.privileges,
            selectedFileData: selectedRows
          })) {
            this.moreOptions.distribution.delegateAction = false;
          }

          if (this.hasClearDelegateTransAction({
            status: AppConstant.ACTION_INCOMPLETE,
            priv: data.privileges,
            selectedFileData: selectedRows,
            isClearPriv: true
          })) {
            if (this.util.hasAccess(data.privileges, "CAN_EDIT_VISIBILITY_OF_OBJECTS") && data.isObjectPrivacyEnable == "true" && isPrivateData) {
              this.moreOptions.distribution.clearEnabledTooltip = this.util.lang.get('visibility-remove-note');
            }
            this.moreOptions.distribution.clearAction = false;
          }

          if (this.hasDEREActiveTransAction({
            "ownOrg": this.util.hasAccess(data.privileges, 'CAN_REACTIVATE_DOCUMENT_ACTIONS_OWN_ORG'),
            "allOrg": this.util.hasAccess(data.privileges, 'CAN_REACTIVATE_DOCUMENT_ACTIONS_ALL_ORG'),
            "status": AppConstant.ACTION_INACTIVE,
            selectedFileData: selectedRows,
          })) {
            this.moreOptions.distribution.reactivateAction = false;
          }

          if (this.hasDEREActiveTransAction({
            "ownOrg": this.util.hasAccess(data.privileges, 'CAN_DEACTIVATE_DOCUMENT_ACTIONS_OWN_ORG'),
            "allOrg": this.util.hasAccess(data.privileges, 'CAN_DEACTIVATE_DOCUMENT_ACTIONS_ALL_ORG'),
            "status": AppConstant.ACTION_INACTIVE,
            "invert": true,
            selectedFileData: selectedRows,
          })) {

            if (this.util.hasAccess(data.privileges, "CAN_EDIT_VISIBILITY_OF_OBJECTS") && data.isObjectPrivacyEnable == "true" && isPrivateData) {
              this.moreOptions.distribution.deactiveEnabledTooltip = this.util.lang.get('visibility-remove-note');
            }
            this.moreOptions.distribution.deactivateAction = false;
          }
        });
      }
      return;
    }

    if (this.multipleListings.selected.id == AppConstant.FILE_LISTING) {
      let actionCompleteStatus = this.getActionCompleteStatusData(selectedRows);
      this.util.getFolderPermissions(selectedRows, async (permissionData) => {
        this.selectedFolderPermissionData = permissionData;
        window['permissionValues'] = window['permissionValues'] || {};
        selectedRows.forEach((row) => {
          let folderId = row.folderId.split('$$')[0];
          window['permissionValues'][row.folderId] = permissionData.folderPermissionValues[folderId];
        });

        // Permission for User Privileges for More Options
        const userPrivFileMoreOptions = await this.util.getUserAppPrivileges();

        let lockedObjects = this.util.getLockedActivitiesObject(permissionData.lockedObjectIds);
        const publishRevisionData = this.hasPermissionToPublishRevision(selectedRows, permissionData);
        const pubilshMSRevision = this.hasPermissionToPublishMSRevision(selectedRows,userPrivFileMoreOptions,permissionData);
        this.moreOptions.files.fileActions.publishFilesRevision = !publishRevisionData.hasPermission || !pubilshMSRevision;
        this.moreOptions.files.fileActionsDisabledTooltip.publishFilesRevision = publishRevisionData.disabledTooltip;


        if ((selectedRows.length > 1 || !lockedObjects.comment) && this.util.hasAccess(permissionData.privileges, 'PRIV_CAN_CREATE_COMMENT') && isAnyActiveDocSelected && !isMultiProjectSelected) {
          this.moreOptions.files.fileActions.startDiscussion = false;
          this.moreOptions.files.fileActions.createReview = false;
        }

        if ((selectedRows.length > 1 || !lockedObjects.distribution) && !isArchivedProjectSelected && !isMultiProjectSelected && isAnyActiveDocSelected) {
          this.moreOptions.files.fileActions.distributeFiles = false;
        }

        if ((selectedRows.length > 1 || !lockedObjects.revisionUpload) && this.hasPermissionToCreateFilePlaceholder(selectedRows, permissionData)) {
          this.moreOptions.files.fileActions.publishFilesPlaceholder = false;
        }

        if (!this.isAllPlaceholderDoc(selectedRows) && isAnyActiveDocSelected && !isArchivedProjectSelected && (selectedRows.length > 1 || !lockedObjects.status) && !isMultiProjectSelected) {
          if (this.util.hasAccess(permissionData.privileges, 'PRIV_CHANGE_STATUS')) {
            this.moreOptions.files.fileActions.statusChange = false;
          } else {
            let hasIncompleteStatusChangeAction = false;
            selectedRows.some((file) => {
              file.actions && file.actions.some((action) => {
                if (action.actionStatus === 0 && action.actionId == DOC_ACTIONS.FOR_STATUS_CHANGE) {
                  hasIncompleteStatusChangeAction = true;
                  return true;
                }
              });
              return hasIncompleteStatusChangeAction;
            });

            if (hasIncompleteStatusChangeAction) {
              this.moreOptions.files.fileActions.statusChange = false;
            }
          }
        }

        if (!isArchivedProjectSelected) {
          this.moreOptions.files.fileActions.assocForm = false;
        }

        if ((selectedRows.length > 1 || (!lockedObjects.attributes)) && !isArchivedProjectSelected && isAnyActiveDocSelected && !isMultiProjectSelected) {
          if (this.util.hasAccess(permissionData.privileges, 'PRIV_ASSIGN_DOCUMENT_ATTRIBUTES')) {
            this.moreOptions.files.fileActions.editAttributes = false;
            this.moreOptions.files.fileActions.batchAssociation = false;
          } else {
            let disabledFilesList = this.getDisabledFileList(selectedRows);
            if (disabledFilesList.length !== selectedRows.length) {
              this.moreOptions.files.fileActions.editAttributes = false;
              this.moreOptions.files.fileActions.batchAssociation = false;
            }
          }
          if(selectedRows.findIndex((row) => (row.isLink && (row.linkType == 'Dynamic' || row.linkType == 'Static'))) > -1){
            this.moreOptions.files.fileActions.batchAssociation = false;
          }
        }

        this.moreOptions.files.fileActions['isBatchAssociationShow'] = permissionData.enableFileAssociation;

        if ((!isArchivedProjectSelected && !isMultiProjectSelected && this.hasPermissionToLinkDoc(selectedRows, permissionData.folderPermissionValues))) {
          this.moreOptions.files.fileActions.linkFile = false;
        }

        if ((!isMultiProjectSelected && this.hasPermissionToMoveFile(selectedRows, permissionData)) ) {
          this.moreOptions.files.fileActions.moveFiles = false;
        }

        if (!isMultiProjectSelected && this.hasPermissionToPrint(selectedRows, permissionData)) {
          this.moreOptions.files.fileActions.printFile = false;
        }

        if (!isMultiProjectSelected && this.enableCheckForActiveDeactiveDoc(selectedRows, permissionData, false)) {
          this.moreOptions.files.fileActions.deactivateFiles = false;
        }

        if (!isMultiProjectSelected && this.enableCheckForActiveDeactiveDoc(selectedRows, permissionData, true)) {
          this.moreOptions.files.fileActions.reactivateFiles = false;
        }

        if (!(isMultiProjectSelected || this.checkAllFilesAreActiveWorkflowOrPlacholder(selectedRows) || !this.util.hasAccess(permissionData.privileges, 'PRIV_MANAGE_WORKFLOW_RULES'))) {
          this.moreOptions.files.fileActions.startWorkflow = false;
        }

        this.moreOptions.files.fileActions.deleteFiles = this.isFileDeleteBtnDisable(isMultiProjectSelected,userPrivFileMoreOptions,selectedRows[0].ownerOrgId);
        this.moreOptions.files.tasks.forInformation = actionCompleteStatus[DOC_ACTIONS.FOR_INFORMATION];
        this.moreOptions.files.tasks.forAcknowledgement = actionCompleteStatus[DOC_ACTIONS.FOR_ACKNOWLEDGEMENT];
        this.moreOptions.files.tasks.forCommentCoordination = actionCompleteStatus[DOC_ACTIONS.FOR_COMMENT_COORD];
        this.moreOptions.files.tasks.forCommentIncorporation = actionCompleteStatus[DOC_ACTIONS.FOR_COMMENT_INCORP];
        this.moreOptions.files.tasks.forAction = actionCompleteStatus[DOC_ACTIONS.FOR_ACTION];
        this.moreOptions.files.fileActions.copyFiles = !this.hasPermissionToCopyFile(selectedRows, permissionData);
      });

    }

  };

  /**
   * @description Check priviledge for Clear/Deligate
   * @param {*} paramObj
   * @returns
   * @memberof DocumentsComponent
   */
  hasClearDelegateTransAction(paramObj) {
    let status = paramObj.status,
      priv = paramObj.priv,
      selectedFileData = paramObj.selectedFileData,
      hasAct = false,
      hasPriv = false,
      privValueArray = [],
      orgPriv,
      projectPriv,
      ownPriv;

    if (paramObj.isClearPriv) {
      orgPriv = this.util.hasAccess(priv, 'PRIV_CAN_CLEAR_ACTIONS_ORG'),
        projectPriv = this.util.hasAccess(priv, 'PRIV_CAN_CLEAR_ACTIONS_PROJECT'),
        ownPriv = this.util.hasAccess(priv, 'PRIV_CAN_CLEAR_ACTIONS_OWN');
    } else {
      orgPriv = this.util.hasAccess(priv, 'PRIV_CAN_DELEGATE_ACTIONS_ORG'),
        projectPriv = this.util.hasAccess(priv, 'PRIV_CAN_DELEGATE_ACTIONS_PROJECT'),
        ownPriv = this.util.hasAccess(priv, 'PRIV_CAN_DELEGATE_ACTIONS_OWN');
    }

    selectedFileData.forEach((data) => {
      data.actions = data.actions || [];
      let condition = data.actions[0] && data.actions[0].actionStatus == status;

      if (condition) {
        hasAct = true
      }
      if (hasAct) {
        //Can Clear/Delegate Own Actions only. i.e. for login Organisations and Project.
        let plainUserId = window['USP'].userID && window['USP'].userID.split("$$")[0] && parseInt(window['USP'].userID.split("$$")[0]);
        if (ownPriv && data.actions[0] && plainUserId && data.actions[0].recipientId == plainUserId) {
          hasPriv = true;
        }

        //Can Clear/Delegate Actions for all users (except own) across all organisation of the Project
        if (hasPriv == false && projectPriv && !(data.actions[0].recipientId == plainUserId)) {
          hasPriv = true;
        }

        // Can Clear/Delegate Actions for all users (except own) of the login organisation only
        if (hasPriv == false && orgPriv && (data.actions[0].recipientOrgId == window['USP'].orgID) && !(data.actions[0].recipientId == plainUserId)) {
          hasPriv = true;
        }

        if (!hasPriv) {
          hasAct = hasPriv;
        }
      }
      privValueArray.push(hasAct);
    });

    return this.$.inArray(true, privValueArray) == -1 ? false : true;
  }

  /**
   * @description Check priviledge for Active/Deactive
   * @param {*} paramObj
   * @returns
   * @memberof DocumentsComponent
   */
  hasDEREActiveTransAction(paramObj) {
    let hasAct = false,
      hasPriv = false,
      allOrgPrivilege = paramObj.allOrg,
      ownOrgPrivilege = paramObj.ownOrg,
      selectedFileData = paramObj.selectedFileData,
      privValueArray = [];


    selectedFileData.forEach((data) => {
      data.actions = data.actions || [];
      let condition = data.actions[0] && data.actions[0].actionStatus == paramObj.status;
      if (paramObj.invert) {
        condition = !condition
      }
      if (condition) {
        hasAct = true
      }

      if (hasAct) {
        if (allOrgPrivilege) { //Can Deactivate/Reactivate All Org Actions.
          hasPriv = true;
        } else if (ownOrgPrivilege && data.actions[0].recipientOrgId == window['USP'].orgID) { //Can Deactivate/Reactivate Own Org Actions only.
          hasPriv = true;
        }
        if (!hasPriv) {
          hasAct = hasPriv;
        }
      }
      privValueArray.push(hasAct);
    });

    return this.$.inArray(true, privValueArray) == -1 ? false : true;
  }

  /**
   * @description Check if selectied data hase single project
   * @param {*} data
   * @returns
   * @memberof DocumentsComponent
   */
  hasMultipleProjects(selectedRows) {
    let projects = [];

    selectedRows.forEach((row) => {
      let projectId = this.util.getProjectId(row);
      projects.indexOf(projectId) == -1 && projects.push(projectId);
    });
    return projects.length == 1 ? false : true;
  }

  /**
   * @description Check if selectied files has password protected file or not
   * @param {*} data
   * @returns
   * @memberof DocumentsComponent
   */
  hasAnyFilePasswordProtected(selectedRows) {
    let hasPasswordProtectedFile = false;
    selectedRows.some((row) => {
      if(row.passwordProtected) {
        hasPasswordProtectedFile = true;
        return true;
      }
    });
    return hasPasswordProtectedFile;
  }

  /**
   * @description Check if selectied data has multiple folders
   * @param {*} data
   * @returns
   * @memberof DocumentsComponent
   */
  hasMultipleFolders(selectedRows) {
    let folders = [];

    selectedRows.forEach((row) => {
      let folderId = row.folderId.split('$$')[0];
      folders.indexOf(folderId) == -1 && folders.push(folderId);
    });
    return folders.length == 1 ? false : true;
  }

  /**
   * @description Check if selectied data has link files or not
   * @param {*} selectedRows
   * @returns boolean
   * @memberof DocumentsComponent
   */
  hasLinkFiles(selectedRows) {
    return selectedRows.findIndex((row) => row.isLink) !== -1;
  }

  /**
   * @description Check that is there any archived project item is selected or not
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasArchivedProjectSelected(selectedRows) {
    let isArchivedProjectSelected = false;
    selectedRows.some((row) => {
      if (row.projectStatusId === PROJECT_STATUS.ARCHIVED) {
        isArchivedProjectSelected = true;
        return true;
      }
    });

    return isArchivedProjectSelected;
  }

  /**
   * @description Return true if one of the active projects file is selected
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasAnyProjectIsActive(selectedRows) {
    let isAnyProjectIsActive = false;
    selectedRows.some((row) => {
      if (row.projectStatusId === PROJECT_STATUS.ARCHIVED) {
        isAnyProjectIsActive = true;
        return true;
      }
    });

    return isAnyProjectIsActive;
  };

  /**
   * @description Return true if one of the active file is selected
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasAnyDocIsActive(selectedRows) {
    let isActive = false;
    selectedRows.some((row) => {
      if (row.isActive) {
        isActive = true;
        return true;
      }
    });

    return isActive;
  };

  /**
   * @description Return true if user has permission to move files from
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasPermissionToMoveFile(selectedRows, permissionData) {
    let flag = true;

    selectedRows.some((row) => {
      if (row.documentTypeId == DOC_TYPE_ID.FEDERATED_MODEL) {
        flag = false;
        return true;
      }
    });

    if (!flag) {
      return false;
    }

    //folder admin check
    selectedRows.some((row) => {
      if (!this.util.hasFolderPermission(permissionData.folderPermissionValues, row.folderId, 'FOLDER_ADMIN_PERMISSION')) {
        flag = false;
        return true;
      }
    });

    if (flag) {
      return true;
    }

    flag = this.util.hasAccess(permissionData.privileges, 'PRIV_CAN_MOVE_DOC');
    return flag;
  };

  /**
   * @description Return true if user has permission to link document
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasPermissionToLinkDoc(selectedRows, folderPermissionValues) {
    let flag = false;

    selectedRows.some((row) => {
      if (row.isActive && (row.documentTypeId == DOC_TYPE_ID.NORMAL || row.documentTypeId == DOC_TYPE_ID.EMAILS_ONLY || row.documentTypeId == DOC_TYPE_ID.AMAIL_PUBLISHED_FILE || row.documentTypeId == DOC_TYPE_ID.AMAIL_PUBLISHED_ATTACHMENT)) {
        flag = true;
        return true;
      }
    });

    if (!flag) {
      return false;
    }

    let selectedFoldersId = [];
    selectedRows.forEach((row) => {
      let folderId = row.folderId;
      if (selectedFoldersId.indexOf(folderId) == -1) {
        selectedFoldersId.push(folderId);
      }
    });

    if (selectedFoldersId.length == 1) {
      if (!this.util.hasFolderPermission(folderPermissionValues, selectedFoldersId[0], 'FOLDER_PUBLISH_AND_LINK_PERMISSION')
        && !this.util.hasFolderPermission(folderPermissionValues, selectedFoldersId[0], 'FOLDER_PERMISSION_VALUE_FOR_VIEW_N_LINK')
        && !this.util.hasFolderPermission(folderPermissionValues, selectedFoldersId[0], 'FOLDER_ADMIN_PERMISSION')) {
        flag = false;
      }
    }
    return flag;
  };

  /**
   * @description Return object of action's complete and incomplete status.
   * @param selectedRows - Selected rows data
   * @returns - Object
   * @memberof DocumentsComponent
   */
  getActionCompleteStatusData(selectedData) {
    let actionCompleteStatus = {};
    actionCompleteStatus[DOC_ACTIONS.FOR_INFORMATION] = true;
    actionCompleteStatus[DOC_ACTIONS.FOR_ACKNOWLEDGEMENT] = true;
    actionCompleteStatus[DOC_ACTIONS.FOR_COMMENT_COORDINATION] = true;
    actionCompleteStatus[DOC_ACTIONS.FOR_COMMENT_INCORP] = true;
    actionCompleteStatus[DOC_ACTIONS.FOR_ACTION] = true;

    if (this.hasArchivedProjectSelected(selectedData)) {
      return actionCompleteStatus;
    }

    selectedData.forEach((file) => {
      let actions = file.actions || [];
      actions.forEach((actionData) => {
        if (actionData.actionStatus == 0 && actionData.actionId == DOC_ACTIONS.FOR_INFORMATION) {
          actionCompleteStatus[DOC_ACTIONS.FOR_INFORMATION] = false;
        }

        if (actionData.actionStatus == 0 && actionData.actionId == DOC_ACTIONS.FOR_ACKNOWLEDGEMENT) {
          actionCompleteStatus[DOC_ACTIONS.FOR_ACKNOWLEDGEMENT] = false;
        }

        if (actionData.actionStatus == 0 && actionData.actionId == DOC_ACTIONS.FOR_COMMENT_COORDINATION && selectedData.length == 1) {
          actionCompleteStatus[DOC_ACTIONS.FOR_COMMENT_COORDINATION] = false;
        }

        if (actionData.actionStatus == 0 && actionData.actionId == DOC_ACTIONS.FOR_COMMENT_INCORP && selectedData.length == 1) {
          actionCompleteStatus[DOC_ACTIONS.FOR_COMMENT_INCORP] = false;
        }

        if (actionData.actionStatus == 0 && actionData.actionId == DOC_ACTIONS.FOR_ACTION) {
          actionCompleteStatus[DOC_ACTIONS.FOR_ACTION] = false;
        }

      });
    });
    return actionCompleteStatus;
  };

  /**
   * @description Return true if user has permission to checkout document
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasPermissionToCheckoutFile(selectedRows, folderPermission) {
    let isEnableCheckout = true;

    selectedRows.some((row) => {
      let permissionValue = folderPermission[row.folderId.split('$$')[0]];
      if (row.isLink || row.documentTypeId == DOC_TYPE_ID.PLACEHOLDER || row.checkOutStatus || permissionValue === AppConstant.FOLDER_VIEW_ONLY ||
        (permissionValue !== AppConstant.FOLDER_ADMIN_PERMISSION && permissionValue !== AppConstant.FOLDER_PUBLISH_AND_LINK_PERMISSION && permissionValue !== AppConstant.FOLDER_PUBLISH_PERMISSION)) {
        isEnableCheckout = false;
        return true;
      }
    });
    return isEnableCheckout;
  };

  /**
   * @description Return true if user has permission to undo checkout document
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasPermissionToUndoCheckoutFile(selectedRows) {
    let isEnableUndoCheckout = true;

    selectedRows.some((row) => {
      if (!row.checkOutStatus || (row.checkOutUserId !== window['USP'].userID.split('$')[0] && !row.isUserFolderAdmin)) {
        isEnableUndoCheckout = false;
        return true;
      }
    });
    return isEnableUndoCheckout;
  };

  /**
   * @description Return true if user has permission to copy document
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasPermissionToCopyFile(selectedRowData, permissionData) {
    if(!selectedRowData || selectedRowData.length == 0){
      return false;
    }

    if(!permissionData.enableFileCopy){
      return false;
    }

    if(this.hasMultipleProjects(selectedRowData)){
      return false;
    }

    return true;
  }

  /**
   * @description Return true if user has permission to download document
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasPermissionToDownload(selectedRowData, permissionData) {
    if (!selectedRowData || selectedRowData.length == 0
      || !this.util.hasAccess(permissionData.privileges, 'PRIV_CAN_DOWNLOAD_DOCUMENTS')
      || this.hasOnlyViewAccess(selectedRowData, permissionData)
      || (this.isAllPlaceholderDoc(selectedRowData) && !this.isAllFilePlaceholderDoc(selectedRowData))) {
      return false;
    }
    return true;
  };

    /**
   * @description Return true if user has permission to print document
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasPermissionToPrint(selectedRowData, permissionData) {
    if (!selectedRowData || selectedRowData.length == 0
      || !this.util.hasAccess(permissionData.privileges, 'PRIV_CAN_PRINT_DOCUMENTS')
      || this.hasOnlyViewAccess(selectedRowData, permissionData)
      || (this.isAllPlaceholderDoc(selectedRowData) && !this.isAllFilePlaceholderDoc(selectedRowData))) {
      return false;
    }
    return true;
  };

  /**
   * @description Return true if all row have for-publishing action
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasPublishingAction(selectedRowData) {
    // No row selected
    if (!selectedRowData || selectedRowData.length == 0) {
      return false;
    }

    // Check if any one row has no for-publishing action
    let found = selectedRowData.find(row => {
      if (!row.actions || !row.actions.length) {
        return true;
      }

      return !(row.actions.find(action => {
        return action.actionStatus == 0 && action.actionId == DOC_ACTIONS.FOR_PUBLISHING;
      }));
    });
    return found ? false : true;
  };

  /**
   * @description Return true if user has permission to publish revision
   * @param selectedRows - Selected rows data
   * @returns - Object
   * @memberof DocumentsComponent
   */
  hasPermissionToPublishRevision(selectedRowData, permissionData) {
    const result = {
      hasPermission: false,
      disabledTooltip: this.util.lang.get('publish-revision-disabled-tooltip')
    };
    // No row selected
    if (!selectedRowData || selectedRowData.length == 0) {
      return result;
    }

    // Do not allow to upload multiple folder files
    if (this.hasMultipleFolders(selectedRowData)) {
      return result;
    }

    // We can get folderId from first array item as all items have same folderId
    let folderId = selectedRowData[0].folderId.split('$$')[0];

    // When folder has no upload permission and no row with "For publishing" action
    if (!this.checkFolderUploadPermission(permissionData, folderId) && !this.hasPublishingAction(selectedRowData)) {
      return result;
    }

    if(this.hasLinkFiles(selectedRowData)) {
     result.disabledTooltip = this.util.lang.get('publish-revision-disabled-tooltip-for-link-files');
      return result;
    }

    result.hasPermission = true;
    result.disabledTooltip = "";
    return result;
  };

  /**
  * @description Return true if user has permission to publish MS revision
  * @param selectedRows - Selected rows data
  * @returns - Boolean
  * @memberof DocumentsComponent
  */
  hasPermissionToPublishMSRevision(selectedRowData,userPrivFileRightClick,permissionData) {
    var hasPermissionToPublishMSRevision = false;

    if (selectedRowData.length > 0) {
      this.selectedMSFiles = 0;
      this.otherSelectedFiles = 0;

      for (let i = 0; i < selectedRowData.length; i++) {
        if (selectedRowData[i].hasOfficeFile) {
          this.selectedMSFiles++;
        } else {
          this.otherSelectedFiles++;
        }
      }

      if (this.selectedMSFiles > 1 || (this.selectedMSFiles === 1 && this.otherSelectedFiles > 0)) {
        hasPermissionToPublishMSRevision = false;
      } else {
        hasPermissionToPublishMSRevision = !(this.util.hasAccess(userPrivFileRightClick, 'ENABLE_MICROSOFT_OFFICE') && permissionData.projectADriveCDEAddonPurchaseStatus) && selectedRowData[0].hasOfficeFile ? false : true;
      }
    }

    return hasPermissionToPublishMSRevision;
  }

  /**
   * @description Return true if user has only read action on selected documents
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasOnlyViewAccess(selectedRows, permissionData) {
    let onlyViewAccess = false;
    try {
      let folderIDs = selectedRows.map((row) => {
        return row.folderId.split('$$')[0];
      });

      folderIDs.forEach((folderId) => {
        if (permissionData.folderPermissionValues[folderId] == AppConstant.FOLDER_VIEW_ONLY) {
          onlyViewAccess = true;
        }
      });
    } catch (e) { }

    return onlyViewAccess;
  };

    /**
     *
     * @param selectedRow
     */
    isValidFileToWork(selectedRow) {
        return selectedRow[0].documentFolderPath?.length < 259;
    }

  /**
   * @description Return true if user has permission to mark document as active or inactive
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  enableCheckForActiveDeactiveDoc(selectedRows, permissionData, forReActive) {
    let canAccessDeactivatedDocs = this.util.hasAccess(permissionData.privileges, 'PRIV_ACCESS_DEACTIVATED_DOCUMENTS');
    let canDeactivateDocs = this.util.hasAccess(permissionData.privileges, 'PRIV_DEACTIVATE_DOCUMENTS');
    let isFolderAdmin = false;
    let flag = false;
    let flipFlop = false;

    selectedRows.forEach((row) => {
      if (this.util.hasFolderPermission(permissionData.folderPermissionValues, row.folderId, 'FOLDER_ADMIN_PERMISSION')) {
        isFolderAdmin = true;
      }
      flipFlop = row.isActive != forReActive;
    });
    if (flipFlop) {
      if ((forReActive && (isFolderAdmin || canAccessDeactivatedDocs)) || (forReActive == false && canDeactivateDocs)) {
        flag = true;
      }
    }


    return flag;
  };

  /**
   * @description Return true if user has permission to create document placeholder
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  hasPermissionToCreateFilePlaceholder(selectedRows, permissionData) {
    if (this.hasMultipleProjects(selectedRows) || this.hasMultipleFolders(selectedRows) || this.isAllPlaceholderDoc(selectedRows) ||
      this.hasArchivedProjectSelected(selectedRows) || this.hasOnlyViewAccess(selectedRows, permissionData) ||
      !(this.util.hasAccess(permissionData.privileges, 'PRIV_MANAGE_PROJECT_PLACEHOLDERS') || this.util.hasAccess(permissionData.privileges, 'PRIV_MANAGE_ORGANIZATION_PLACEHOLDERS'))) {
      return false;
    }


    let enablePlaceholder = true;
    selectedRows.some(attributeData => {
      if (attributeData.isEntireDocDeactivated || attributeData.documentTypeId == DOC_TYPE_ID.PAPERDOC
        || attributeData.documentTypeId == DOC_TYPE_ID.FEDERATED_MODEL || attributeData.isLink || attributeData.documentTypeId == DOC_TYPE_ID.PLACEHOLDER
        || (attributeData.checkOutUserId != "0" && window['USP'] && window['USP'].userID && window['USP'].userID.split("$$")[0] != attributeData.checkOutUserId)) {
        enablePlaceholder = false;
        return true;
      }
    });

    if (enablePlaceholder) {
      let singleFolderPermissions = [];
      for (let key in permissionData.folderPermissionValues) {
        (key != "Project_viewer_id") && singleFolderPermissions.push({ folderId: key, permission: permissionData.folderPermissionValues[key] });
      }

      if (singleFolderPermissions.length == 1) {
        enablePlaceholder = (singleFolderPermissions[0].permission == AppConstant.FOLDER_ADMIN_PERMISSION ||
          singleFolderPermissions[0].permission == AppConstant.FOLDER_PUBLISH_AND_LINK_PERMISSION ||
          singleFolderPermissions[0].permission == AppConstant.FOLDER_PUBLISH_PERMISSION);
      }
    }

    return enablePlaceholder;
  };


  /**
   * @description Return true if all selected documents are placeholders.
   * @param selectedRows - Selected rows data
   * @returns - Boolean
   * @memberof DocumentsComponent
   */
  isAllPlaceholderDoc(selectedRows?) {
    let allPlaceholder = true;
    selectedRows.some((row) => {
      if (row.documentTypeId != DOC_TYPE_ID.PLACEHOLDER) {
        allPlaceholder = false;
        return true;
      }
    });

    return allPlaceholder;
  };

  /**
  * @description Return true if all selected documents are file placeholders.
  * @param selectedRows - Selected rows data
  * @returns - Boolean
  * @memberof DocumentsComponent
  */
  isAllFilePlaceholderDoc(selectedRows?) {
    let allFilePlaceholder = true;
    selectedRows.some((row) => {
      if (row.documentTypeId !== DOC_TYPE_ID.PLACEHOLDER || row.issueNo === 1) {
        allFilePlaceholder = false;
        return true;
      }
    });

    return allFilePlaceholder;
  };

  /**
   * @description After delegate action complete show msg and update row data
   * @param {*} data
   * @memberof DocumentsComponent
   */
  completeDelegateAction(data) {
    let totalSelectedRecords = data.delegated + data.notDelegated + data.notDelegatedForStatusChange;
    let actionNotDoneCnt = totalSelectedRecords - data.delegated;
    if (data.notDelegated || data.notDelegatedForStatusChange) {
      this.util.notification.warn({
        theClass: 'notification-sm',
        msg: 'Task(s) delegated for ' + data.delegated + ' / ' + totalSelectedRecords + ' record(s).<br>' +
          + actionNotDoneCnt + ' record(s) were not delegated due to one/all of the below reasons: <br>'
          + ' a) You do not have privilege to delegate action on selected record <br>'
          + ' b) Action for selected record was already delegated <br>'
          + ' c) Same action for the same user cannot be delegated!'
      });
    } else {
      this.util.notification.success({
        theClass: 'notification-sm',
        msg: 'Task(s) delegated for ' + data.delegated + ' / ' + totalSelectedRecords + ' record(s)'
      });
    }

    this.updateSelectedRowData(true);
  }

  /**
   * @description Update passed row data of Table Listing
   * @memberof DocumentsComponent
   */
  updateRowData(rows) {
    if (rows && rows.length) {
      this.updateSelectedRowData(undefined, rows);
      return;
    }
    this.updateSelectedRowData(true);
  }

  /**
   * Partial refresh timeout handlers
   */
  partialRefreshTimers: any = [];

  /**
   * @description Update selected row data of Table Listing
   * @memberof DocumentsComponent
   */
  updateSelectedRowData(refreshAll?, rows?: any) {
    let partialRefreshTimer = setTimeout(() => {
      if (!refreshAll) {
        if (this.multipleListings.selected.id == AppConstant.DISCUSSION_LISTING) {
          this.tableListingComponent.updateSelectedRowData({
            action_id: AppConstant.PARTIAL_REFRESH,
            appType: this.appType,
            entityTypeId: ENTITY_TYPE.DISCUSSION,
            updateData: '',
            dcId: 1,
            key: 'projectId#commId'
          }, rows);
        } else if (this.multipleListings.selected.id == AppConstant.FILE_TRANSMITTAL_LISTING) {
          this.tableListingComponent.updateSelectedRowData({
            action_id: AppConstant.PARTIAL_REFRESH,
            appType: this.appType,
            entityTypeId: ENTITY_TYPE.DISTRIBUTION_FILE,
            updateData: '',
            dcId: 1,
            key: 'projectId#actions[0].id'
          }, rows);
        } else {
          this.tableListingComponent.updateSelectedRowData({
            action_id: AppConstant.PARTIAL_REFRESH,
            appType: this.appType,
            entityTypeId: ENTITY_TYPE.FILE,
            updateData: '',
            dcId: 1,
            key: 'projectId#revisionId'
          }, rows);
        }
      } else {
        this.updateFileListing({ item: this.treeSelection });
      }

      this.getLeftNavCounts();
    }, 2000);

    this.partialRefreshTimers.push(partialRefreshTimer);

  }

  /**
   * @description Refresh table listing data
   * @memberof DocumentsComponent
   */
  refreshList(forData?, actionId?, treeSelection?, extra?) {
    if (!extra) {
      extra = {};
    }
    let data = {
      listingTypeId: (this.activeListing.type == "UNREAD_REVIEWS_COUNT") ? AppConstant.REVIEW_LISTING : this.multipleListings.selected.id,
      for: forData || this.activeListing.type,
      ...extra
    }
    if (actionId) {
      data[actionId] = true;
    }
    this.tableListingComponent.addQueries(data, treeSelection);
  }

  uploader: FileUploader;

  publishRevisionUploader: FileUploader;

  hasBaseDropZoneOver: boolean = false;

  /**
   * Invoke on hover of drop zone with file drag
   * @param {Event} e
   * @returns
   * @memberof DocumentsComponent
   */
  fileOverBase(e) {
    this.hasBaseDropZoneOver = e && ((this.treeSelection.folderId && this.hasUploadPerm) || (this.treeSelection.projectID && this.hasFolderUploadPerm));
  }

  private initPublishRevisionUploader() {
    this.publishRevisionUploader = new FileUploader({
      url: ApiConstant.ADODDLE_UPLOAD_CONTROLLER + '?action_id=' + AppConstant.UPLOAD_CHUNK_FILE,
      isHTML5: true,
      itemAlias: 'file',
      autoUpload: false,
      removeAfterUpload: false
    });

    this.publishRevisionUploader.onAfterAddingAll = (files: Array<FileItem>) => {
      if (!files || !files.length) {
        return;
      }

      const selectedFiles = this.tableListingComponent.getSelectionDataCurrentPage();
      if (selectedFiles && selectedFiles.length == 1) {
        this.publishRevision(selectedFiles, files[0]._file);
      }
      this.publishRevisionUploader.clearQueue();
    };
  }


  /**
   * @description Function to check whether the parent folder is duplicate in name or not.
   *
   * @private
   * @param {*} nameArray - Parent Folder names in Array
   * @param {*} data - API data(dcId, folderId, isWorkspace, projectId)
   * @param {*} folderObj - Folder Obj formed during the parsing of the files and folders
   * @param {*} callBack - Callback function to be executed on Success
   * @memberof DocumentsComponent
   */
  private checkForDuplicateFolderNameExist(nameArray: any, tempObj, rootFolderObj, allFiles) {
    let obj = {
      projectId: this.treeSelection.projectID || this.treeSelection.projectId,
      folderId: this.treeSelection.folderId,
      isWorkspace: this.treeSelection.folderId ? 0 : 1,
      dcId: this.treeSelection.dcId || window['LOCAL_DC_ID'],
      extra: JSON.stringify(nameArray),
      processInDb: false,
    };
    if (!obj.folderId) {
      delete obj.folderId
    }
    this.util.ajax({
      url: ApiConstant.CHECK_DUP_FOLDER_NAME,
      method: 'POST',
      data: obj,
      _cdnUrl: this.util.getTabServiceUrl(this.util.TabServices.FILES),
      _dcId: obj.dcId,
      success: (response) => {
        let data = response.body;
        for (let i = 0; i < data.length; i++) {
          let names = data[i];
          if (!rootFolderObj[names.newFolderName]) {
            rootFolderObj[names.newFolderName] = {};
          }
          let loopTempObj = rootFolderObj[names.newFolderName];
          for (const key in tempObj) {
            if (Object.prototype.hasOwnProperty.call(tempObj, key)) {
              let element = tempObj[key];
              if (key.split(":::")[0] === names.oldFolderName) {
                if (names.newFolderName !== names.oldFolderName) {
                  delete loopTempObj[key];
                  let newKey = key.replace(names.oldFolderName, names.newFolderName);
                  loopTempObj[newKey] = element;
                  loopTempObj[newKey].relativeFolderPath = element.relativeFolderPath.replace(names.oldFolderName, names.newFolderName);
                  if (element.parentLevel === 0) {
                    loopTempObj[newKey].name = names.newFolderName;
                  } else if (element.parentLevel === 1) {
                    loopTempObj[newKey].parent = names.newFolderName;
                  }
                } else {
                  loopTempObj[key] = element;
                }
              }
            }
          }
        }
        if (Object.keys(rootFolderObj).length) {
          this.zone.run(() => {
            this.activity.addActivity({
              type: 'UploadFolder',
              options: {
                params: {
                  projectId: this.treeSelection.projectID || this.treeSelection.projectId,
                  folderId: this.treeSelection.folderId,
                  isWorkspace: this.treeSelection.folderId ? 0 : 1,
                  dcId: this.treeSelection.dcId || window['LOCAL_DC_ID']
                },
                files: allFiles,
                folderUpload: true,
                folder_tree_data: rootFolderObj,
                folderPath: this.treeSelection.folderPath || this.treeSelection.projectName,
                onFolderComplete: (obj) => {
                  this.folderTreeComponent.refreshFolder(this.treeSelection);
                },
                onComplete: (obj) => {
                  this.multipleListings.selected = this.multipleListings.list[0];
                  this.updateSelectedRowData(true);
                }
              }
            });
          })
        }
      },
      error: (err) => {
        console.log('error folder create', err)
      }
    });
  }

  private initUploader() {
    this.uploader = new FileUploader({
      url: ApiConstant.ADODDLE_UPLOAD_CONTROLLER + '?action_id=' + AppConstant.UPLOAD_CHUNK_FILE,
      isHTML5: true,
      itemAlias: 'file',
      autoUpload: false,
      removeAfterUpload: false
    });

    this.uploader.onAfterAddingAll = (files: Array<FileItem>) => {
      if (!(this.treeSelection.folderId || (this.treeSelection.projectID || this.treeSelection.projectId))) {
        this.uploader.clearQueue();
        return;
      }

      if (!files || !files.length) {
        return;
      }
      for (let i = 0; i < files.length; i++) {
        const f = files[i].file;
        let fileType = f.name.split(".");
        let extention = fileType[fileType.length - 1];
        if ((extention.toLowerCase()).match(/^iam$/) || (extention.toLowerCase()).match(/^catproduct$/) || (extention.toLowerCase()).match(/^ifc$/)) {
          files = files.sort((a, b) => {
            let aFileName = a.file.name.substr(0, a.file.name.lastIndexOf("."))
            let bFileName = b.file.name.substr(0, b.file.name.lastIndexOf("."))
            if (aFileName.toLowerCase() < bFileName.toLowerCase()) return -1;
            if (aFileName.toLowerCase() > bFileName.toLowerCase()) return 1;
            return 0;
          });
          break;
        }
      }
      var tempObj = {}
      var allFiles = []
      let rootFolderObj = {}
      var rootFolderArray = []
      for (var i = 0; i < files.length; i++) {
        let f = files[i];
        if (f.file.isDirectory && this.hasFolderUploadPerm) {
          if (f.file.rawFile["relativePath"]) {
            let splitFolderArray = f.file.rawFile["relativePath"].split("/")
            splitFolderArray.length && splitFolderArray.splice(splitFolderArray.indexOf(''), 1)

            if (splitFolderArray.length) {
              // Last element is /. removed and loop.
              if (!this.folderArrayValidation(splitFolderArray)) {
                return
              }
              for (var j = splitFolderArray.length - 1; j >= 0; j--) {
                //Adding Root Folder to Object
                if (j == 0) {
                  // Root Folder files.
                  if (!tempObj[splitFolderArray[0]]) {
                    tempObj[splitFolderArray[0]] = { "name": splitFolderArray[0], "parent": this.treeSelection.folder_title || "", "level": 1, "parentLevel": 0, relativeFolderPath: splitFolderArray[0] }
                    rootFolderArray.push(splitFolderArray[0])
                  }
                  // Folder exists. Add files.
                  f.file.size && splitFolderArray.length === 1 && (tempObj[splitFolderArray[0]].files ? tempObj[splitFolderArray[0]].files.push(f) : tempObj[splitFolderArray[0]].files = [f]);

                  continue
                } else if (j === splitFolderArray.length - 1) {

                  if (!tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)]) {
                    tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)] = { "name": splitFolderArray[j], "parent": splitFolderArray[j - 1], "level": j + 1, "parentLevel": j, relativeFolderPath: this.util.myJoin(splitFolderArray, "/", null, j) }
                  }
                  // Folder exists. Add files.
                  f.file.size && (tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)].files ? tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)].files.push(f) : (tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)].files = [f]));
                } else {
                  if (!tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)]) {
                    tempObj[this.util.myJoin(splitFolderArray, ":::", null, j)] = { "name": splitFolderArray[j], "parent": splitFolderArray[j - 1], "level": j + 1, "parentLevel": j, relativeFolderPath: this.util.myJoin(splitFolderArray, "/", null, j) }
                  }
                }
              }
            }
          } else {
            // Folder. Add it as empty folder.
            let filePathArray = f.file.rawFile["fullPath"] ? f.file.rawFile["fullPath"].split('/') : [];
            filePathArray.splice(filePathArray.indexOf(''), 1)
            // First element is /
            if (filePathArray.length) {
              //Check if any folder contains invalid characters or not. If Yes, terminate the process. If No, proceed as usual.
              if (!this.folderArrayValidation(filePathArray)) {
                return
              }

              for (var j = filePathArray.length - 1; j >= 0; j--) {
                if (j == 0) {
                  // Adding Root Folder to Object.
                  if (!tempObj[filePathArray[0]]) {
                    tempObj[filePathArray[0]] = { name: filePathArray[0], level: 1, parent: this.treeSelection.folder_title || "", parentLevel: 0, relativeFolderPath: filePathArray[0] }
                    rootFolderArray.push(filePathArray[0])
                  }
                } else if (!tempObj[this.util.myJoin(filePathArray, ":::", null, j)]) {
                  // Entry does not exists. Add it
                  tempObj[this.util.myJoin(filePathArray, ":::", null, j)] = {
                    "name": filePathArray[j], "parent": filePathArray[j - 1], "level": j + 1, "parentLevel": j, relativeFolderPath: this.util.myJoin(filePathArray, "/", null, j)
                  }
                }
              }
            }
          }
        } else if (f.file.isDirectory && !((this.treeSelection.folderId || (this.treeSelection.projectId || this.treeSelection.projectID)) && this.hasFolderUploadPerm)) {
          return
        } else if (!f.file.isDirectory && !((this.treeSelection.folderId && (this.treeSelection.projectId || this.treeSelection.projectID)) && this.hasUploadPerm)) {
          return
        }
        allFiles.push(f._file)
      }

      if (rootFolderArray.length) {
        this.checkForDuplicateFolderNameExist(rootFolderArray, tempObj, rootFolderObj, allFiles);
      } else {
        this.activity.addActivity({
          type: 'Upload',
          options: {
            params: {
              projectId: this.treeSelection.projectId,
              folderId: this.treeSelection.folderId,
              dcId: this.treeSelection.dcId || window['LOCAL_DC_ID'] // TODO:get correct dc id
            },
            maxCharLength : true,
            files: files.map((f) => { return f._file }),
            excelImport: true,
            onComplete: (obj) => {
              if (!this.activity.isOpen()) {
                return;
              }

              if (obj && obj.options && obj.options.params && this.treeSelection.folderId && obj.options.params.folderId != this.treeSelection.folderId) {
                return;
              }

              this.multipleListings.selected = this.multipleListings.list[0];
              this.updateSelectedRowData(true);
            }
          }
        });
      }


    };
  };
  // method to get the list of models based on project id
  private getProjectModelLists(selectedRow, callback) {
    this.util.ajax({
      url: ApiConstant.GET_PROJECT_MODELS,
      method: 'POST',
      data: {
        projectId: selectedRow[0].projectId || '',
        docRef: selectedRow[0].docRef
      },
      _dcId: selectedRow[0].dcId,
      success: (response) => {
        if (response.body && response.body.length > 0) {
          this.getDisciplineList((disciplineList) => {
            let modelDisciplineObj = {};
            Object.assign(modelDisciplineObj, { disciplineList: disciplineList }, { projectModelList: response.body });
            callback && callback(modelDisciplineObj);
          });
        } else {
          callback && callback(null);
        }
      },
      error: (err) => { }
    });
  }

  //method to get the list of models associated with the file for removing
  private getAssociatedModelsList(selectedRow, callback) {
    this.util.ajax({
      url: ApiConstant.GET_ASSOCIATED_MODELS,
      method: 'POST',
      data: {
        projectId: selectedRow[0].projectId || '',
        revisionId: selectedRow[0].revisionId || '',
        docRef: selectedRow[0].docRef
      },
      _dcId: selectedRow[0].dcId,
      success: (response) => {
        callback && callback(response.body);
      },
      error: (err) => { }
    });
  }

  //method to get the list of all the available disciplines
  private getDisciplineList(callback) {
    this.util.ajax({
      url: ApiConstant.GET_MODEL_DISCIPLINES,
      method: 'GET',
      success: (response) => {
        callback && callback(response.body);
      },
      error: (err) => { }
    });
  }

  createProjectModelList(modelDisciplineObj): any[] {
    let submenuItem = [];
    if (modelDisciplineObj && modelDisciplineObj.projectModelList && modelDisciplineObj.projectModelList.length > 0) {
      let selectedRowData = this.tableListingComponent.getSelectionDataCurrentPage();
      modelDisciplineObj.projectModelList.forEach(projectList => {
        let submenu = {
          id: "AddToModel",
          icon: "",
          title: projectList.userModelName,
          callback: (data) => { },
          hidden: false,
          disable: projectList.isDocRefAlreadyAdded,
          data: []
        };
        submenuItem.push(submenu);
      });
    }
    return submenuItem;
  }

  private createDisciplineList(selectedRow, modelDisciplineObj, projectList): any[] {
    let submenuItem = [];
    if (modelDisciplineObj && modelDisciplineObj.disciplineList && modelDisciplineObj.disciplineList.length > 0) {
      modelDisciplineObj.disciplineList.forEach(disciplineList => {
        let copySelectedRow = this.util.copy(selectedRow);
        if (selectedRow && selectedRow[0]) {
          Object.assign(copySelectedRow[0], { projectList: projectList, disciplineList: disciplineList });
        }
        let submenu = {
          id: "discipline",
          icon: "",
          title: disciplineList.disciplineName,
          callback: (data) => {
            if (data && data.length > 0) {
              data.forEach(activity => {
                this.activity.addActivity({
                  type: 'AddRemoveModel',
                  forPublish: true,
                  options: {
                    params: {
                      projectId: activity.projectId,
                      modelId: activity.projectList.bimModelId,
                      disciplineId: activity.disciplineList.disciplineId,
                      isAddToModel: true,
                      revisionIds: activity.revisionId,
                      modelVersionId: "-1",
                      modelName: activity.userModelName,
                      dcId: activity.dcId
                    },
                  }
                });
              });
            }
            //  this.addRemoveModel(data, true);
          },
          hidden: false,
          disable: false,
          data: copySelectedRow,
        }
        submenuItem.push(submenu);
      });
    }
    return submenuItem;
  }

  private addRemoveModel(selectedRows, addRemove: boolean, rowData) {
    let revisionId = '';
    if (selectedRows && selectedRows.length > 0) {
      selectedRows.forEach(sr => {
        if (revisionId.trim()) {
          revisionId = revisionId.concat(',' + sr.revisionId);
        } else {
          revisionId = revisionId.concat(sr.revisionId);
        }
      });
    }
    this.util.ajax({
      url: ApiConstant.ADD_REMOVE_MODEL,
      method: 'POST',
      data: {
        projectId: selectedRows[0].projectId,
        modelId: rowData.modelId,
        //disciplineId: selectedRows[0].disciplineList.disciplineId,
        isAddToModel: addRemove,
        revisionIds: revisionId,
        modelVersionId: "-1"
      },
      _dcId: selectedRows[0].dcId,
      success: (response) => {
        if (!response.body) {
          this.util.notification.error({
            theClass: 'notification-sm',
            title: 'Error',
            msg: 'Error'
          });
        } else {
          selectedRows[0].bimModelId = rowData.modelId;
          selectedRows[0].userModelName = rowData.modelName;
          selectedRows[0].modelTypeId = rowData.typeID;
          this.listing.openModel(selectedRows[0]);
          this.updateSelectedRowData(true, selectedRows);
        }
        selectedRows = [];
        window['setSelectedModelFiles']();
      },
      error: (err) => {
        window['setSelectedModelFiles']();
      }
    });
  }
  private createAssociatedModelList(associatedModelList, id?: string): any[] {
    let submenuItem = [];
    if (associatedModelList && associatedModelList.length > 0) {
      let selectedRow = this.tableListingComponent.getSelectionDataCurrentPage();
      associatedModelList.forEach(associatedModelList => {
        let copySelectedRow = this.util.copy(selectedRow);
        if (selectedRow && selectedRow[0]) {
          switch (id) {
            case 'RemoveFromModel':
              Object.assign(copySelectedRow[0], { projectList: associatedModelList });
              break;
            case 'ViewInModel':
              copySelectedRow[0].bimModelId = associatedModelList.bimModelId;
              Object.assign(copySelectedRow[0], { userModelName: associatedModelList.userModelName }, { modelTypeId: associatedModelList.modelTypeId });
          }
        }
        let submenu = {
          id: id,
          icon: "",
          title: associatedModelList.userModelName,
          callback: (data) => {
            switch (id) {
              case 'RemoveFromModel':
                if (data && data.length > 0) {
                  data.forEach(activity => {
                    this.activity.addActivity({
                      type: 'AddRemoveModel',
                      forPublish: true,
                      options: {
                        params: {
                          projectId: activity.projectId,
                          modelId: activity.projectList.bimModelId,
                          disciplineId: '0',
                          isAddToModel: false,
                          revisionIds: activity.revisionId,
                          modelVersionId: "-1",
                          modelName: activity.userModelName,
                          dcId: activity.dcId
                        },
                      }
                    });
                  });
                }
                break;
              case 'ViewInModel':
                this.listing.openModel(data[0]);
                break;
            }
          },
          hidden: false,
          disable: associatedModelList.isMerged,
          data: copySelectedRow,
        };
        submenuItem.push(submenu);
      });
    }
    return submenuItem;
  }


  private getCommentListingType(data) {
    let parentProject = this.folderTreeComponent.getParentProject(data);
    let enableCommentReview = parentProject ? parentProject.enableCommentReview : false;
    if (parentProject.projectID && parentProject.projectID.split('$$')[0] != '-1' && enableCommentReview) {
      return AppConstant.REVIEW_LISTING;
    }
    return AppConstant.DISCUSSION_LISTING;
  }

  private canManageProjectModels(permissionData: any): boolean {
    return this.util.hasAccess(permissionData.privileges, 'CAN_MANAGE_PROJECTS_MODELS');
  }

  private isNotAddedToModel(selectedRowData: any[]): boolean {
    return selectedRowData.some((file) => {
      const bimModelIds = (file.bimModelIds) ? file.bimModelIds.split('$$') : [];
      return (bimModelIds.length === 0 || bimModelIds[0] === '0' || bimModelIds[0] === '-1');
    });
  }

  private hasViewerDisabledProjectFilesSelected(selectedRowData: any[]): boolean {
    const bimEnabledProjectIds = this.folderTreeComponent.getBimEnabledProjectIds();
    const threeDRepoEnabledProjectIds = this.folderTreeComponent.get3DRepoEnabledProjectIds();

    return selectedRowData.some((row) => {
      const projectId = this.util.getProjectId(row);
      return projectId && projectId !== '-1' && (bimEnabledProjectIds.indexOf(projectId) === -1 && threeDRepoEnabledProjectIds.indexOf(projectId) === -1);
    });
  }

  private has3DRepoViewerDisabledProjectFilesSelected(selectedRowData: any[]): boolean {
    const threeDRepoEnabledProjectIds = this.folderTreeComponent.get3DRepoEnabledProjectIds();

    return selectedRowData.some((row) => {
      const projectId = this.util.getProjectId(row);
      return projectId && projectId !== '-1' && threeDRepoEnabledProjectIds.indexOf(projectId) === -1;
    });
  }

  private hasOnlyHoopsViewerEnabledProjectFilesSelected(selectedRowData: any[]): boolean {
    const bimEnabledProjectIds = this.folderTreeComponent.getBimEnabledProjectIds();
    const threeDRepoEnabledProjectIds = this.folderTreeComponent.get3DRepoEnabledProjectIds();

    return selectedRowData.every((row) => {
      const projectId = this.util.getProjectId(row);
      return projectId && projectId !== '-1' && (bimEnabledProjectIds.indexOf(projectId) != -1 && threeDRepoEnabledProjectIds.indexOf(projectId) == -1);
    });
  }

  private hasOnly3DRepoViewerEnabledProjectFilesSelected(selectedRowData: any[]): boolean {
    const threeDRepoEnabledProjectIds = this.folderTreeComponent.get3DRepoEnabledProjectIds();

    return selectedRowData.every((row) => {
      const projectId = this.util.getProjectId(row);
      return projectId && projectId !== '-1' && threeDRepoEnabledProjectIds.indexOf(projectId) != -1;
    });
  }

  private hasOnlyValidSuportFilesSelected(selectedRowData: any[]): boolean {
    const self = this;
    
    const projectId = self.util.getProjectId(selectedRowData[0]);
    const threeDRepoEnabledProjectIds = self.folderTreeComponent.get3DRepoEnabledProjectIds();
    if(threeDRepoEnabledProjectIds.indexOf(projectId) != -1) {
      return self.hasOnly3DRepoViewerSupportedFileSelected(selectedRowData);
    }

    const bimEnabledProjectIds = self.folderTreeComponent.getBimEnabledProjectIds();
    if(bimEnabledProjectIds.indexOf(projectId) != -1) {
      return self.hasOnlyHoopsViewerSupportedFileSelected(selectedRowData);
    }

    return false;
  }

  private get3DRepoRevisionsDetail(selectedRowData: any[]): any {
    const threeDRepoEnabledProjectIds = this.folderTreeComponent.get3DRepoEnabledProjectIds();

    let revisions = [];
    selectedRowData.forEach((row) => {
      const projectId = this.util.getProjectId(row);
      if(projectId && projectId !== '-1' && row.has3DRepoViewerSupport && threeDRepoEnabledProjectIds.indexOf(projectId) != -1) {
        revisions.push({
          projectId: projectId,
          dcId: row.dcId,
          revisionId: row.revisionId,
          projectName: row.projectName,
          fileName: row.uploadFilename || row.fileName
        });
      }
    });

    return revisions;
  }

  private reactivateFolders = (data) => {
    this.dialog.open(ReactivateFoldersComponent, {
      data: {data}
    }).afterClosed().subscribe((res) => {
      // refresh listing on close of success deactivate modal
      if(res === 'refresh-listing') {
        this.updateFileListing({ item: this.treeSelection });
      }
    });
  };




  deleteFileCallback = (res) => {
    if(res.status !== 200) {
      return;
    }

    // Toast Message While deleting file
    const successFileRevisionsCount = res.body.selectedFileCount - res.body.failedRevisionsList.length;

    if(successFileRevisionsCount !== 0) {
      this.updateSelectedRowData(true);
    }

    if(res.body.failedRevisionsList.length !== 0) {
      this.deleteFileFailureCallback(res.body.failedRevisionsList);
    }
  }

  private deleteFileFailureCallback = (failedRevisionsList) => {
    for(let i=0;i<failedRevisionsList[0].columns.length;i++){ // BLDBTRCDE-8345 : To disable sorting in not deleted file listing.
      failedRevisionsList[0].columns[i].isSortSupported = false;
    };
    this.dialog.open(CommonModalComponent, {
      data: {
        data: failedRevisionsList,
        deleteFailure: true,
        editable: this.activeListing.list.editable,
        listingType: this.activeListing.list.listingType
      }
    });
  }

  /**
   * Checks if the selected file is eligible for DocuSign e-signature.
   * The file must:
   * - DocuSign integration enabled
   * - Not be a placeholder document
   * - Be the latest and active revision
   * - Not be activity locked or password protected
   * - Have a supported file extension
   * - Not be a linked file
   * @private
   * @param selectedFile The file object to check
   * @returns {boolean} True if the file supports DocuSign, false otherwise
   */
  private hasSupportForDocusign(selectedFile): boolean {
    return (this.selectedFolderPermissionData.enableDocusignIntegration ? true : false) &&
      selectedFile.documentTypeId != DOC_TYPE_ID.PLACEHOLDER &&
      selectedFile.isLatest &&
      selectedFile.isActive &&
      !selectedFile.isActivityLocked &&
      !selectedFile.passwordProtected &&
      this.docuSignAllowedExtensions.includes(selectedFile.fileName.split('.')[1]) &&
      !selectedFile.isLink;
  }

  /**
   * Initiates the DocuSign e-signing process for the selected file.
   * Sends a request to the backend to get the DocuSign signing URL and opens it in a new tab.
   * Shows an error notification if the process fails.
   * @private
   * @param selectedFile The file object to initiate signing for
   */
  private initiateSigningProcess(selectedFile, isForViewSigning?){
    if(!selectedFile){
      return;
    }      
    // Pre-open the window immediately to prevent popup blocking
    const newWindow = window.open('about:blank', '_blank');
    if (!newWindow) {
        return; // Popup blocked
    }
    let payload = {
      action_id: AppConstant.INITIATE_DOCUSIGN_E_SIGNING,
      projectId: selectedFile.projectId,
      revisionId: selectedFile.revisionId,
      folderId: selectedFile.folderId,
      fileName: selectedFile.uploadFilename || selectedFile.fileName,
      fileSizeInBytes: selectedFile.fileSizeInBytes,
      docusignAction: isForViewSigning ? 'VIEW_SIGNING' : 'INITIATE_SIGNING'
    }
    const actionText = isForViewSigning ? 'viewing e-signing document. Please try again.' : 'initiating e-signing process. Please try again.';
    this.util.ajax({
      url: ApiConstant.HOME_CONTROLLER,
      method: 'POST',
      data: payload,
      responseType:'text',
      _dcId:selectedFile.dcId,
      success: (response) => {
        if (!response.body) {
          newWindow.close();
          return;
        }
        if (!newWindow.closed) {
            newWindow.location.href = response.body;
        } else {
            window.open(response.body, '_blank');
        }
      },
      error: (err) => {
        newWindow.close();
        this.util.notification.error({
          theClass: 'notification-sm',
          title: 'Error',
          msg: `An error occurred while ${actionText}`
        });
        return;
      },
      offline: (err) => {
        newWindow.close();
        this.util.notification.error({
          theClass: 'notification-sm',
          title: 'Error',
          msg: `An error occurred while ${actionText}`
        });
        return;
      }
    });
  }

  private hasViewerNotSupportedFileSelected = (selectedRows) => {
    return selectedRows.some((row) => !row.hasHoopsViewerSupport && !row.has3DRepoViewerSupport);
  }

  private hasOnly3DRepoViewerSupportedFileSelected = (selectedRows) => {
    return selectedRows.every((row) => row.has3DRepoViewerSupport);
  }

  private hasOnlyHoopsViewerSupportedFileSelected = (selectedRows) => {
    return selectedRows.every((row) => row.hasHoopsViewerSupport);
  }

  private convertKBtoMB(fileSizeInKB) {
    var fileSizeInMB = fileSizeInKB / 1024;
    return fileSizeInMB;
  }

  handleFolderClick(e: KeyboardEvent){
    HtmlOperationService.setActiveItemClass(this.uploadDropDownBtn?.nativeElement);
    this.folderUpload.nativeElement?.click()
  }
}
