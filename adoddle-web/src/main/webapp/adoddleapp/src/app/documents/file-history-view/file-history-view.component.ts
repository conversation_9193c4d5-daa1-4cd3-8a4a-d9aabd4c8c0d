import { <PERSON>mpo<PERSON>, HostL<PERSON>ener, Input, OnInit, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef  } from '@angular/core';
import { ApiConstant } from 'app/shared/api-constant';
import { AppConstant } from 'app/shared/app-constant';
import { CommonUtilService } from 'app/shared/common-util.service';
import { <PERSON>b<PERSON><PERSON>picker, NgbDropdown } from '@ng-bootstrap/ng-bootstrap';
import { DISTRIBUTION_LEVEL } from 'app/shared/actions.enum';
import { RetentionEntityTypeId } from 'app/admin/record-retention-policy/retention-policy.enum';
import { ListingApiService } from 'app/shared/listing/listing-api.service';
import { CommonViewActionService } from 'app/shared/common-view/common-view-action-container/common-view-action.service';
import { FileViewActionService } from 'app/shared/file-view-action/file-view-action.service';
import { CommonViewAction } from 'app/shared/common-view/common-view-action-container/common-view-action-container.enum';
import { Subscription } from 'rxjs';
import { FileHistoryPipe } from './file-history.pipe';

@Component({
  selector: 'adoddle-file-history-view',
  templateUrl: './file-history-view.component.html',
  styleUrl: './file-history-view.component.scss'
})

export class FileHistoryViewComponent implements OnInit, DoCheck {

  PREFIX_DISTRIBUTION_LEVEL = {
      "1": "(R)",
      "2": "(O)",
      "3": "(U)",
      "4": "(UG)",
      "5": "(D)"
  };

  @Input('data') fileData: any;
  @Input('configData') configData: any;

  public getHistoryDetailsXHR: Subscription;
  public clearActionXHR: any;
  public exportXHR: any = false;
  public distDataUserListXHR: any;
  public delegateActionXHR: any;
  public reactiveDeactivateXHR: any;

  @ViewChild('datePicker') datePicker: NgbDatepicker;
  @ViewChild('datePickerBody') datePickerBody: NgbDatepicker;

  privilege: string;

  history = {
    canAccessDeactivatedDocs: false,
    canDownloadFile: false,
    canViewHistoricMarkups: false,
    canViewSignatories: false,
    expanded: false
  }

  typeMap = {
    signatories: "27",
    distribution: "11",
    revisions: "1",
    status: "6",
    migration: "75",
    all: ""
  };

  search = {
    actionId: this.typeMap.revisions,
    revisionId: "",
    text: ""
  };

  sortingDetails = {
    field: 'actionDate',
    order: true,
    userDateFormat: null
  }

  historyList: [];

  signatureHistoryList = [];
  
  distActionList = [];
  isMobile = false;
  isForAdoddlePlugin = false;
  revList = [];
  checkAllDist = false;
  delegateHeaderDate: any;
  distTypeWiseUserList = [];
  lastSelectedItem = null;

  minDate = {
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    day: new Date().getDate()
  };

  delegateDetails = {
    active: false,
    dueType: null,
    user: [],
    userList: {},
    incompleteDistAction: [],
    totalDocs: 0
  };

  moreOption = {
    deactive: false,
    reactive: false,
    clearAction: false,
    delegateAction: false
  };

  isDistSelected: boolean = false;
  distributionUrl: string = ApiConstant.COMMUNICATIONS_CONTROLLER;
  isActionIdChanged: boolean = false;
  isRevisionIdChanged: boolean = false;

  /**
  * Holds Integration dropdown instance
  * @type {NgbDropdown}
  * @memberof CreateCommentComponent
  */
  @ViewChild('integrationDrop') integrationDrop: NgbDropdown;

  /**
   * Flag to enable/disable distribute btn
   * @type {boolean}
   * @memberof FileHistoryViewComponent
   */
  isDelegateValidFlag: boolean = false;

  /**
   * Hold historyCallback function for 1st time load scenario.
   * @private
   * @memberof FileHistoryViewComponent
   */
  private _historyCallback: any;

  /**
   * @description Flag to display loader while fetch dist type user list
   * @type {boolean}
   * @memberof FileHistoryViewComponent
   */
  _isLoadingDistTypeUserList: boolean = false;

  constructor(
    private util: CommonUtilService,
    private listingApiService: ListingApiService,
    private fileViewActionService: FileViewActionService,
    private commonViewActionService: CommonViewActionService,
    private _changeRef: ChangeDetectorRef,
    private historyPipe: FileHistoryPipe
  ) { 
  }
  
  @HostListener('document:click', ['$event'])
  clickout(event) {
    let className = ['rec-object', 'dist-object-name'];
    if(!className.includes(event.target.className)) {
      let filterOpenedPopupdist = this.distActionList.filter((data) => data.isOpenUserList);
      if(filterOpenedPopupdist.length) {
        filterOpenedPopupdist[0].isOpenUserList = false;
      }
    }
  }
  
  ngOnInit(): void {
    this.commonViewActionService.isFullScreen.subscribe(isFullScreen => {
      this.history.expanded = isFullScreen;
    });

    this.commonViewActionService.actionList.subscribe(data => {
      if (!data || data instanceof Array) return;
      this.refresh()
    });

    this.sortingDetails.userDateFormat = this.configData.userDateFormat;
    this.isMobile = this.util.isMobile();

    if(this.isMobile) {
      this.history.expanded = true;
    }

    this.util.getProjectPermission(this.fileData.projectId, this.fileData.dcId, (data) => {
        this.privilege = data.privileges;
				this.history.canAccessDeactivatedDocs = this.util.hasAccess(this.privilege, 'PRIV_ACCESS_DEACTIVATED_DOCUMENTS');
				this.history.canViewHistoricMarkups = this.util.hasAccess(this.privilege, 'CAN_VIEW_HISTORIC_MARKUPS');
				this.history.canDownloadFile = this.util.hasAccess(this.privilege, "PRIV_CAN_DOWNLOAD_DOCUMENTS") && (this.configData.userFolderPermission != AppConstant.FOLDER_VIEW_ONLY);
    });
    this.history.canViewSignatories = this.configData.enableDocusignIntegration;

    this.resetSortingField();
    this.deselectAllDistItems();
    this.getHistoryDetails();
  }

  ngDoCheck(): void {
    this.isDelegateValidFlag = this.isDelegateValid();
  }


  setDate(data) {
    this.delegateHeaderDate = data;
  }

  resetSortingField = function(){
    this.orderByField = 'actionDate';
    this.sortOrder = true;
  };

  getHistoryDetails = function(isFromRefresh?: boolean) {
    let url = ApiConstant.DOCUMENT_CONTROLLER;
    let param = {
      action_id: AppConstant.VIEW_FILE_HISTORY,
      documentId: this.configData.documentId,
      projectId: this.configData.projectId,
      folderId: this.configData.folderId,
      historyType: -1
    };
    
    if(this.history.canViewSignatories) {
      param['enableDocusignIntegration'] = this.history.canViewSignatories;
    }

    let revStr = this.revList?.map((data) => data.revisionId).toString();
    if(revStr) {
      param['revisionId'] = revStr;
    }

    if(this.getHistoryDetailsXHR) {
      this.getHistoryDetailsXHR.unsubscribe();
    }
    
    this.getHistoryDetailsXHR = this.util.ajax({
      url: url,
      data: param,
      method: 'POST',
      _dcId:  this.configData.dcId,
      success: (response) => {
        this.distActionList = [];
        if(!response.body) {
            this._util.notification.error({
                theClass: 'notification-sm',
                msg: this._util.lang.get('error-while-processing-your-request')
            });
        }
        else {
          this.historyList = response.body.historyVoList;

          if(response.body.revisionList) {
            this.revList = response.body.revisionList?.elementVOList || [];
          }

          if(response.body.signatureVOlist) {
            this.signatureHistoryList = response.body.signatureVOlist || [];
          }

          this.setDistActionList(isFromRefresh);
          this.getHistoryDetailsXHR = false;
          this._historyCallback?.();
        }
      },
      error: (err) => {
          this.getHistoryDetailsXHR = false;
          this._util.notification.error({
              theClass: 'notification-sm',
              msg: this._util.lang.get('error-while-processing-your-request')
          });
      }
    });
  };

  getDistTypeWiseUserList(event, action) {
    this.distTypeWiseUserList = [];
    let filterOpenedPopupdist = this.distActionList.filter((data) => data.isOpenUserList);
    if(filterOpenedPopupdist.length) {
      filterOpenedPopupdist[0].isOpenUserList = false;
    }

    if(action.prefix == this.PREFIX_DISTRIBUTION_LEVEL["3"]){
      action.isOpenUserList = false;
      return;
    }
    action.isOpenUserList = !action.isOpenUserList;

    this._isLoadingDistTypeUserList = true;
    
    this.util.ajax({
      url: ApiConstant.GET_DIST_WISE_USER_LIST + "?entityType=" +  action.distributionLevel + "&distributionLevelId=" + action.distributionLevelId + "&projectID=" + this.configData.projectId, ///manageAdoddleObjectPrivacy/getUserListForGivenResource,
      method: 'GET',
      success: (response) => {
        let responseData = response.body;
        this._isLoadingDistTypeUserList = false;
        for(let data of responseData){
          data.userImg = ApiConstant.IMAGE_PATH + data.userID.toString().split('$$')[0];
          data.userImg += '&v=' + this.util.getLastModifiedProfileTime(data.userImageName);
        }
        this.distTypeWiseUserList = responseData;
      },
      error: (err) => {	
        this._isLoadingDistTypeUserList = false;
      }
    });
  }

  setDistActionList(isFromRefresh?: boolean) {
    for(let index in this.historyList){
      let rowData: any = this.historyList[index];
      rowData.proxy = JSON.parse(JSON.stringify({
        userID: rowData.userID,
        hProxyUserId: rowData.hProxyUserId,
        proxyUserName: rowData.proxyUserName,
        proxyOrgName: rowData.proxyOrgName,
        userTypeId: rowData.userTypeId
      }));

      rowData.username = rowData.username ? rowData.username : rowData.fname + ' ' + (rowData.lname || '');

      if(rowData.userImage){					
        let plainUserID = rowData.userID.split("$$")[0],
          plainProxyID = rowData.hProxyUserId.split("$$")[0],
          currUserImgID = (plainUserID != plainProxyID ? plainUserID : plainProxyID)
          rowData.userImage =  rowData.userImage.replace(plainProxyID, currUserImgID);
      }

      rowData.userPhoto = ApiConstant.IMAGE_PATH + rowData.userID.split('$$')[0];
      rowData.userPhoto += '&v=' + this.util.getLastModifiedProfileTime(rowData.userImage);

      if(rowData.actions) {
        for(let actionIndex in rowData.actions) {
          let actionData = rowData.actions[actionIndex];
          let preFixDistName = actionData.distributionLevel ? this.PREFIX_DISTRIBUTION_LEVEL[actionData.distributionLevel] + " : " : "";
          actionData.revisionId = rowData.revisionId;
          actionData.actionDate = rowData.actionDate;
          actionData.item = rowData;
          actionData.userID = rowData.userID;
          actionData.prefix = this.PREFIX_DISTRIBUTION_LEVEL[actionData.distributionLevel];
          actionData.hProxyUserId = rowData.hProxyUserId;
          actionData.userImage = rowData.userImage;
          actionData.userTypeId = rowData.userTypeId;
          actionData.username = rowData.username;
          actionData.orgName = rowData.orgName;
          actionData.user_org_id = parseInt(actionData.user_org_id);
          actionData.actionId = this.typeMap.distribution;
          actionData.revisionNum = rowData.revisionNum;
          actionData.revisionCounter = rowData.revisionCounter;
          actionData.proxy = rowData.proxy;
          actionData.userPhoto = rowData.userPhoto;
          actionData.recipientPhoto = ApiConstant.IMAGE_PATH + actionData.recipient_user_id.split('$$')[0];
          actionData.recipientPhoto += '&v=' + this.util.getLastModifiedProfileTime(actionData.user_image);
          actionData.objectName = (preFixDistName + actionData.objectName) || "";
          actionData.isOpenUserList = false;
          actionData.checked = false;
          this.distActionList.push(actionData);        
        }
      }
    }
    if(isFromRefresh) {
      this._historyCallback = undefined;
      return;
    }
    let configHistoryType = this.configData.historyType;
    if(configHistoryType) {
      if(!this.isRevisionIdChanged) {
        this.search.revisionId = this.configData.latestRevId;
      }
      if(!this.isActionIdChanged) {
        this.search.actionId = configHistoryType.toString() == "-1" ? "" : configHistoryType;
      }
    }else if(this.distActionList.length) {
      this.search.actionId = this.typeMap.distribution; 
    }
  };

  filterDistActionList = () => {
    return this.historyPipe.transform(this.distActionList, this.search);
  }

  selectAllDistribution(event) {
    this.lastSelectedItem = 0;

    const filteredItems = this.filterDistActionList();
    filteredItems.forEach((data) => {
        data.checked = event.target.checked;
    });
    this.isDistSelected = this.checkForDistSelection();
  }

  selectDistItem(event, distAction, index) {
    if(event.shiftKey) {
      let startIndex , endIndex;

      if(index < this.lastSelectedItem) {
        startIndex = index;
        endIndex = this.lastSelectedItem;
      } else {
        startIndex = this.lastSelectedItem;
        endIndex = index;
      }

      for(let i in this.distActionList) {
        this.distActionList[i].checked = (i >= startIndex && i <= endIndex);
      }
    } 
    else {
      distAction.checked = event.target.checked;
      this.lastSelectedItem = index;
    }
    let filteredDist = this.distActionList.filter((data) => data.checked);
    this.checkAllDist = filteredDist.length == this.distActionList.length;
    this.isDistSelected = this.checkForDistSelection();
  }

  checkForDistSelection() {
    const filteredItems = this.filterDistActionList();
    let filteredDist = filteredItems.filter((data) => data.checked);
    return !!filteredDist.length;
  }

  deselectAllDistItems() {
    this.distActionList.forEach((data) => data.checked = false);
    this.checkAllDist = false;
    this.clearActionXHR = false;
    this.delegateActionXHR = false;
    this.reactiveDeactivateXHR = false;
    this.isDistSelected = false;
  }

  typeChange(currentRevisionId?: any) {
    if(currentRevisionId) {
      this.search.revisionId = currentRevisionId;
      this.isRevisionIdChanged = true;
    } else {
      this.isActionIdChanged = true;
    }
    this.cancelDelegate();
    this.deselectAllDistItems();
  }

  moreOptionClick() {
    this.moreOption = {
      deactive: false,
      reactive: false,
      clearAction: false,
      delegateAction: false
    };

    let selectedDistribution = this.getSelectedDistributionList();

    if(selectedDistribution.length == 0) {
      return false;
    }

    //Deactive Action
    let deactiveParamObj = {
      "ownOrg": this.util.hasAccess(this.privilege, 'CAN_DEACTIVATE_DOCUMENT_ACTIONS_OWN_ORG'), 
      "allOrg": this.util.hasAccess(this.privilege, 'CAN_DEACTIVATE_DOCUMENT_ACTIONS_ALL_ORG'), 
      "status": "Inactive", 
      "invert": true
    };
    this.moreOption.deactive = this.hasDeactiveReactivatePermission(deactiveParamObj);

    //Reactivate Action
    let reactiveParamObj = {
      "ownOrg": this.util.hasAccess(this.privilege, 'CAN_REACTIVATE_DOCUMENT_ACTIONS_OWN_ORG'), 
      "allOrg": this.util.hasAccess(this.privilege, 'CAN_REACTIVATE_DOCUMENT_ACTIONS_ALL_ORG'), 
      "status": "Inactive"
    };
    this.moreOption.reactive = this.hasDeactiveReactivatePermission(reactiveParamObj);
  
    //Clear Action
    let clearParamObj = {
      "ownOrg" : this.util.hasAccess(this.privilege, 'PRIV_CAN_CLEAR_ACTIONS_OWN'),
      "projOrg": this.util.hasAccess(this.privilege, 'PRIV_CAN_CLEAR_ACTIONS_PROJECT'),
      "allOrg" : this.util.hasAccess(this.privilege, 'PRIV_CAN_CLEAR_ACTIONS_ORG'),
      "status": "Incomplete"
    };
    this.moreOption.clearAction = this.hasDelegateAndClearPermission(clearParamObj);

    //Delegate Action
    let delegateParamObj = {
      "ownOrg" : this.util.hasAccess(this.privilege, 'PRIV_CAN_DELEGATE_ACTIONS_OWN'),
      "projOrg": this.util.hasAccess(this.privilege, 'PRIV_CAN_DELEGATE_ACTIONS_PROJECT'),
      "allOrg" : this.util.hasAccess(this.privilege, 'PRIV_CAN_DELEGATE_ACTIONS_ORG'),
      "status": "Incomplete"
    };
    this.moreOption.delegateAction = this.hasDelegateAndClearPermission(delegateParamObj);
  }

  hasDelegateAndClearPermission(paramObj: any) {
    let hasPermission = false;
    
    // Get selected distribution list with Incomplete status..
    let incompleteStatusData = this.distActionList.filter(data => data.checked && data.action_status == paramObj.status);

    if(incompleteStatusData.length > 0) {
      for(let index in incompleteStatusData) {
        let data = incompleteStatusData[index];
        let plainUserId = (<any>window).USP.userID;

        if(paramObj.ownOrg && plainUserId && data.recipient_user_id == plainUserId){
          hasPermission = true;
          break;
        }
        //Can Clear/Delegate Actions for all users (except own) across all organisation of the Project
        if(paramObj.projOrg  && data.recipient_user_id != plainUserId){
          hasPermission = true;
          break;
        }
        // Can Clear/Delegate Actions for all users (except own) of the login organisation only
        if(paramObj.allOrg && (data.user_org_id == (<any>window).USP.orgID ) && data.recipient_user_id != plainUserId){
          hasPermission = true;
          break;
        }
      }
    }

    return hasPermission;
  }

  hasDeactiveReactivatePermission(paramObj) {
    let selectedDistList = this.getSelectedDistributionList();
    let hasPermission = false;

    for(let index in selectedDistList) {
      let data = selectedDistList[index];

      let condition = data.action_status == paramObj.status;
      if (paramObj.invert) { // "invert = true" for Deactivation
          condition = !condition
      }
      
      if(condition){
        //Can Deactivate/Reactivate All/Own Org Actions.
        if(paramObj.allOrg || (paramObj.ownOrg && data.user_org_id == (<any>window).USP.orgID)){
          hasPermission = true;
          break;
        }
      }
    }

    return hasPermission;
  }

  getSelectedDistributionList() {
    return this.distActionList.filter(data => data.checked);
  }

  headerCellClick(event, field) {

    if(this.sortingDetails.field == field){
      this.sortingDetails.order = !this.sortingDetails.order;
    } else {
      this.sortingDetails.field = field;
      this.sortingDetails.order = false;
    }
  }

  clearAction() {
    if(!this.getSelectedDistributionList().length) {
      return;
    }  
    let distributionIds = this.getDistributionIds();

    this.clearActionXHR = this.util.ajax({
      url: ApiConstant.DOCUMENT_CONTROLLER,
      data: {
        action_id : AppConstant.DOCUMENT_DISTRIBUTION_CLEAR_ACTION_LIST, 
				drId : distributionIds, 
				project_id : this.configData.projectId
      },
      method: 'POST',
      _dcId:  this.configData.dcId,
      _cdnUrl: (<any>window).TabServices && (<any>window).getTabServiceUrl((<any>window).TabServices.FILES),
      success: (response) => {
        let totalSelectedRecord = this.getSelectedDistributionList().length;
        let responseData = response.body.data;
        let distributionListIds = [];
        let listActionData = [];
        let visibilityDistListId = [];
        if(responseData && responseData.length > 0){
          for(let data of responseData){
            let distId = data.revisionDistListId + "|" + data.recipient_user_id.split('$$')[0] + "|" + data.actionId;
            if(distributionIds.includes(distId)) {
              distributionListIds.push(distId);
            }
            if(data.askForRemoveVisibility) {
              visibilityDistListId.push(data.revisionDistListId);
            }
          };

          if(visibilityDistListId.length){	
						listActionData = this.getSelectedDistributionList().filter((val) => {
              return visibilityDistListId.some((data) => val.dist_list_id.split("$$")[0] == data.split("$$")[0]);
						});
					}

          if(distributionListIds.length) {
            if(listActionData.length > 0) {
              this.util.showRemoveVisibilityConfirmation(
                () => this.confirmClearAction(distributionListIds.join(), totalSelectedRecord, listActionData),
                () => this.deselectAllDistItems()
              );
            } else {
              this.confirmClearAction(distributionListIds.join(), totalSelectedRecord, listActionData);
            }
          }
					this.clearActionXHR = false;
        } else {
					this.deselectAllDistItems();
					this.util.notification.warn({msg:'<div class="bold-msg">Task(s) cleared for 0 / ' + totalSelectedRecord + ' record(s).<br>'
					 + totalSelectedRecord + ' record(s) were not cleared due to one/all of the reasons mentioned below: <br>'
					 + ' a) You do not have privilege to clear task on selected record <br>'
					 + ' b) Task for selected record was already completed</div>'});
        }
      },
      error: (err) => {
        this.deselectAllDistItems();
      }
    });
  }

  confirmClearAction(distributionListIds, totalSelectedRecords, listData) {
    let incompleteActions = this.distActionList.filter(data => data.checked && data.action_status == 'Incomplete');
    let params: any = {
      "action_id": AppConstant.DOCUMENT_DISTRIBUTION_CLEAR_ACTION_SUBMIT,
      "UserSessionProfile.proxyOrgName": incompleteActions[0].proxyUserOrg,
      "UserSessionProfile.proxyUserName": incompleteActions[0].proxyUserName,
      "UserSessionProfile.tpdOrgName": incompleteActions[0].user_org_name,
      "UserSessionProfile.tpdUserName": incompleteActions[0].user_name,
      "docRevisionId": incompleteActions[0].revisionId,
      "drReportId": this.getDistributionIds(incompleteActions, true),
      "project_id": this.configData.projectId,
      "drId": distributionListIds, //getIdString($scope.filterList), //getIdString(actions),
      "isFromRemoveUser": 'null',
      "isForAllUsers": 'null'
    };			

    if (listData && listData.length > 0) {
      let visibilityData = this.returnVisibilityData(listData);
      if (visibilityData.length) {
        params.visibilityVOList = JSON.stringify(visibilityData);
      }
    }

    this.clearActionXHR = this.util.ajax({
      url: ApiConstant.DOCUMENT_CONTROLLER,
      data: params,
      method: 'POST',
      responseType: 'text',
      success: (response) => {
        let updatedRecordsCnt = distributionListIds.split(",").length;
				this.deselectAllDistItems();
				this.getHistoryDetails();

        setTimeout(() => {
          let updateDetail = {};
					updateDetail[RetentionEntityTypeId.FILE] = incompleteActions[0].revisionId;
					this.util.informParentWin(updateDetail);
        }, 2000);

				if(updatedRecordsCnt < totalSelectedRecords){
					this.util.notification.warn({msg: '<div class="bold-msg">Task(s) cleared for ' + updatedRecordsCnt + ' / ' + totalSelectedRecords + ' record(s).<br>'
					 + (totalSelectedRecords - updatedRecordsCnt) + ' record(s) were not cleared due to one/all of the below reasons: <br>'
					 + ' a) You do not have privilege to clear task on selected record <br>'
					 + ' b) Task for selected record was already completed</div>'});
				} else {
					this.util.notification.success({msg:'<div class="bold-msg text-center">Task(s) cleared for ' + updatedRecordsCnt + ' / ' + totalSelectedRecords + ' record(s)</div>'});
				}
        this.clearActionXHR = false;
      },
      error: (err) => {
        this.deselectAllDistItems();
      }
    });
  }

  /**
		 * Return visibility object
		 */
  returnVisibilityData(actionData) {
    let visibilityData = [];
    let uniqueKeyMap = {};
    for(let i in actionData) {
      let data = actionData[i];
      if(data.distributionLevel != DISTRIBUTION_LEVEL.USERS){
        let permissionLevelId = data.distributionLevelId;
        if(data.distributionLevel == DISTRIBUTION_LEVEL.DISTRIBUTION_GROUPS){
          permissionLevelId = data.distributionLevelId?.split("$$")[0];
        }
        if(Object.keys(uniqueKeyMap).length == 0 || !uniqueKeyMap[data.revisionId + "#" + data.distributionLevelId]) {
          let newJson = {
            "hProjectId": this.configData.projectId,
            "resourceTypeId": 2,
            "hResourceId": data.revisionId,
            "adoddleObjectPermissionVOList": [{
              "hPermissionLevelId": permissionLevelId,
              "hashLevelId": data.distributionLevelId,
              "permissionLevel": data.distributionLevel
            }]
          };
          uniqueKeyMap[data.revisionId + "#" + data.distributionLevelId] = newJson;
          visibilityData.push(newJson);
        }
      }			
    }
    return visibilityData;
  };

  getDistributionIds(actions?: any, isForReport?: any) {
    let selectedDistList = actions ?? this.getSelectedDistributionList();
    let distributionIds = '';

    if(!isForReport) {
      distributionIds = selectedDistList.map(data => data.dist_list_id + "|" + data.user_id + "|" + data.action_id).toString();
    } else {
      distributionIds = selectedDistList.map(data => data.dist_list_id + "|" + data.action_id + "|" + data.user_id + "|" + data.revisionId).toString();
    }
    return distributionIds;
  }

  selectDelegateDetails() {
    this.delegateDetails = {
      active: true,
      dueType: 0,
      user: [],
      userList: {},
      incompleteDistAction: [],
      totalDocs: 0
    };
    this.getDistDataUserList();
  }

  getDistDataUserList() {
    this.distDataUserListXHR = this.util.ajax({
      url: ApiConstant.DOCUMENT_CONTROLLER,
      data: {
        action_id : AppConstant.DOCUMENT_DISTRIBUTION_DELEGATE_ACTION_LIST,
        projectId: this.configData.projectId,
        drId: this.getDistributionIds()
      },
      method: 'POST',
      _dcId:  this.configData.dcId,
      success: (response) => {
        let totalSelectedRecords = this.getSelectedDistributionList().length;
        let responseData = response.body.data;

        let validDistList = responseData[0] || [];
        let userList = responseData[1] || [];
				if(validDistList.length && userList.length) {
						for(let i in userList) {
							let user = userList[i];
							user.emailId = user.email;
							user.userName = user.username;
						}			
					this.delegateDetails.userList = { userList: userList };
          this.delegateDetails.incompleteDistAction = validDistList;
          this.delegateDetails.totalDocs = response.body.totalDocs;
				} else {
					this.util.notification.warn({msg: '<div class="bold-msg">Task(s) delegated for 0 / ' + totalSelectedRecords + ' record(s).<br>'
					 + totalSelectedRecords + ' record(s) were not delegated due to one/all of the below reasons: <br>'
					 + ' a) You do not have privilege to delegate task on selected record <br>'
					 + ' b) Task for selected record was already delegated</div>'});
					// $scope.delegate.validUsersList = []
					this.cancelDelegate();
					this.deselectAllDistItems();
				}
        this.distDataUserListXHR = false;
      },
      error: (err) => {
        this.deselectAllDistItems();
      }
    });
  }

  cancelDelegate() {
    this.delegateDetails = {
      active: false,
      dueType: 0,
      user: [],
      userList: {},
      incompleteDistAction: [],
      totalDocs: 0
    };
    this.clearDueDates();
  }

  clearDueDates() {
    let selectedDistList = this.getSelectedDistributionList();

    if(selectedDistList.length) {
      for(let action of this.distActionList) {
        action.duedate = null;
      }
    }
  }

  delegateAction() {
    if(!this.isDelegateValid()) {
      return;
    }

    let user = this.delegateDetails.user[0].item;
    let incompleteActions = this.distActionList.filter(data => data.checked && data.action_status == 'Incomplete');
    let validActions = [];

    for(let distData of this.delegateDetails.incompleteDistAction) {
      for(let action of incompleteActions){
        if(action.dist_list_id.split("$$")[0] == distData.revisionDistListId.split("$$")[0]
          && action.action_id == distData.actionId
          && action.user_id == distData.recipient_user_id.split("$$")[0]) {
            let newJson = {
              proxyUserOrg: action.proxyUserOrg,
              proxyUserName: action.proxyUserName,
              user_org_name: action.user_org_name,
              user_name: action.user_name,
              revisionId: distData.revisionId,
              recipient_user_id: distData.recipient_user_id,
              folder_id: distData.folder_id,
              revisionDistListId: distData.revisionDistListId,
              actionId: distData.actionId,
              recipientFullActioName: distData.recipientFullActioName,
              dist_list_id: action.dist_list_id,
              user_id: action.user_id,
              action_id: action.action_id
            }
            if(this.delegateDetails.dueType == 2) {
              newJson['duedate'] = action.duedate;
            }
            validActions.push(newJson);
        }
      }
    }

    let param = {
      action_id: AppConstant.DOCUMENT_DISTRIBUTION_DELEGATE_ACTION_SUBMIT,
      project_id: this.configData.projectId,
      drId: this.getDistributionIds(validActions),
      drReportId: this.getDistributionIds(validActions, true),
      distlistcount : validActions.length,
      "UserSessionProfile.proxyOrgName": validActions[0].proxyUserOrg,
      "UserSessionProfile.proxyUserName": validActions[0].proxyUserName,
      "UserSessionProfile.tpdOrgName": validActions[0].user_org_name,
      "UserSessionProfile.tpdUserName": validActions[0].user_name,
      actionDueDateType: this.delegateDetails.dueType,
      selected_user_type: user.user_type,
      selected_username: user.username,
      userId: user.userID,
      selected_orgname: user.orgName,
      orgId: user.hashedOrgID,
      isFromRemoveUser : 'null',
    };
    
    if(validActions.length) {
      for(let i in validActions) {
        let index = parseInt(i) + 1;
        let obj = validActions[i];
        param['revision-id' + index] = obj.revisionId;
        param['recipient-user-id' + index] = obj.recipient_user_id;
        param['folder-id' + index] = obj.folder_id;
        param['revision-dist-list-id' + index] = obj.revisionDistListId;
        param['delegated' + index] = false;
        param['action-id' + index] = obj.actionId;
        param['action-name' + index] = obj.recipientFullActioName;
        if(this.delegateDetails.dueType == 2) {
          param['NewActionDueDate' + index] = obj.duedate;
        }
      }
    }

    this.delegateActionXHR = this.util.ajax({
      url: ApiConstant.DOCUMENT_CONTROLLER,
      data: param,
      method: 'POST',
      _dcId:  this.configData.dcId,
      success: (data) => {
        let response = data.body;
        this.cancelDelegate();
        this.deselectAllDistItems();
        this.getHistoryDetails();

        setTimeout(() => {
          let updateDetail = {};
					updateDetail[RetentionEntityTypeId.FILE] = validActions[0].revisionId;
					this.util.informParentWin(updateDetail);
        }, 2000);

				if(response.notDelegated > 0 || response.notDelegatedForStatusChange > 0)  {
					this.util.notification.warn({msg: '<div class="bold-msg">Task(s) delegated for ' + response.delegated + ' / ' + incompleteActions.length + ' record(s).<br>'
					+ (response.notDelegated + response.notDelegatedForStatusChange) + ' record(s) were not delegated due to one/all of the reasons mentioned below: <br>'
					+ ' a) You do not have privilege to delegate task on selected record <br>'
					+ ' b) Recipient user does not have privilege to be assigned the selected task<br>'
					+ ' c) Task(s) are either cleared/delegated for selected records <br>'
          + ' d) Same action for the same user cannot be delegated! </div>'});
				} else {
					this.util.notification.success({msg: '<div class="bold-msg text-center">' + validActions.length + ' ' + this.util.lang.get('delegateConfirmMsg') + '</div>'});
				}
        this.delegateActionXHR = false;
      },
      error: (err) => {
        this.deselectAllDistItems();
      }
    });
  }

  isDelegateValid = function() {
    this._changeRef.detectChanges();
    if(!this.delegateDetails.user.length) {
      return false;
    }

    let selectedDistList = this.getSelectedDistributionList();
    
    if(!selectedDistList.length) {
      return false;
    }
    
    if(this.delegateDetails.dueType == 2) {
      let filterData = selectedDistList.filter((data) => data.action_status == "Incomplete" && !data.duedate);

      if(filterData.length) {
        return false;
      }
    }
    return true;
  };

  applyDateToAll() {
    let selectedDistAction = this.getSelectedDistributionList();
    for(let i in selectedDistAction) {
      let currentData = selectedDistAction[i];
      if(currentData.action_status == 'Incomplete') {
        selectedDistAction[i].duedate = this.delegateHeaderDate;
      }
    }
  }

  reactiveDeactiveAction(actionType) {
    let distributionIds = this.getDistributionIds();

    this.reactiveDeactivateXHR = this.util.ajax({
      url: ApiConstant.DOCUMENT_CONTROLLER,
      data: {
        action_id : actionType == 'deactive' ? AppConstant.DEACTIVATE_FILE_ACTION_LIST : AppConstant.REACTIVATE_FILE_ACTION_LIST, 
				drId : distributionIds, 
				project_id : this.configData.projectId
      },
      method: 'POST',
      _dcId:  this.configData.dcId,
      _cdnUrl: (<any>window).TabServices && (<any>window).getTabServiceUrl((<any>window).TabServices.FILES),
      success: (response) => {
        let responseData = response.body.data;
        let distributionListIds = [];
        let listActionData = [];
        let visibilityDistListId = [];
        if(responseData && responseData.length > 0){
          for(let data of responseData){
            let distId = data.revisionDistListId + "|" + data.recipient_user_id.split('$$')[0] + "|" + data.actionId;
            if(distributionIds.includes(distId)) {
              distributionListIds.push(distId);
            }
            if(data.askForRemoveVisibility) {
              visibilityDistListId.push(data.revisionDistListId);
            }
          };

          if(actionType == 'deactive' && visibilityDistListId.length){	
						listActionData = this.getSelectedDistributionList().filter((val) => {
              return visibilityDistListId.some((data) => val.dist_list_id.split("$$")[0] == data.split("$$")[0]);
						});
					}

          if(distributionListIds.length) {
            if(actionType == 'deactive' && listActionData.length > 0) {
              this.util.showRemoveVisibilityConfirmation(
                () => this.confirmDeactiveReactiveAction(actionType, distributionListIds, listActionData),
                () => {
                  this.reactiveDeactivateXHR = false;
					        this.deselectAllDistItems();
                }
              );
            } else {
              this.confirmDeactiveReactiveAction(actionType, distributionListIds);
            }
          }
				} else {
					this.warningMessageForReactiveAndDeactivate(actionType, 0);
				}
        this.reactiveDeactivateXHR = false;
      },
      error: (err) => {
        this.deselectAllDistItems();
      }
    });
  }

  warningMessageForReactiveAndDeactivate(actionType, updatedRecordCount) {
    let totalSelectedRecords = this.getSelectedDistributionList().length;
    if(actionType == 'deactive'){
      this.util.notification.warn({msg: '<div class="bold-msg">Task(s) deactivated for ' + updatedRecordCount + ' / ' + totalSelectedRecords + ' record(s).<br>'
       + totalSelectedRecords + ' record(s) were not deactivated due to one/all of the below reasons: <br>'
       + ' a) You do not have privilege to deactivate task on selected record <br>'
       + ' b) Task for selected record was already deactivated</div>'});
    } else{
      this.util.notification.warn({msg: '<div class="bold-msg">Task(s) reactivated for ' + updatedRecordCount + ' / ' + totalSelectedRecords + ' record(s).<br>'
       + totalSelectedRecords + ' record(s) were not reactivated due to one/all of the below reasons: <br>'
       + ' a) You do not have privilege to reactivate task on selected record <br>'
       + ' b) Task for selected record was already reactivated</div>'});
    }
  }

  confirmDeactiveReactiveAction(actionType, distributionListIds, listData?) {
    let selectedDistList = this.getSelectedDistributionList();
    let totalSelectedRecords = selectedDistList.length;
    let params = {
      action_id: actionType == 'deactive' ? AppConstant.DEACTIVATE_FILE_ACTION : AppConstant.REACTIVATE_FILE_ACTION,
				project_id: this.configData.projectId,
				drId: distributionListIds.join()
    }

    if (listData && listData.length > 0) {
      let visibilityData = this.returnVisibilityData(listData);
      if (visibilityData.length) {
        params['visibilityVOList'] = JSON.stringify(visibilityData);
      }
    }

    this.reactiveDeactivateXHR = this.util.ajax({
      url: ApiConstant.DOCUMENT_CONTROLLER,
      data: params,
      method: 'POST',
      responseType: 'text',
      success: (response) => {
				let updatedRecordsCnt = distributionListIds.length;
				this.getHistoryDetails();
        setTimeout(() => {
          let updateDetail = {};
					updateDetail[RetentionEntityTypeId.FILE] = selectedDistList[0].revisionId;
					this.util.informParentWin(updateDetail);
        }, 2000);

				if(actionType == 'deactive'){
					//For Deactivate
					if(updatedRecordsCnt < totalSelectedRecords){
						this.warningMessageForReactiveAndDeactivate(actionType, updatedRecordsCnt);
					} else {
						this.util.notification.success({msg: '<div class="bold-msg text-center">Task(s) deactivated for ' + updatedRecordsCnt + ' / ' + totalSelectedRecords + ' record(s)</div>'});
					}
				} else {
					//For Reactivate
					if(updatedRecordsCnt < totalSelectedRecords){
						this.warningMessageForReactiveAndDeactivate(actionType, updatedRecordsCnt);
					} else {
						this.util.notification.success({msg: '<div class="bold-msg text-center">Task(s) reactivated for ' + updatedRecordsCnt + ' / ' + totalSelectedRecords + ' record(s)</div>'});
					}
				}
        this.reactiveDeactivateXHR = false;
      },
      error: (err) => {
        this.reactiveDeactivateXHR = false;
        this.deselectAllDistItems();
      }
    });
  }

  exportHistory() {
    let uniqueName="file_history_export";
    this.exportXHR = true;
    let exportObj = {
      historyType: this.search.actionId || '-1',
      action_id: AppConstant.EXPORT_DOCUMENT_HISTORY,
      controller: ApiConstant.EXPORT_CONTROLLER,
      listingType: AppConstant.DOCUMENT_HISTORY_EXPORT,
      projectId: this.configData.projectId,
      folderId: this.configData.folderId,
      docTypeId: this.configData.docTypeId,
      revisionId: this.revList.map((data) => data.revisionId).toString(),
      latestRevId: this.configData.latestRevId
    };
    
    if(this.configData.applicationId == 2){
      (<any>window).qtObject?.slot_exportClicked(exportObj);
      return;
    }

    let options = {
      url: exportObj.controller,
      uniqueName:uniqueName,
      method: 'post',
      param: exportObj
    };
    this.util.exportRecords(options);
    this.util.getPendingExportStatus(uniqueName);
    setTimeout(() => {
      this.exportXHR = false;
    },100);
  }

  resetToDefault() {
    (<any>window)['resetToDefaultState']();
  }

  refresh() {
    this.cancelDelegate();
    this.deselectAllDistItems();
    this.resetSortingField();
    if(!this.getHistoryDetailsXHR) {
      this.getHistoryDetails(true);
    }else {
      this._historyCallback = () => { this.getHistoryDetails(true); }
    }
  }

  closeHistory() {
    this.commonViewActionService.closeViewAction();
  }

  editShareLink(item) {
    item.isEdit = true;
    item.documentFolderPath = item.documentFolderPath || this.configData.documentFolderPath;
    item.documentFolderPath = decodeURI(item.documentFolderPath).replace(/\+/g, ' ');
    item.uploadFilename = item.uploadFilename || item.fileName || this.configData.fileName;
    item.documentId = item.documentId || this.configData.documentId;
    item.dcId = item.dcId || this.configData.dcId;
    
    let commonViewAction = CommonViewAction;
    let actionParams:any = {
      name: commonViewAction.Share_Link,
      data: {currentRevision : item, isEdit : true},
      commonViewParams: {
        targetBtn: 'file-action-btn',
        ddName: 'share-link',
        dialog: true,
        dockable: true,
        expandable: true,
        autoClose: 'outsideClick',
        appendTo: 'right-content'
      }
    };
    
    this.commonViewActionService.openAction(actionParams);
  }

  openReview(item) {
    this.listingApiService.openReview(item);
  }

  openAttachment(item) {
    let hasOnlineViewerSupport = item.hasOnlineViewerSupportForAttachment;

    if(!hasOnlineViewerSupport && this.configData.viewerPreference == '7') {
      item.hasOnlineViewerSupportForAttachment = this.util.isZip(item.fileName);
    }		
    if(!item.hasOnlineViewerSupportForAttachment) {
      if(this.history.canDownloadFile) {
        this.listingApiService.downloadAttachmentFile(item);
      } else {
        this.util.notification.error({msg: this.util.lang.get("disabled-enabled-download-button")});
      }
      return;
    }
    
    this.listingApiService.openAttachment({
      projectId: item.projectId,
      fileName: item.attachImgName,
      revisionId: item.revisionId,
      isFromFileViewForDomain: true,
      commingFrom: "internalAttachment",
      attachmentId: item.attachmentId,
      method: 'POST'
    }, item.dcId);
  }

  openRevision(data) {
    let revData = this.revList.filter((val) => val.revisionId == data.revisionId);
    
    let param = {
          projectId: revData[0].projectId,
          revisionId: revData[0].revisionId,
          documentId: revData[0].documentId,
          folderId: revData[0].folderId,
          hasOnlineViewerSupport: revData[0].hasOnlineViewerSupport, //
          toOpen: "FromFile",
          dcId: revData[0].dcId,
          documentTypeId: revData[0].documentTypeId,
          viewerId: revData[0].viewerId,
          isFromFileViewForDomain: "true",
          isActive: revData[0].isActive
      };

    if (this.configData.applicationId) {
      param['applicationId'] = this.configData.applicationId;
    }

    this.util.submitForm({
        target: '_FILE_VIEWER_' + param.revisionId,
        method: 'GET',
        url: ApiConstant.FILE_VIEW_PAGE,
        param: param,
        _dcId: data.dcId
    });
  }

  loadLegacyMarkup(name, revId, projectId) {
    let markup_revisionId = revId.split("$$")[0];
    let markup_projectId = projectId.split("$$")[0];

    if(this.configData.applicationId == 2 && (<any>window).qtObject){
      let currentTime = new Date().getTime();
      let qtFileName = name + '_' + currentTime + '.pdf';
      let qtURL = window.location.origin + ApiConstant.PUBLISH_PDF_CONTROLLER + '?markupName='+ encodeURIComponent(name) + '&lm_projectId='+markup_projectId + '&lm_revisionId=' + markup_revisionId + '&action_id=' + AppConstant.GENERATE_LEGACY_MARKUP;
      (<any>window).qtObject.loadLegacyMarkup(qtURL, qtFileName);
      return;
    }

    if(((<any>window)['asiteFieldAppIds'] || [3]).includes(this.configData.applicationId)){
      let param ={
        markupName : name,
        fileName : this.configData.fileName,
        lm_projectId : markup_projectId,
        lm_revisionId : markup_revisionId,
        action_id : AppConstant.GENERATE_LEGACY_MARKUP
      }
      this.util.sendEventToIpad("legacyMarkup:" + JSON.stringify(param));
        return;
    }

    this.util.submitForm({
      url: ApiConstant.PUBLISH_PDF_CONTROLLER,
      param: {
        markupName : name,
        lm_projectId : markup_projectId,
        lm_revisionId : markup_revisionId,
        action_id : AppConstant.GENERATE_LEGACY_MARKUP
      },
      target: '_blank'
    });
  };

  openTransmittalsDetails(data) {
    if(data) {
      data.projectId = this.configData.projectId
    }
    this.listingApiService.openTransmittalsView(data, 'fileHistory')
  }
}