<div class="ibox-header">
	<h1>{{ 'history' | translate }}</h1>
	<div class="header-action" [ngClass]="{'mobile': isMobile}">
		<span class="small loading" *ngIf="clearActionXHR || delegateActionXHR || reactiveDeactivateXHR"></span>
		<input type="text" *ngIf="!isMobile" [(ngModel)]="search.text" placeholder="{{'search' | translate}}" class="search-inpt" [attr.aria-label]="'search' | translate" />
		<select class="type-select actionSelection" *ngIf="!isMobile" [(ngModel)]="search.actionId" (change)="typeChange()" [attr.aria-label]="('filter' | translate) + ' ' + ('history' | translate)">
			<option value="11">{{'distribution' | translate}}</option>
			<option value="1">{{'revision' | translate}}</option>
			<option value="6">{{'status' | translate}}</option>
			<option value="">{{'access' | translate}}</option>
			<option value="75" *ngIf="history.canViewHistoricMarkups">{{'historic-markups' | translate}}</option> <!-- *Migration. -->
			<option value="typeMap.signatories" *ngIf="history.canViewSignatories">{{'signatories' | translate}}</option>
		</select>
		<select class="rev-select" *ngIf="!isMobile" [(ngModel)]="search.revisionId" [hidden]="revList.length < 2" (change)="typeChange($event.target.value)" [attr.aria-label]="('filter' | translate) + ' ' + ('revisions' | translate)">
			<option value="">{{'all' | translate}}</option>
			<option *ngFor="let rev of revList" [value]="rev.revisionId">{{rev.revisionNum}} : {{rev.publishDate.split("#")[0]}}</option>
		</select>
		<span class="dist-history-action" *ngIf="search.actionId == '11'" ngbDropdown [placement]="['bottom-right']" #integrationDrop="ngbDropdown">
			<button type="button" id="more-options"  [disabled]="!isDistSelected" ngbDropdownToggle class="btn btn-default btn-xs dropdown-toggle common-ibox-header-btn" [title]="'more-options' | translate" (click)="$event.stopPropagation();moreOptionClick()">
				<i class="fa fa-ellipsis-v"></i>
			</button>
			<ul ngbDropdownMenu [attr.aria-label]="'history' | translate" class="dropdown-menu history-dist-dropdown dropdown-menu-right drp-integration">
				<li class="clear-action" ngbDropdownItem [class.disabled]="!moreOption.clearAction" (click)="clearAction()">
					<a adoddleHandleEnterKeydown tabindex="0">{{'clear-actions' | translate}}</a>
				</li>
				<li class="delegate-action" ngbDropdownItem [class.disabled]="!moreOption.delegateAction" (click)="selectDelegateDetails()">
					<a adoddleHandleEnterKeydown tabindex="0">{{'delegate-actions' | translate}}</a>
				</li>
				<li class="deactivate-action" ngbDropdownItem [class.disabled]="!moreOption.deactive" (click)="reactiveDeactiveAction('deactive')">
					<a adoddleHandleEnterKeydown tabindex="0">{{'deactivate-actions' | translate}}</a>
				</li>
				<li class="reactivate-action" ngbDropdownItem [class.disabled]="!moreOption.reactive" (click)="reactiveDeactiveAction('reactive')">
					<a adoddleHandleEnterKeydown tabindex="0">{{'reactivate-actions' | translate}}</a>
				</li>
			</ul>
		</span>
		<div class="excel-export" [ngClass]="{'disabled': exportXHR}">
			<button type="button" id="file-history-btn" class="btn btn-default btn-xs button-reload export-results common-ibox-header-btn" [class.disabled]="exportXHR"
				*ngIf="!isMobile" title="{{(exportXHR ? 'please-wait-files-processd-while' : 'export-results') | translate}}" (click)="exportHistory($event)">
				<i class="fa fa-file-excel-o"></i>
				<span class="small aLoader" [ngClass]="{'hide': !exportXHR}"></span>
			</button>
		</div>
		<button *ngIf="isForAdoddlePlugin" class="btn btn-default reset-to-default-for-issue common-ibox-header-btn" (click)="resetToDefault()" title="{{'reset-visibility' | translate}}">
			<i class="fa fa-refresh"></i>
		</button>
		<button type="button" class="btn btn-default btn-xs button-reload refresh common-ibox-header-btn" [class.disabled]="getHistoryDetailsXHR" title="{{'click-to-refresh-view' | translate}}" (click)="refresh()">
			<i class="fa fa-rotate-right"></i>
		</button>
	
		<adoddle-common-header-action-buttons *ngIf="!isMobile"></adoddle-common-header-action-buttons>
	
		<button class="btn btn-default btn-xs close-link ng-scope common-ibox-header-btn" title="{{'close' | translate}}" (click)="closeHistory()">
			<i class="fa fa-times"></i>
		</button>
	</div>
</div>

<div class="history-container history-panel" [ngClass]="{
    'loading': getHistoryDetailsXHR,
    'delegate-action-table': !delegateDetails.active,
	'ibox-body': !delegateDetails.active
  }">

  	<div class="delegate-controls" *ngIf="delegateDetails.active" [ngClass]="distDataUserListXHR ? 'loading' : ''">
		<div class="dist-select" *ngIf="!distDataUserListXHR">
			<adoddle-dist-select
				[projectId]="fileData.projectId"
				[dcId]="fileData.dcId"
				[noGroupExpand]="true"
				[allowDuplicate]="false"
				[hideEmailNotify]="false"
				[hideActions]="true"
				[hideDueDate]="true"
				[maxSelection]="1"
				[distData]="delegateDetails.userList"
				[(selected)]="delegateDetails.user"
				[controller]="distributionUrl"
				[validationActionId]="140">
			  </adoddle-dist-select>
		</div>
		<div class="due-date-wrapper">
			<label>
				<input type="radio" name="dueType" [value]="0" [(ngModel)]="delegateDetails.dueType"/> 
				{{'existing-due-date' | translate}}
			</label>
			<label>
				<input type="radio" name="dueType" [value]="1" [(ngModel)]="delegateDetails.dueType"/>
				{{'re-calculate-days' | translate}}
			</label>
			<label>
				<input type="radio" name="dueType" [value]="2" [(ngModel)]="delegateDetails.dueType"/>
				{{'user-definition' | translate}}
			</label>
		</div>
		<div class="form-group text-right">
			<button class="btn btn-inverse btn-tertiary" type="button" (click)="cancelDelegate()">{{'close' | translate}}</button>
     	   <button class="btn btn-danger delegate-button" type="button" (click)="delegateAction()" [disabled]="!isDelegateValidFlag || delegateActionXHR">{{(delegateActionXHR ? 'submitting...' : 'delegate-action') | translate}}</button>
    	</div>
	</div>

	<div class="table-grid" *ngIf="!getHistoryDetailsXHR && (distActionList.length > 0 || historyList.length > 0 || signatureHistoryList.length > 0)">
		<!-- Distribution History -->
		<div *ngIf="search.actionId == '11'; else otherThanDistribution">
			<ng-container *ngIf="(distActionList | fileHistoryFilter: search | orderBy: sortingDetails) as filteredDistList">
				<div *ngIf="filteredDistList.length > 0">
					<div class="ghead">
						<ul class="grow dist">
							<li class="gcheck"><input type="checkbox" [disabled]="delegateDetails.active" [(ngModel)]="checkAllDist" (click)="selectAllDistribution($event)" [attr.aria-label]="'distribution' | translate" /></li>
							<li class="gldate" *ngIf="delegateDetails.active && delegateDetails.dueType == 2">
								<div style="width: 128px; float: left; height: 28px;">
									<adoddle-date-picker [minDate]="minDate" (dateChange)="setDate($event)" [placement]="['bottom-left', 'bottom-right']" [openOnFocus]="true"></adoddle-date-picker>
								</div>
								<i class="fa fa-arrow-down" (click)="applyDateToAll()"></i>
							</li>
							<li class="gint sorting" *ngIf="history.expanded"
								title="{{'email-notified' | translate}} - {{(sortingDetails.field != 'instant_email_notif' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'instant_email_notif');">
								<i class="fa fa-envelope"></i>
								<i [hidden]="sortingDetails.field != 'instant_email_notif'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gint sorting revisionNum"
								title="{{'rev' | translate}} - {{(sortingDetails.field != 'revisionNum' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'revisionNum');">
								{{ 'rev' | translate }}
								<i [hidden]="sortingDetails.field != 'revisionNum'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gissueno sorting" *ngIf="history.expanded"
								title="{{'ver' | translate}} - {{(sortingDetails.field != 'revisionCounter' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'revisionCounter');">
								{{ 'ver' | translate }}
								<i [hidden]="sortingDetails.field != 'revisionCounter'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gpuser sorting" *ngIf="history.expanded"
								title="{{'recipient-object' | translate}} - {{(sortingDetails.field != 'objectName' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'objectName');">
								{{ 'recipient-object' | translate }}
								<i [hidden]="sortingDetails.field != 'objectName'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gpuser recipient-user sorting"
								title="{{'recipient-user' | translate}} - {{(sortingDetails.field != 'user_name' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'user_name');">
								{{ 'recipient-user' | translate }}
								<i [hidden]="sortingDetails.field != 'user_name'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gaction sorting"
								title="{{'recipient-action' | translate}} - {{(sortingDetails.field != 'action_name' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'action_name');">
								{{ 'recipient-action' | translate }}
								<i [hidden]="sortingDetails.field != 'action_name'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gastatus sorting"
								title="{{'action-status' | translate}} - {{(sortingDetails.field != 'action_status' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'action_status');">
								{{ 'action-status' | translate}}
								<i [hidden]="sortingDetails.field != 'action_status'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gpuser sorting" *ngIf="history.expanded"
								title="{{'assigned-by' | translate}} - {{(sortingDetails.field != 'username' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'username');">
								{{'assigned-by' | translate}}
								<i [hidden]="sortingDetails.field != 'username'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gdate sorting" *ngIf="history.expanded"
								title="{{'assigned-date' | translate}} - {{(sortingDetails.field != 'actionDate' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'actionDate');">
								{{ 'assigned-date' | translate}}
								<i [hidden]="sortingDetails.field != 'actionDate'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gdate sorting"
								title="{{'due-date' | translate}} - {{(sortingDetails.field != 'action_due_by_date' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'action_due_by_date');">
								{{ 'due-date' | translate }}
								<i [hidden]="sortingDetails.field != 'action_due_by_date'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gfname" *ngIf="history.expanded">{{'action-notes' | translate}}</li>
							<li class="gstatus sorting" *ngIf="history.expanded"
								title="{{'action-complete' | translate}} - {{(sortingDetails.field != 'action_complete_date' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'action_complete_date');">
								{{ 'action-complete' | translate }}
								<i [hidden]="sortingDetails.field != 'action_complete_date'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gdate sorting" *ngIf="history.expanded"
								title="{{'viewed' | translate}} - {{(sortingDetails.field != 'view_date' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'view_date');">
								{{ 'viewed' | translate }}
								<i [hidden]="sortingDetails.field != 'view_date'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gdate">{{'transmittal-no' | translate}}</li>
							<li class="gremarks" *ngIf="( history.expanded || isForAdoddlePlugin )">{{'remarks' | translate}}</li>
						</ul>
					</div>
					<div class="gbody">
						<ul class="grow dist" *ngFor="let distAction of filteredDistList; index as i" [class.italic]="distAction.action_status == 'Inactive'">
							<li class="gcheck">
								<input type="checkbox" class="distributionBodyCheckbox" [disabled]="delegateDetails.active" [(ngModel)]="distAction.checked" [ngModelOptions]="{standalone: true}" checked="{{distAction.checked}}" (click)="selectDistItem($event, distAction, i)" [attr.aria-label]="'distribution' | translate"/>
							</li>
							<li class="gldate date" *ngIf="delegateDetails.active && delegateDetails.dueType == 2">
								<adoddle-date-picker *ngIf="distAction.checked && distAction.action_status == 'Incomplete'" 
									[date]="distAction.duedate" [(ngModel)]="distAction.duedate" [minDate]="minDate" (dateChange)="distAction.duedate = $event" [placement]="['bottom-left', 'bottom-right', 'top-left']" [openOnFocus]="true">
								</adoddle-date-picker>
							</li>
							<li class="gint" *ngIf="history.expanded" title="{{distAction.instant_email_notif}}">{{distAction.instant_email_notif}}</li>
							<li class="gint" title="{{distAction.revisionNum}}">{{distAction.revisionNum}}</li>
							<li class="gissueno" *ngIf="history.expanded" title="{{distAction.revisionCounter}}">{{distAction.revisionCounter}}</li>
							<li class="gpuser gDistListNew" *ngIf="history.expanded" (click)="getDistTypeWiseUserList($event,distAction)" title="{{distAction.objectName}}">
								<div class="dist-object-name" [class.distCursor]="distAction.distributionLevel != 3">
									<span class="rec-object" *ngIf="distAction.prefix != '(U)'"><a class="rec-object" adoddleHandleEnterKeydown tabindex="0">{{distAction.objectName}}</a></span>
									<span class="rec-object" *ngIf="distAction.prefix == '(U)'">{{distAction.objectName}}</span>
								</div>
								<div class="dist-type-userlist" (click)="$event.stopPropagation()" *ngIf="distAction.isOpenUserList">
									<span class="small loading" *ngIf="_isLoadingDistTypeUserList"></span>
									<span class="empty-option" *ngIf="!_isLoadingDistTypeUserList && !distTypeWiseUserList.length">{{'empty' | translate}}</span>
									<ul *ngIf="distTypeWiseUserList.length">
										<li *ngFor="let distTypeList of distTypeWiseUserList">
											<img alt="" height="24" width="24" class="img-circle" [src]="distTypeList?.userImg"
												onerror="this.onerror=null;this.src='images/avatar.png'"/>
											<span [title]="distTypeList?.username">{{distTypeList?.username}}</span>
										</li>
									</ul>
								</div>
							</li>
							<li class="gpuser" proxy="{{distAction.item.proxy}}" [attr.user-id]="distAction.recipient_user_id">
								<img alt="" height="36" width="36" class="img-circle" src="{{distAction.recipientPhoto}}" onerror="this.onerror=null;this.src='images/avatar.png'" />
								<a adoddleHandleEnterKeydown tabindex="0">{{distAction.user_name}}<br />{{distAction.user_org_name}}</a>
							</li>
							<li class="gaction" title="{{distAction.action_name}}">{{distAction.action_name}}</li>
							<li class="gastatus" title="{{distAction.action_status == 'Complete' ? distAction.action_complete_date.replace('#',', ') : ''}}">{{distAction.action_status}}</li>
							<li class="gpuser" *ngIf="history.expanded" proxy="{{distAction.item.proxy}}" attr.user-id="{{(distAction.userID.split('$$')[0] != distAction.hProxyUserId.split('$$')[0]) ? distAction.userID : distAction.hProxyUserId}}">
								<img alt="" height="36" width="36" class="img-circle" src="{{distAction.userPhoto}}" onerror="this.onerror=null;this.src='images/avatar.png'" />						
								<span *ngIf="distAction.userTypeId == 2 && distAction.hProxyUserId.split('$$')[0] != 0 && distAction.userID.split('$$')[0] != distAction.hProxyUserId.split('$$')[0]">
									<i class="fa fa-user-o" aria-hidden="true" [attr.user-id]="distAction.hProxyUserId"></i>
									<a adoddleHandleEnterKeydown tabindex="0">
										{{distAction.username}}<br /> {{distAction.orgName}}
									</a>
								</span>
								<span *ngIf="distAction.userTypeId != 2 && distAction.hProxyUserId.split('$$')[0] != 0 && distAction.userID.split('$$')[0] != distAction.hProxyUserId.split('$$')[0]">
									<i class="fa fa-user" aria-hidden="true" [attr.user-id]="distAction.hProxyUserId"></i>
									<a adoddleHandleEnterKeydown tabindex="0">
										{{distAction.username}} <br /> {{distAction.orgName}}
									</a>
								</span>
								<a *ngIf="!(distAction.hProxyUserId.split('$$')[0] != 0 && distAction.userID.split('$$')[0] != distAction.hProxyUserId.split('$$')[0])" adoddleHandleEnterKeydown tabindex="0">{{distAction.username}}<br />{{distAction.orgName}}</a>
							</li>
							<li class="gdate" *ngIf="history.expanded" title="{{distAction.actionDate?.split('#')[1]?.split(' ')[0]?.split(':')[0]}}:{{distAction.actionDate?.split('#')[1]?.split(' ')[0]?.split(':')[1]}}{{' '}}{{distAction.actionDate?.split('#')[1]?.split(' ')[1]}}">{{distAction.actionDate?.split('#')[0]}}</li>
							<li class="gdate" title="{{distAction.action_due_by_date?.split('#')[1]}}">{{distAction.action_due_by_date?.split('#')[0] || ''}}</li>
							<li class="gfname" *ngIf="history.expanded" title="{{distAction.action_notes}}">{{distAction.action_notes}}</li>
							<li class="gstatus" *ngIf="history.expanded" title="{{distAction.action_complete_date?.split('#')[1]}}">{{distAction.action_complete_date?.split('#')[0]}}</li>
							<li class="gdate" *ngIf="history.expanded" title="{{distAction.view_date?.split('#')[1]}}">{{distAction.view_date?.split('#')[0]}}</li>
							<li class="gdate" title="{{distAction.transmittal_no}}" (click)="openTransmittalsDetails(distAction)"><a adoddleHandleEnterKeydown tabindex="0">{{distAction.transmittal_no}}</a></li>
							<li class="gremarks" *ngIf="( history.expanded || isForAdoddlePlugin )" title="{{distAction.remarks}}">{{distAction.remarks}}</li>
						</ul>
					</div>
				</div>
				<p *ngIf="filteredDistList.length == 0" class="no-record">{{ 'no-records-available' | translate }}</p>
			</ng-container>
		</div>

		<!-- Other than Distribution History -->
		<ng-template #otherThanDistribution>
			<ng-container *ngIf="(historyList | fileHistoryFilter: search | orderBy: sortingDetails) as filteredHistoryList">
				<!-- Revision History -->
				<ng-container *ngIf="search.actionId == '1' && filteredHistoryList.length > 0">
					<div class="ghead">
						<ul class="grow revision">
							<li class="gfname revfname sorting"
								title="{{'fileName' | translate}} - {{(sortingDetails.field != 'fileName' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'fileName');">
								{{'fileName' | translate}}
								<i [hidden]="sortingDetails.field != 'fileName'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gint sorting"
								title="{{'rev' | translate}} - {{(sortingDetails.field != 'revisionNum' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'revisionNum');">
								{{'rev' | translate}}
								<i [hidden]="sortingDetails.field != 'revisionNum'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gissueno sorting" *ngIf="history.expanded"
								title="{{'ver' | translate}} - {{(sortingDetails.field != 'revisionCounter' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'revisionCounter');">
								{{'ver' | translate}}
								<i [hidden]="sortingDetails.field != 'revisionCounter'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gfname sorting" *ngIf="history.expanded"
								title="{{'title' | translate}} - {{(sortingDetails.field != 'fileDescription' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'fileDescription');">
								{{'title' | translate}}
								<i [hidden]="sortingDetails.field != 'fileDescription'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gpuser sorting"
								title="{{'publishedBy' | translate}} - {{(sortingDetails.field != 'username' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'username');">
								{{'publishedBy' | translate}}
								<i [hidden]="sortingDetails.field != 'username'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gdate sorting"
								title="{{'publishDate' | translate}} - {{(sortingDetails.field != 'actionDate' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'actionDate');">
								{{'publishDate' | translate}}
								<i [hidden]="sortingDetails.field != 'actionDate'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gstatus sorting"
								title="{{'status' | translate}} - {{(sortingDetails.field != 'revisionStatusName' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'revisionStatusName');">
								{{'status' | translate}}
								<i [hidden]="sortingDetails.field != 'revisionStatusName'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gstatus sorting" *ngIf="history.expanded"
								title="{{'purpose_issue_id' | translate}} - {{(sortingDetails.field != 'purposeOfIssue' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'purposeOfIssue');">
								{{'purpose_issue_id' | translate}}
								<i [hidden]="sortingDetails.field != 'purposeOfIssue'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gremarks" *ngIf="( history.expanded || isForAdoddlePlugin )">{{'revisionNotes' | translate}}</li>
						</ul>
					</div>
					<div class="gbody">
						<ul class="grow revision" *ngFor="let item of filteredHistoryList" [class.italic]="!item.isActive">
							<li class="gfname revfname" title="{{item.fileName}}">
								<a *ngIf="item.isActive || history.canAccessDeactivatedDocs" adoddleHandleEnterKeydown tabindex="0" (click)="openRevision(item)">{{item.fileName}}</a>
								<span *ngIf="!item.isActive && !history.canAccessDeactivatedDocs">{{item.fileName}}</span>
							</li>
							<li class="gint" title="{{item.revisionNum}}">{{item.revisionNum}}</li>
							<li class="gissueno" *ngIf="history.expanded" title="{{item.revisionCounter}}">{{item.revisionCounter}}</li>
							<li class="gfname" *ngIf="history.expanded" title="{{item.fileDescription}}">{{item.fileDescription}}</li>
							<li class="gpuser" proxy="{{item.proxy}}" attr.user-id="{{(item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]) ? item.userID : item.hProxyUserId}}">
								<img alt="" height="36" width="36" class="img-circle" src="{{item.userPhoto}}" onerror="this.onerror=null;this.src='images/avatar.png'" />
								<span *ngIf="item.userTypeId == 2 && item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]">
									<i class="fa fa-user-o" aria-hidden="true" [attr.user-id]="item.hProxyUserId"></i>
									<a adoddleHandleEnterKeydown tabindex="0">
										{{item.username}} <br /> {{item.orgName}}
									</a>
								</span>
								<span *ngIf="item.userTypeId != 2 && item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]">
									<i class="fa fa-user" aria-hidden="true" [attr.user-id]="item.hProxyUserId"></i>
									<a adoddleHandleEnterKeydown tabindex="0">
										{{item.username}} <br /> {{item.orgName}}
									</a>
								</span>
								<a *ngIf="!(item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0])" adoddleHandleEnterKeydown tabindex="0">{{item.username}}<br />{{item.orgName}}</a>
							</li>
							<li class="gdate" title="{{item.actionDate.split('#')[1].split(' ')[0].split(':')[0]}}:{{item.actionDate.split('#')[1].split(' ')[0].split(':')[1]}}{{' '}}{{item.actionDate.split('#')[1].split(' ')[1]}}">{{item.actionDate.split('#')[0]}}</li>					
							<li class="gstatus" title="{{item.revisionStatusName}}">{{item.revisionStatusName}}</li>
							<li class="gstatus" *ngIf="history.expanded" title="{{item.purposeOfIssue}}">{{item.purposeOfIssue}}</li>
							<li class="gremarks" *ngIf="( history.expanded || isForAdoddlePlugin )">
								<span>{{item.revisionNotes}}</span>
								<div *ngIf="item.actionId == 7"> 
									<strong> {{'link-type' | translate}}:</strong> {{item.linkType}}
									&nbsp;&nbsp; <strong> {{'project' | translate}}:</strong> {{item.projectName}}
									&nbsp;&nbsp; <strong> {{'link-location' | translate}}:</strong> {{item.linkLocation}}
								</div>
							</li>
						</ul>
					</div>
				</ng-container>

				<!-- Status History -->
				<ng-container *ngIf="(search.actionId == '6' || search.actionId == '5')  && filteredHistoryList.length > 0">
					<div class="ghead">
						<ul class="grow status">
							<li class="gstatus sorting"
								title="{{'new-status' | translate}} - {{(sortingDetails.field != 'statusName' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'statusName');">
								{{'new-status' | translate}}
								<i [hidden]="sortingDetails.field != 'statusName'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gstatus sorting"
								title="{{'old-status' | translate}} - {{(sortingDetails.field != 'oldStatusName' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'oldStatusName');">
								{{'old-status' | translate}}
								<i [hidden]="sortingDetails.field != 'oldStatusName'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gpuser sorting"
								title="{{'changed-by' | translate}} - {{(sortingDetails.field != 'username' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'username');">
								{{'changed-by' | translate}}
								<i [hidden]="sortingDetails.field != 'username'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gldate sorting"
								title="{{'changed-date' | translate}} - {{(sortingDetails.field != 'actionDate' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'actionDate');">
								{{'changed-date' | translate}}
								<i [hidden]="sortingDetails.field != 'actionDate'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gremarksfix" *ngIf="(history.expanded || isForAdoddlePlugin)">{{'remarks' | translate}}</li>
							<li class="gremarks" *ngIf="history.expanded">{{'reason-for-change' | translate}}</li>
						</ul>
					</div>
					<div class="gbody">
						<ul class="grow status" *ngFor="let item of filteredHistoryList">
							<li class="gstatus" title="{{item.statusName}}">{{item.statusName}}</li>
							<li class="gstatus" title="{{item.oldStatusName}}">{{item.oldStatusName}}</li>
							<li class="gpuser" proxy="{{item.proxy}}" attr.user-id="{{(item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]) ? item.userID : item.hProxyUserId}}">
								<img alt="" height="36" width="36" class="img-circle" src="{{item.userPhoto}}" onerror="this.onerror=null;this.src='images/avatar.png'" />
								<span *ngIf="item.userTypeId == 2 && item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]">
									<i class="fa fa-user-o" aria-hidden="true" [attr.user-id]="item.hProxyUserId"></i>
									<a adoddleHandleEnterKeydown tabindex="0">
										{{item.username}}<br /> {{item.orgName}}
									</a>
								</span>
								<span *ngIf="item.userTypeId != 2 && item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]">
									<i class="fa fa-user" aria-hidden="true" [attr.user-id]="item.hProxyUserId"></i>
									<a adoddleHandleEnterKeydown tabindex="0">
										{{item.username}}<br /> {{item.orgName}}
									</a>
								</span>
								<a *ngIf="!(item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0])" adoddleHandleEnterKeydown tabindex="0">{{item.username}}<br />{{item.orgName}}</a>
							</li>
							<li class="gldate" title="{{item.actionDate.split('#')[1].split(' ')[0].split(':')[0]}}:{{item.actionDate.split('#')[1].split(' ')[0].split(':')[1]}}{{' '}}{{item.actionDate.split('#')[1].split(' ')[1]}}">{{item.actionDate.split('#')[0]}}</li>
							<li class="gremarksfix" *ngIf="(history.expanded || isForAdoddlePlugin)" title="{{item.description}}">{{item.description}}</li>
							<li class="gremarks status-text-li" *ngIf="history.expanded">{{item.reasonForStatusChange || item.remark}}</li>
						</ul>
					</div>
				</ng-container>

				<!-- Access History -->
				<ng-container *ngIf="(!search.actionId || search.actionId == '') && filteredHistoryList.length > 0">
					<div class="ghead">
						<ul class="grow access">
							<li class="gpuser sorting"
								title="{{'accessed-by' | translate}} - {{(sortingDetails.field != 'username' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'username');">
								{{'accessed-by' | translate}}
								<i [hidden]="sortingDetails.field != 'username'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gacces-type sorting"
								title="{{'type' | translate}} - {{(sortingDetails.field != 'actionName' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'actionName');">
								{{'type' | translate}}
								<i [hidden]="sortingDetails.field != 'actionName'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gint sorting"
							   
								title="{{'rev' | translate}} - {{(sortingDetails.field != 'revisionNum' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'revisionNum');">
								{{'rev' | translate}}
								<i [hidden]="sortingDetails.field != 'revisionNum'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gissueno sorting" *ngIf="history.expanded"
								title="{{'ver' | translate}} - {{(sortingDetails.field != 'revisionCounter' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'revisionCounter');">
								{{'ver' | translate}}
								<i [hidden]="sortingDetails.field != 'revisionCounter'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gdate sorting"
								title="{{'date' | translate}} - {{(sortingDetails.field != 'actionDate' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'actionDate');">
								{{'date' | translate}}
								<i [hidden]="sortingDetails.field != 'actionDate'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gremarks" *ngIf="( history.expanded || isForAdoddlePlugin )">{{'remarks' | translate}}</li>
						</ul>
					</div>
					<div class="gbody">
						<ul class="grow access" *ngFor="let item of filteredHistoryList">
							<li class="gpuser" proxy="{{item.proxy}}" attr.user-id="{{(item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]) ? item.userID : item.hProxyUserId}}">
								<img alt="" height="36" width="36" class="img-circle" src="{{item.userPhoto}}" onerror="this.onerror=null;this.src='images/avatar.png'" />
								<span *ngIf="item.userTypeId == 2 && item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]">
									<i class="fa fa-user-o" aria-hidden="true" [attr.user-id]="item.hProxyUserId"></i>
									<a adoddleHandleEnterKeydown tabindex="0">
										{{item.username}} <br/> {{item.orgName}}
									</a>
								</span>
								<span *ngIf="item.userTypeId != 2 && item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]">
									<i class="fa fa-user" aria-hidden="true" [attr.user-id]="item.hProxyUserId"></i>
									<a adoddleHandleEnterKeydown tabindex="0">
										{{item.username}} <br/> {{item.orgName}}
									</a>
								</span>
								<a *ngIf="!(item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0])" adoddleHandleEnterKeydown tabindex="0">{{item.username}}<br />{{item.orgName}}</a>
							</li>
							<li class="gacces-type edit-shareLink" title="{{item.actionName}}">{{item.actionName}} 
								<span *ngIf="item.actionId == 7">({{item.linkType}})</span>
								<i *ngIf="item.actionId == 40 && item.isRevShareLinkEditable" tabindex="0" adoddleHandleEnterKeydown (click)="editShareLink(item)" class="edit-share-link fa fa-edit"
								title="{{'edit' | translate}} {{'share-link' | translate}}" ></i>
							</li>
							<li class="gint" title="{{item.revisionNum}}">{{item.revisionNum}}</li>
							<li class="gissueno" *ngIf="history.expanded"  title="{{item.revisionCounter}}">{{item.revisionCounter}}</li>
							<li class="gdate" title="{{item.actionDate.split('#')[1].split(' ')[0].split(':')[0]}}:{{item.actionDate.split('#')[1].split(' ')[0].split(':')[1]}}{{' '}}{{item.actionDate.split('#')[1].split(' ')[1]}}">{{item.actionDate.split('#')[0]}}</li>
							<li class="gremarks status-text-li" *ngIf="(history.expanded || isForAdoddlePlugin)">
								<div class='floatleft textOverflow historyRemark' *ngIf="item.description!=''">
									<span title="{{item.description}}">
										<span *ngIf="item.attachIssueNumber == 1 && item.actionId == 57"> {{'attached-new-external-file' | translate}}</span>
										<span *ngIf="item.attachIssueNumber != 1 && item.actionId == 57"> {{'replaced-external-file-with' | translate}}</span>								
										<span *ngIf="item.description!='' && (item.actionId == 3 && !hasHoopsViewerSupport) && item.actionId != 57 && item.actionId != 85 && (!item.customObjectInstanceId || !item.customObjectInstanceId.split('$$')[0] || item.customObjectInstanceId.split('$$')[0] == 0)">{{item.description}}</span>
										<span *ngIf="item.description!='' && item.actionId == 85 && item.customObjectInstanceId && item.customObjectInstanceId.split('$$')[0] != 0">
											<a (click)="openReview(item)" adoddleHandleEnterKeydown tabindex="0">{{item.description}}</a>
										</span>
										<span *ngIf="item.description!='' && (item.actionId != 3 && item.actionId != 57 && item.actionId != 85)">{{item.description}}</span>
										<span *ngIf="item.remarks!='' && (((item.actionId == 3 || item.actionId == 1) && !hasHoopsViewerSupport) || (item.actionId != 1 && item.actionId != 3))">{{item.remarks}}</span> 
										<span *ngIf="item.actionId == 7"> 
											&nbsp;&nbsp; <strong> {{'project' | translate}}:</strong> {{item.projectName}}
											&nbsp;&nbsp; <strong> {{'link-location' | translate}}:</strong> {{item.linkLocation}}
										</span>
										<a *ngIf="item.actionId == 57" adoddleHandleEnterKeydown tabindex="0" (click)="openAttachment(item)">{{ item.attachImgName }}</a>
									</span>
								</div>
								<div class='status-text' *ngIf="item.reasonForStatusChange"><b>{{'reason-for-change' | translate}}</b>: <span>{{item.reasonForStatusChange}}</span></div>
								<!-- we rely on remarks for files uploaded through plugin & there will be no remarks if file is uploaded via web app -->
								<div class='status-text' *ngIf="item.actionId == 1 && item.description == '' && hasHoopsViewerSupport">
									<span>{{ item.remarks && item.remarks.length > 0  ? item.remarks : 'Upload Type: CDE'}} </span> 
								</div>  
								
								<!-- we rely on remarks for files downloaded through plugin & there will be no remarks if file is downloaded via other apps -->
								<div class='status-text' *ngIf="item.actionId == 3 && hasHoopsViewerSupport">
									<span *ngIf="item.description != ''">{{item.description}}<br/></span>
									<span>{{ item.remarks && item.remarks.length > 0  ? item.remarks : 'Download Type: CDE'}} </span> 
								</div>

								<!-- Show remarks data in history remarks column for Review Read Action (94) and Review Create Action (86) -->
								<div class="status-text" *ngIf="item.actionId == 94 || item.actionId == 86">
									<span *ngIf="item.remarks != ''">{{item.remarks}}</span>
								</div>
							</li>
						</ul>
					</div>
				</ng-container>

				<!-- Historic Markup History -->
				<ng-container *ngIf="search.actionId == '75' && filteredHistoryList.length > 0">
					<div class="ghead">
						<ul class="grow migration">
							<li class="gpuser sorting"
								title="{{'accessed-by' | translate}} - {{(sortingDetails.field != 'username' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'username');">
								{{'accessed-by' | translate}}
								<i [hidden]="sortingDetails.field != 'username'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gacces-type sorting"
								title="{{'type' | translate}} - {{(sortingDetails.field != 'actionName' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'actionName');">
								{{'type' | translate}}
								<i [hidden]="sortingDetails.field != 'actionName'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gint sorting"
								title="{{'rev' | translate}} - {{(sortingDetails.field != 'revisionNum' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'revisionNum');">
								{{'rev' | translate}}
								<i [hidden]="sortingDetails.field != 'revisionNum'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gissueno sorting" *ngIf="history.expanded"
								title="{{'ver' | translate}} - {{(sortingDetails.field != 'revisionCounter' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'revisionCounter');">
								{{'ver' | translate}}
								<i [hidden]="sortingDetails.field != 'revisionCounter'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gdate sorting"
								title="{{'date' | translate}} - {{(sortingDetails.field != 'actionDate' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'actionDate');">
								{{'date' | translate}}
								<i [hidden]="sortingDetails.field != 'actionDate'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gremarks" *ngIf="( history.expanded || isForAdoddlePlugin )">{{'remarks' | translate}}</li>
						</ul>
					</div>
					<div class="gbody">
						<ul class="grow migration" *ngFor="let item of filteredHistoryList">
							<li class="gpuser" proxy="{{item.proxy}}" attr.user-id="{{(item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]) ? item.userID : item.hProxyUserId}}">
								<img alt="" height="36" width="36" class="img-circle" src="{{item.userPhoto}}" onerror="this.onerror=null;this.src='images/avatar.png'" />
								<span *ngIf="item.userTypeId == 2 && item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]">
									<i class="fa fa-user-o" aria-hidden="true" [attr.user-id]="item.hProxyUserId"></i>
									<a adoddleHandleEnterKeydown tabindex="0">
										{{item.username}} <br/> {{item.orgName}}
									</a>
								</span>
								<span *ngIf="item.userTypeId != 2 && item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]">
									<i class="fa fa-user" aria-hidden="true" [attr.user-id]="item.hProxyUserId"></i>
									<a adoddleHandleEnterKeydown tabindex="0">
										{{item.username}} <br/> {{item.orgName}}
									</a>
								</span>
								<a *ngIf="!(item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0])" adoddleHandleEnterKeydown tabindex="0">{{item.username}}<br />{{item.orgName}}</a>
							</li>
							<li class="gacces-type" title="{{item.actionName}}">{{item.actionName}} 
								<span *ngIf="item.actionId == 7">({{item.linkType}})</span>
								<i *ngIf="item.actionId == 40 && item.isRevShareLinkEditable" (click)="editShareLink(item)" class="edit-share-link fa fa-edit"
								title="{{'edit' | translate}} {{'share-link' | translate}}" ></i>
							</li>
							<li class="gint" title="{{item.revisionNum}}">{{item.revisionNum}}</li>
							<li class="gissueno" *ngIf="history.expanded"  title="{{item.revisionCounter}}">{{item.revisionCounter}}</li>
							<li class="gdate" title="{{item.actionDate.split('#')[1].split(' ')[0].split(':')[0]}}:{{item.actionDate.split('#')[1].split(' ')[0].split(':')[1]}}{{' '}}{{item.actionDate.split('#')[1].split(' ')[1]}}">{{item.actionDate.split('#')[0]}}</li>
							<li class="gremarks status-text-li" *ngIf="( history.expanded || isForAdoddlePlugin )">
								<div class='floatleft textOverflow historyRemark' *ngIf="item.description!='' && item.actionId == 75">
									<span *ngIf="item.description.endsWith('_JAVA') || item.description.endsWith('_ThinClient')" title="{{item.description}}">Consolidated markup file: 
										<a adoddleHandleEnterKeydown tabindex="0" (click)="loadLegacyMarkup(item.description,item.revisionId,item.projectId)">{{item.description}}</a>
									</span>
									<span *ngIf="!item.description.endsWith('_JAVA') && !item.description.endsWith('_ThinClient')" title="{{item.description}}">Markup file: 
										<a adoddleHandleEnterKeydown tabindex="0" (click)="loadLegacyMarkup(item.description,item.revisionId,item.projectId)">{{item.description}}</a>
									</span>
								</div>
							</li>
						</ul>
					</div>
				</ng-container>
				<p *ngIf="search.actionId != typeMap.signatories && filteredHistoryList.length < 1" class="no-record">{{ 'no-records-available' | translate }}</p>
			</ng-container>
			<ng-container *ngIf="(signatureHistoryList | fileHistoryFilter: search | orderBy: sortingDetails) as filteredSignList">
				<!-- Signature History -->
				<ng-container *ngIf="search.actionId == typeMap.signatories && filteredSignList.length > 0">
					<div class="ghead">
						<ul class="grow signature">
							<li class="gcheck w-p-3"></li>
							<li class="gpuser sorting"
								title="{{'username' | translate}} - {{(sortingDetails.field != 'userName' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event,'userName');">
								{{'username' | translate}}
								<i [hidden]="sortingDetails.field != 'userName'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gacces-type sorting"
								title="{{'event-type' | translate}} - {{(sortingDetails.field != 'eventType' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'eventType');">
								{{'event-type' | translate}}
								<i [hidden]="sortingDetails.field != 'eventType'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
							<li class="gdate sorting"
								title="{{'event-date' | translate}} - {{(sortingDetails.field != 'eventDate' ? 'clickToSortBy' : (sortingDetails.order == false ? 'sortedAsc' : 'sortedDesc')) | translate}}"
								(click)="headerCellClick($event, 'eventDate');">
								{{'event-date' | translate}}
								<i [hidden]="sortingDetails.field != 'eventDate'" class="fa" [ngClass]="sortingDetails.order ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
							</li>
						</ul>
					</div>
					<div class="gbody">
						<ul class="grow signature" *ngFor="let item of filteredSignList">
							<li class="gcheck w-p-3"></li>
							<li class="gpuser">
								<img alt="" height="36" width="36" class="img-circle" src="{{item.userImg}}" onerror="this.onerror=null;this.src='images/avatar.png'" />
								<span *ngIf="item.userTypeId == 2 && item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]">
									<a adoddleHandleEnterKeydown tabindex="0">
										{{item.userName}} <br/> {{item.userEmail}}
									</a>
								</span>
								<span *ngIf="item.userTypeId != 2 && item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0]">
									<a adoddleHandleEnterKeydown tabindex="0">
										{{item.userName}} <br/> {{item.userEmail}}
									</a>
								</span>
								<a *ngIf="!(item.hProxyUserId.split('$$')[0] != 0 && item.userID.split('$$')[0] != item.hProxyUserId.split('$$')[0])" adoddleHandleEnterKeydown tabindex="0">{{item.userName}}<br />{{item.userEmail}}</a>
							</li>
							<li class="gacces-type" title="{{item.eventType}}">{{item.eventType}}</li>
							<li class="gdate" title="{{item.eventDate.split('#')[0]}}">{{item.eventDate.split('#')[0]}} 
								<br> 
								<span title="{{item.eventDate.split('#')[1]}}" class="event-time">{{item.eventDate.split('#')[1]}}</span>
							</li>
						</ul>
					</div>
				</ng-container>
				<p *ngIf="search.actionId == typeMap.signatories && filteredSignList.length < 1" class="no-record second">{{ 'no-records-available' | translate }}</p>
			</ng-container>
		</ng-template>
    </div>
</div>