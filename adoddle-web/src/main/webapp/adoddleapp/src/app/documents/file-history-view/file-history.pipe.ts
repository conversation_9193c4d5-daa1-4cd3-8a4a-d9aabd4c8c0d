import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'fileHistoryFilter',
  pure: false
})
export class FileHistoryPipe implements PipeTransform {

  //It is use in FileHistoryViewComponent.ts
  transform(history, search) {
    let filterHistory: any[] = [];

    // Filter with action Id.
    if(search.actionId) {
      filterHistory = history.filter((data) => (data.actionId || data.action_id) == Number(search.actionId));
    } else {
      filterHistory = history;
    }

    // Filter with Revision
    if(search.revisionId) {
      filterHistory = filterHistory.filter((data) => data.revisionId == search.revisionId);
    }

    // FIlter with Text
    if(!search.text || search.text == "") {
      return filterHistory;
    }

    let columnData = [];
		switch(search.actionId){
			case "11":
			case "1":	
        columnData = ["instant_email_notif", "revisionNum", "revisionCounter", "user_name", "user_org_name", "orgName", "action_name", "action_status", "username", "actionDate", 
				           "action_due_by_date", "action_complete_date", "view_date", "revisionStatusName", "purposeOfIssue", "statusName", "oldStatusName", "actionName", "fileName", 
				           "fileDescription", "action_notes", "transmittal_no", "remark", "remarks", "revisionNotes", "revisionStatusName", "description"];
			break;			
			case "6":
			case "5":
				columnData = [ "instant_email_notif", "user_name", "user_org_name", "orgName", "action_status", "username", "actionDate", 
				           "action_due_by_date", "action_complete_date", "view_date", "statusName", "oldStatusName","remark", "remarks","description"]
			break;
			default:
				columnData = ["instant_email_notif", "revisionNum",  "revisionCounter", "user_name", "user_org_name", "orgName", "action_name", "action_status", "username", "actionDate","actionName","description", "userName", "eventDate", "eventType"]		
			break;
		}

    let filteredSearchData = [];

    for(let i in filterHistory) {
      let currentHistory: any = filterHistory[i];
      let isSearchDataAvailable = false;
      for(let index in columnData){
        let key: any = columnData[index];
        
        if(currentHistory.hasOwnProperty(key) && currentHistory[key] && typeof currentHistory[key] !== "object") {
          let value = currentHistory[key] + "";
          if(value.toLowerCase().indexOf(search.text.toLowerCase()) > -1) {
            isSearchDataAvailable = true;
            break;
          }
        }
      }
      if(isSearchDataAvailable) {
        filteredSearchData.push(currentHistory);
      }
    }

    return filteredSearchData;
    
  }

}
