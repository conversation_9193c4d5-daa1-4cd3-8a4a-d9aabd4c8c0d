export enum AppConstant {
  // PROJECT PRIVILEGES
  PRIV_MANAGE_WORKSPACE_ROLES_USERS = 9,
  PRIV_MANAGE_PROJECT_ROLES = 10,
  PRIV_ASSIGN_USERS_PRIVILEGES = 11,
  PRIV_VIEW_USER_PRIVILEGES = 12,
  PRIV_MANAGE_NOTICES = 13,
  PRIV_ASSIGN_FORMS_TO_PROJECT = 14,
  PRIV_EDIT_PROJECT_FORM_SETTINGS = 15,
  PRIV_MANAGE_PROJECT_WORKPACKAGES = 16,
  PRIV_MANAGE_PROJECT_DOCUMENT_STATUS = 17,
  PRIV_MANAGE_PROJECT_DRAWING_SERIRES = 18,
  PRIV_CREATE_FOLDER = 19,
  PRIV_AMEND_FOLDER_PERMISSIONS = 20,
  PRIV_ASSIGN_DOCUMENT_ATTRIBUTES = 21,
  PRIV_DEACTIVATE_DOCUMENTS = 22,
  PRIV_DISTRIBUTE_DOCUMENTS = 23,
  PRIV_CREATE_COMMENTS = 24,
  PRIV_CAN_BE_ASSIGNED_ACTION_CHANGE_STATUS = 25,
  PRIV_CAN_BE_ASSIGNED_ACTION_DISTRIBUTE = 26,
  PRIV_CAN_BE_ASSIGNED_ACTION_INC_COMMENTS = 27,
  PRIV_MANAGE_PROJECT_DISTRIUBTIION_GROUPS = 28,
  PRIV_VIEW_REPORTS = 29,
  PRIV_FORM_PROJECT_PRIV_CREATE = 30,
  PRIV_FORM_PROJECT_PRIV_CONTROL = 31,
  PRIV_CREATE_PARENT_FOLDERS = 32,
  PRIV_FORM_PROJECT_PRIV_VIEW = 33,
  PRIV_CHANGE_STATUS = 34,
  PRIV_FORM_NO_ACCESS = 35,
  PRIV_FORM_VIEW_ALL_PRIVATE_FORMS = 36,
  PRIV_PURPOSEOF_DOC_ISSUE = 37,
  PRIV_MANAGE_ORGANIZATION_PLACEHOLDERS = 38,
  PRIV_MANAGE_PROJECT_PLACEHOLDERS = 39,
  PRIV_CAN_CLEAR_ACTIONS_ORG = 40,
  PRIV_CAN_CLEAR_ACTIONS_PROJECT = 41,
  PRIV_CAN_CLEAR_ACTIONS_OWN = 42,
  PRIV_CAN_DELEGATE_ACTIONS_ORG = 43,
  PRIV_CAN_DELEGATE_ACTIONS_PROJECT = 44,
  PRIV_CAN_DELEGATE_ACTIONS_OWN = 45,
  PRIV_CAN_CLEAR_COMMENTS = 46,
  PRIV_ACCESS_DEACTIVATED_DOCUMENTS = 47,
  PRIV_MANAGE_PROJECT_PAPER_DOCUMENTS = 48,
  PRIV_CAN_DEACTIVATE_USERS_FROM_PROJECT = 49,
  PRIV_CAN_ASSIGN_PROXY_USERS = 50,
  VALUE_ALLOW_CUSTOM_DISTRIBUTION_ALL_ORG = 51,
  PRIV_MANAGE_SPACES = 52,
  PRIV_MANAGE_USER_SUBSCRIPTIONS = 58,
  PRIV_MANAGE_PROJECTS_ALL_ORGS = 59,
  PRIV_MANAGE_PROJECT_MAILBOX = 60,
  PRIV_EDIT_PROJECT_DETAILS = 61,
  VALUE_ALLOW_CUSTOM_DISTRIBUTION_OWN_ORG = 62,
  PRIV_CAN_CREATE_FLAT_FEE_BASED_PROJECTS = 63,
  PRIV_CAN_CREATE_SUBSCRIPTION_PROJECTS = 64,
  PRIV_CAN_CONFIGURE_DOCUMENT_NAMING = 67,
  PRIV_CAN_ACESS_AUDIT_INFO = 69,
  PRIV_CAN_CREATE_COMMENT = 70,
  PRIV_ASSIGN_ROLE_MEMBERSHIP_ALL_ORG = 71,
  PRIV_ASSIGN_ROLE_MEMBERSHIP_OWN_ORG = 72,
  PRIV_CAN_SAVE_WORKSPACE_AS_TEMPLATE = 73,
  PRIV_CAN_MANAGE_FORM_STATUSES = 74,
  PRIV_FORM_VIEW_PRIVATE_OWN_ORG = 75,
  PRIV_CAN_MANAGE_USER_PREFERENCES = 76,
  PRIV_CAN_DEACTIVATE_OWN_FORMS = 77,
  PRIV_CAN_DEACTIVATE_ALL_FORMS = 78,
  PRIV_DEACTIVATE_FORMS_NO_ACCESS = 0,
  PRIV_FORM_VIEW_DRAFT_OWN_ORG = 79,
  PRIV_FORM_VIEW_DRAFT_ALL_ORG = 80,
  PRIV_CAN_DOWNLOAD_DOCUMENTS = 81,
  PRIV_CAN_PRINT_DOCUMENTS = 82,
  PRIV_CAN_ACCESS_WORKSPACE_WITHOUT_SUBSCRIPTION = 83,
  PRIV_CAN_ACCESS_WORKSPACE_CALENDAR = 84,
  CAN_ACCESS_DISTRIBUTION_MODULE = 85,
  CAN_ACCESS_DESIGN_WORKFLOW = 86,
  CAN_ACCESS_PREQUALIFICATION_MODULE = 87,
  CAN_ACCESS_BIDDING_MODULE = 88,
  CAN_ACCESS_CONSTRUCTION_MODULE = 89,
  PRIV_CAN_CREATE_DOCUMENT_MANAGER_PWF = 90,
  PRIV_CAN_CREATE_DOCUMENT_MANAGER_PWS = 91,
  PRIV_CAN_CREATE_DOCUMENT_MANAGER_EWS = 92,
  PRIV_CAN_CREATE_DOCUMENT_MANAGER_SWS = 93,
  PRIV_CAN_CREATE_DOCUMENT_MANAGER_TWS = 94,
  PRIV_CAN_CREATE_DOCUMENT_MANAGER_PMW = 95,
  PRIV_CAN_ACCESS_NAVIGATOR = 96,
  PRIV_MANAGE_APPS = 97,
  PRIV_MANAGE_APP_SETTINGS = 98,
  CAN_BATCH_CHANGE_STATUS_OF_OWN_FORM = 99,
  CAN_REOPEN_CLOSED_FORM = 100,
  CAN_ASSIGN_TO_WORKSPACE_WITHOUT_ACCEPTANCE = 101,
  CAN_AMEND_ALL_FOLDER_PERMISSIONS = 102,
  CAN_SHARE_SEARCH_VIEWS = 109,
  CAN_MANAGE_PROJECTS_MODELS = 110,
  CAN_BATCH_CHANGE_STATUS_OF_ALL_FORMS = 111,
  PRIV_CAN_MANAGE_PROJECTS_MODELS_VIEWS = 112,
  PRIV_CAN_CREATE_ADHOC_REPORT = 113,
  PRIV_MANAGE_WORKFLOW_RULES = 114,
  CAN_DEACTIVATE_FORM_ACTIONS_OWN_ORG = 115,
  CAN_DEACTIVATE_FORM_ACTIONS_ALL_ORG = 116,
  CAN_REACTIVATE_FORM_ACTIONS_OWN_ORG = 117,
  CAN_REACTIVATE_FORM_ACTIONS_ALL_ORG = 118,
  CAN_DEACTIVATE_DOCUMENT_ACTIONS_OWN_ORG = 119,
  CAN_DEACTIVATE_DOCUMENT_ACTIONS_ALL_ORG = 120,
  CAN_REACTIVATE_DOCUMENT_ACTIONS_OWN_ORG = 121,
  CAN_REACTIVATE_DOCUMENT_ACTIONS_ALL_ORG = 122,
  CAN_DELETE_RECORDS = 181,
  ENABLE_MICROSOFT_OFFICE = 185,
  CAN_CREATE_USER_AND_ORGS = 123,
  PRIV_CAN_MANAGE_OBJECT_LISTS = 124,
  PRIV_CAN_MANAGE_CONFIGURABLE_ATTRIBUTES = 125,
  PRIV_CAN_MANAGE_PROJECT_TASKS = 144,
  REV_HISTORY_ACTION=149,
  PRIV_CAN_MOVE_DOC = 126,
  CAN_CREATE_eINVOICE = 127,
  MANAGE_PUNCHOUT_CATALOGUE_RESTRICTION = 128,
  CAN_CREATE_PRIVATE_COMMENTS = 129,
  MARK_REVIEW_PRIVATE=172,
  CAN_MANAGE_REVIEW_FLAGS = 173,
  CAN_REOPEN_ALL_CLOSED_FORMS_ADMIN = 130,
  CAN_CONTROLL_LAYOUT_ALL_ORG = 131,
  PRIV_WORKSPACE_NO_ACCESS = 0,
  PRIV_WORKSPACE_ADMIN = 1023,
  CAN_MANAGE_PROJECTS_FIELD = 135,
  CAN_CREATE_NEW_USERS_AND_ORG_OWN_ORG = 133,
  CAN_CREATE_NEW_USERS_AND_ORG_ALL_ORG = 136,
  CAN_MANAGE_ROLE_PRIVILEGES = 155,
  CAN_MANAGE_FORM_PRIVILEGES = 156,
  CAN_PUBLISH_REPORT_TEMPLATES = 55,
  CAN_MANAGE_PROJECT_INVITITATIONS = 157,
  CAN_EDIT_VISIBILITY_OF_OBJECTS = 161,
  CAN_MANAGE_QUALITY_TEST_PLAN = 168,
  CAN_VIEW_QUALITY_TEST_PLAN = 169,
  ADODDLE_CREATE_FOLDER = 7,
  ADODDLE_EDIT_FOLDER = 8,
  ADRIVE_EDITION_ID = 16,
  ADRIVE_DOCUMENT_LISTING = 136,
  ADRIVE_WORKSPACE_LISTING = 137,
  SAVE_USER_EDITION = 227,
  CAN_CREATE_FILES_FROM_DOCUMENT_TEMPLATES = 179,
  PLAYBOOK_FORM_LISTING = 167,
  CAN_CREATE_ASSET_GROUP = 184,
  CAN_CREATE_ISSUE_COMMENT = 167,
  CAN_CREATE_ISSUE = 166,
  CAN_ACCESS_COGNITIVE_CDE = 241,
  CAN_ACCESS_ADMIN_COGNITIVE_CDE = 251,
  PRIV_CAN_SEND_TO_DOCUSIGN = 250,
  
  // Action ids
  LIST_WORKFLOWS = 1,
  LIST_WORKFLOW_MODEL = 4,
  RULE_LIST = 5,
  ORG_RULE_LIST = 17,
  LIST_ACT_PLUGINS = 4,
  ADODDLE_FOLDER_TREE = 2,
  WORKFLOW_PROGRESS = 4,
  ADODDLE_WORKSPACE_TREE = 5,
  ADODDLE_DISTRIBUTE_FILES = 10,
  DISTRIBUTE_FORM = 11,
  ADD_REMOVE_FAVOURITE_FOLDER = 13,
  ADODDLE_WORKSPACE_AND_FOLDER_TREE_LEVEL = 14,
  PUBLISH_TEMPLATE = 1001,
  VIEW_REPORT_CRITERIA = 8004,
  LIST_WORKFLOWS_ORGS = 14,
  ADODDLE_APP_TYPE_WORKSPACE_TREE = 16,
  ADODDLE_APP_TYPE_FORM_TREE = 17,
  COMMIT_DISTRIBUTION = 19,
  FORM_DIST_VALIDATION = 26,
  PREVIOUSLY_FORM_DIST_USER_LIST = 27,
  ADODDLE_LISTING_ACTION = 100,
  COGNITIVE_CDE_LISTING_ACTION = 201,
  SAVE_CONFIGURABLE_COLUMN_ACTION = 102,
  DOWNLOAD_SINGLE_DOCUMENT_ACTION = 103,
  DOWNLOAD_INTERNAL_ATTACHMENT = 197,
  SAVE_USER_SELECTED_DEFAULT_LISTING_VIEW = 105,
  GET_LIST_FOR_DISTRIBUTION = 107,
  VIEW_FORM_ACTION = 114,
  CREATE_TEST_PLAN = 1,
  RENAME_TEST_PLAN = 2,
  SHOW_SUMMARY_DATA = 4,
  VIEW_FORM_MSG_DETAILS_ACTION = 115,
  GET_FILE_ATTRIBUTES_SET = 117,
  DISPLAY_APP_TEMPLATES_LIST = 118,
  DOWNLOAD_BATCH_DOCUMENT_PAGE = 119,
  DOWNLOAD_BATCH_DOCUMENT_ACTION = 120,
  GET_FILE_PREV_REVISION_DETAILS = 122,
  GET_FILE_PREV_REVISION_DETAILS_NEW = 801,
  UPLOAD_FILE_VALIDATION_NEW = 802,
  GET_UPLOAD_FILE_STATUS = 803,
  GET_PROJECT_LIST_POPUP = 1001,
  SAVE_USER_SEL_PROJECTS = 810,
  UPDATE_COMMENT_ACTION = 124,
  DOWNLOAD_TEMP_ZIP_FILE = 125,
  PROGRESS_ZIP_CREATION = 126,
  DISPLAY_PRINT_VIEW_FORM = 132,
  ADD_REMOVE_FAVOURITE_COMMS = 133,
  GET_FILE_NAME_FOR_REVISION = 136,
  GET_UPLOAD_SETTING = 137,
  UPLOAD_FILE_VALIDATION = 138,
  UPLOAD_FILE_RULES_VALIDATION = 139,
  FILE_DIST_VALIDATION = 140,
  VIEW_FILE_DISCUSSIONS = 148,
  VIEW_FILE_HISTORY = 149,
  VIEW_FORM_HISTORY = 150,
  GET_COMMENT_ICON_DETAIL = 155,
  UPLOAD_INTERNAL_ATTACHMENT = 158,
  GET_SELECTED_REV_ATTRIBUTE_VO = 162,
  FOLDER_DIRECT_ACCESS_LINK = 165,
  FORM_DIRECT_LINK = 166,
  ADODDLE_FORM_PERMISSIONS = 167,
  ADODDLE_FORM_SIGNATURE_PERMISSIONS = 350,
  MULTI_DC_DOWNLOAD_BATCH_DOCUMENT_ACTION = 168,
  MOVE_FILES_TO_TEMP_LOCATION = 172,
  UPDATE_AUDIT_TRAIL_FORM_ATTACHMENT = 179,
  APPLET_UPLOAD_PAGE = 176,
  COPY_DISTRIBUTION_FROM_PREVIOUS_DISTRIBUTION = 182,
  COMMIT_UPLOAD_FILES_DETAIL = 184,
  LISTING_TYPE_INLINE_ASSOCIATION_FILES_LISTING = 184,
  DISPLAY_PRINT_VIEW_FORM_ALL = 185,
  GET_PUBLISHED_DOCS_FOR_PALCEHOLDER = 192,
  VIEW_FILE_ATTACHMENT_ASSOCIATION = 192,
  CHECK_FOR_AKAMAI_DOWNLOAD_LIMIT = 193,
  VIEW_FORM_SIGNATURE_HISTORY = 200,
  CHECK_PUBLIC_PASSWORD_LINK= 850,
  ADRIVE_PUBLIC_FOLDER_TREE = 851,
  ADRIVE_PUBLIC_LISTING_ACTION = 852,
  ADRIVE_PARENT_FOLDER_LIST = 853,
  ADRIVE_USER_PROFILE_IMAGE = 855,
  MODEL_UPLOAD_ATTACHMENTS = 856,
  GET_PLACEHOLDER_DATA_FROM_EXCEL = 195,
  DOWNLOAD_PLACEHOLDER_TEMPLATE = 196,
  CHECK_AVAILABILITY_OF_DOWNLOAD_DOCUMENT = 198,
  DOCUMENT_LISTING_FOR_SPECIFIC_DAYS = 203,
  GET_MY_ACCOUNT_DETAILS = 205,
  SAVE_MY_ACCOUNT_DETAILS = 206,
  GET_LANGUAGE_LIST_FROM_PORTAL = 218,
  GET_USER_APPLICATION_PRIVILEGES = 207,
  GET_FORM_TYPE_PRIVILEGES = 210,
  DESCIPLINE_WISE_FILE_LISTING = 223,
  DOCUMENT_COMPLETE_FOR_INFORMATION = 226,
  OFFLINE_GET_MARKUPS_LIST = 227,
  OFFLINE_SAVE_COMMENT = 228,
  OFFLINE_SAVE_DRAFT_COMMENT = 229,
  OFFLINE_GET_MARKUPID_BYNAME = 230,
  GET_SEARCH_PROJECT_LIST_OFFLINE = 221,
  GET_PROJECT_LIST_OFFLINE = 222,
  GET_FOLDER_LIST_OFFLINE = 223,
  GET_SUBFOLDER_LIST_OFFLINE = 224,
  OFFLINE_GET_OBSERVATION_LIST_BY_PLAN_ACTIONID = 225,
  EXPORT_ATTRIBUTE_ACTIONID = 1413,
  EXPORT_ATTRIBUTE_SETS_ACTIONID = 1414,
  CHECK_CAN_DOWNLOAD_FOLDER_LEVEL = 256,
  GET_INCOMPLETE_ACTIONS_COUNT = 301,
  GET_OVERDUE_ACTIONS_COUNT = 302,
  DEACTIVATE_FORM_ACTION_LIST = 316,
  DEACTIVATE_FORM_ACTION = 317,
  REACTIVATE_FORM_ACTION_LIST = 318,
  REACTIVATE_FORM_ACTION = 319,
  GET_TODAY_DUE_ACTIONS_COUNT = 323,
  GET_UNREAD_MESSAGE_COUNT = 301,
  GET_UNREAD_REVIEWS_COUNT = 324,
  PROCUREMENT_GET_SKN_VALIDATION_CALL_OFF = 426,
  SEARCH_ACTION = 501,
  STATUS_CHANGE_ACTION = 501,
  STATUS_CHANGE_SINGLE_SUBMIT = 502,
  DISPLAY_FORM_CHANGE_STATUS = 571,
  SUBMIT_FORM_CHANGE_STATUS = 572,
  FORM_DISTRIBUTION_CLEAR_ACTION_LIST = 573,
  FORM_DIST_CLEAR_ACTION_SUBMIT = 574,
  FORM_DIST_DELEGATE_ACTION_LIST = 575,
  FORM_DIST_DELEGATE_ACTION_SUBMIT = 576,
  CHECK_INVENTOR_FILE_HAS_REQUIRED_ATTACHMENT = 579,
  MODEL_LISTING_ACTION = 601,
  SAVE_MODEL_ACTION = 607,
  SAVE_MODEL_ACTION_LATEST_REV_ZIP = 608,
  MODEL_WORKSET_LIST = 608,
  SIMPLE_MODEL_DOCUMENT_UPLOAD_ACTION = 609,
  GET_ACTIVITY_CENTER = 610,
  CREATE_MODEL_COMMENT = 615,
  SET_UNSET_FAVORITE_MODEL_ACTION = 612,
  GET_RECENT_MODELS_COUNT = 621,
  GET_FAVOURITE_MODELS_COUNT = 622,
  GET_MY_ACTIONS_COUNT = 623,
  GET_VIEWS_OF_MODEL = 625,
  GET_ASSOC_LISTS_MODEL = 627,
  GET_HWF_MODEL_FILE_STATUS = 631,
  VIEW_MODEL_HISTORY = 632,
  INSERT_MODEL_AUDIT_TRAIL= 98,
  GET_USER_SEARCH_FILTERS_COLUMNS = 700,
  DELETE_USER_SEARCH_FILTER = 703,
  GET_USER_SEARCH_FILTERS = 701,
  GET_USER_SAVED_FILTER_DETAILS = 649,
  EDIT_USER_SEARCH_FILTER = 702,
  SAVE_USER_SEARCH_FILTER = 704,
  UNSAVED_USER_SEARCH_FILTER = 705,
  SEARCH_FILTER_DATA = 706,
  GET_USER_FILTER_PREFERENCES = 839,
  GET_SEARCH_FIELD_VALUES = 707,
  SEARCH_DEFAULT_FIELDS_RESULT = 708,
  EXPORT_FILTER_SEARCH_RESULT = 709,
  DOCUMENT_DISTRIBUTION_CLEAR_ACTION_LIST = 716,
  DOCUMENT_DISTRIBUTION_CLEAR_ACTION_SUBMIT = 717,
  DOCUMENT_DISTRIBUTION_DELEGATE_ACTION_LIST = 718,
  DOCUMENT_DISTRIBUTION_DELEGATE_ACTION_SUBMIT = 719,
  UPDATE_PROJECT_FAV_FLAG = 809,
  GET_FOLDER_PERMISSION_VALUE = 818,
  DEACTIVATE_FILE_ACTION_LIST = 823,
  DEACTIVATE_FILE_ACTION = 824,
  REACTIVATE_FILE_ACTION_LIST = 825,
  REACTIVATE_FILE_ACTION = 826,
  INITIATE_CREATE_ORI_MSG = 903,
  INITIATE_CREATE_FWD_MSG = 904,
  INITIATE_CREATE_RES_MSG = 905,
  BATCH_FILES_FOR_COMMENT_INCORPORATION = 952,
  INITIATE_EDIT_FORM_MSG_COMMITED = 953,
  BATCH_FILES_FOR_COMMENT_COORDINATION = 954,
  ACTION_COMMENT_COORDINATION_INVIEW = 955,
  INITIATE_IMPORT_FORM = 959,
  INITIATE_IMPORT_FORM_FOR_EDIT_ORI = 964,
  CHECK_CONCURRENCY_ISSUE = 965,
  CHECK_EDIT_ORI_DRAFT_MESSAGE = 966,
  CHECK_MAX_INSTANCE_CREATION = 967,
  GET_FOLDER_LIST_POPUP = 1002,
  SAVE_ATTACH_EXT_DOC = 1125,
  UPLOAD_CHUNK_FILE = 1203,
  COPY_TEMP_FILE = 1207,
  COPY_TEMP_FILE_ASITEWORKS = 1208,
  COPY_TO_ASITEWORKS_APPLICATION_ID = 16, // Files are being copied from CDE to asiteworks
  COPY_FROM_ASITEWORKS_APPLICATION_ID = 1, //Files are being copied from Asiteworks to CDE
  GET_UPLOADED_CHUNK_DETAILS = 1204,
  DELETE_UPLOADED_TEMP_FILES = 1205,
  DELETE_THUMBNAIL_TEMP_FILES = 1206,
  GET_APP_SUBAPPTYPES = 22,
  GET_FILE_VIEW_ATTRIBUTES_DETAILS = 1305,
  GET_ADRIVE_FILE_VIEW_ATTRIBUTES_DETAILS = 1306,
  DASHBOARD_GET_USERS_TO_SHARE = 1331,
  EXPORT_LISTING_DATA = 1360,
  EXPORT_PROJECT_LISTING_DATA = 1361,
  EXPORT_FORM_LISTING_DATA = 1362,
  EXPORT_ALL_FORMS_TO_PDF = 175,
  GET_CHECKOUT_REV_DETAILS = 1368,
  REV_VALID_FOR_CHECKOUT_ON_RIGHTCLICK = 1369,
  EXPORT_DOCUMENT_HISTORY = 1404,
  VALIDATE_DOC_NAMING_RULE = 1404,
  EXPORT_FORM_HISTORY = 1405,
  EXPORT_FILE_ASSOCIATIONS_LINK_INFO = 1408,
  SAVE_AS_PDF_AUDIT = 1501,
  SAVE_AS_TIFF_AUDIT = 1502,
  BATCH_PRINT_DOCUMENTS = 1504,
  PRINT_ALL_FILES = 1505,
  SAVE_AS_CSF_AUDIT = 1506,
  SAVE_FILE_VIEWER_USER_PREFERENCE_ID = 1507,
  COMPARE_FILES = 1508,
  GENERATE_LEGACY_MARKUP = 1509,
  GENERATE_LEGACY_MARKUP_CONSOLIDATED = 1510,
  RIGHT_CLICK_PRINT_VIEW_FORM_ALL = 1703,
  GET_CONTACTS = 1700,
  UPDATE_FLAG = 1710,
  GET_USER_FLAG = 1711,
  CAN_EXPORT_CONTACTS = 1715,
  GET_EXPORT_ALL_TO_PDF_FLAG = 1737,
  GET_ASSOCIATION_COUNTS = 1720,
  GET_ATTACHMENT_AND_ASSOCIATIONS = 1721,
  GET_USER_SELECTED_COLUMNS = 1722,
  GET_ALL_LISTING_HEADER = 1734,
  SET_SELECTED_VIEW_TYPE = 1726,
  SAVE_COMMENT = 1728,
  SAVE_DRAFT_COMMENT = 1729,
  INSERT_MODEL_VIEW_HISTORY = 642,
  SAVE_ASSOCIATE_DOCS = 1015,
  GET_ASSOCIATE_DOC = 1013,
  SAVE_AUDIT_TRAIL_FOR_DOCUMENT_ASSOCIATION = 1016,
  GET_USERS_MODEL_DIST_LIST = 616,
  DOWNLOAD_MODEL_TILE = 640,
  SET_MODEL_TILE = 639,
  RENAME_MODEL_VIEW = 636,
  GET_VIEW_DATA = 780,
  SAVE_MODEL_VIEW = 630,
  GET_IFC_OBJECT_TYPE = 1004,
  GET_ALL_BIM_LIST = 1007,
  OBJECT_BASIC_SEARCH = 1006,
  GET_OBJECT_PROPERTIES = 1003,
  URI_OBJECT_ADVANCED_SEARCH = 1009,
  ADD_OBJECT_TO_LIST = 1011,
  SAVE_MODEL_LIST = 1005,
  URI_GET_BIM_LIST_DETAIL = 1008,
  UPDATE_BIM_OBJECT_LIST = 1014,
  GET_HOOPS_FILE_MARKUP = 635,
  SAVE_COPIED_FILE_REF_To_ASITEWORKS_DB = 257,

  SAVE_SORT_DETAILS_FOR_USER = 335,
  SET_RECORD_PER_PAGE_FOR_USER = 333,
  DOCUMENT_ASSOCIATED_COMMS_ACTION = 121,
  VIEW_MODEL_DISCUSSIONS = 152,
  PARTIAL_REFRESH = 174,
  LOAD_INVITE_USER_PAGE = 1343,
  INVITE_USERS = 1344,
  CHECK_FOR_INVALID_INVITATIONS =1348,
  INVITE_USERS_STATUS =1345,
  CHECK_FILE_PERMISSION = 5,
  CHECK_FOLDER_PERMISSION = 4,
  BATCH_FILES_FOR_ACKNOWLEDGEMENT = 951,
  BATCH_FILES_FOR_ACTION = 953,
  COMPLETE_FOR_ACKNOWLEDGEMENT_ACTION = 20,
  COMPLETE_FOR_ACTION = 21,

  GET_LATEST_DRAFT_MSGID_FOR_RESPOND_ACTION = 1603,
  CHECK_REV_RESTRICT_FOR_DOWNLOAD = 175,
  CHECKOUT_REV = 1367,
  EXPORT_TEMPLATE = 1384,
  UPDATE_MSG_STATUS = 181,
  BATCH_FILES_FOR_INFORMATION = 1301,
  UNDO_CHECKOUT = 1709,
  GET_FOLDER_SECURITY_DATA = 9,

  // subscription
  FREE_TRIAL_SUBSCRIPTION_PLAN_ID = 15,
  KEY_LITE_SUBSCRIPTION_PLAN_ID = 1,
  KEY_PRO_SUBSCRIPTION_PLAN_ID = 2,
  KEY_BIM_SUBSCRIPTION_PLAN_ID = 3,
  KEY_ENTERPRISE_SUBSCRIPTION_PLAN_ID = 5,

  BID_WINNER_EDITION = 13,
  PLAYBOOK_EDITION = 36,
  PROJECT_COST_PROD_EDITION = 7,
  DEVELOPER_EDITION = 37,
  PROJECT_CDE = 1,
  SUSTAINABILITY_EDITION = 28,

  TIME_AGO_REFRESH_INTREVAL = 6000,

  REMOVE_NOTIFICATION_AFTER_TIME = 5000,

  FILE_LISTING = 1,
  COMMUNICATION_FORM_LISTING = 31,
  DISCUSSION_LISTING = 32,
  REVIEW_LISTING = 147,
  FILE_TRANSMITTAL_LISTING = 21,
  APPS_TRANSMITTAL_LISTING = 22,
  FIELD_TRANSMITTAL_LISTING = 23,
  CONTRACT_TRANSMITTAL_LISTING = 24,
  FM_TRANSMITTAL_LISTING = 25,
  FINANCE_TRANSMITTAL_LISTING = 26,
  PPM_TRANSMITTAL_LISTING = 27,
  HR_TRANSMITTAL_LISTING = 28,
  H_AND_S_TRANSMITTAL_LISTING = 29,
  PPMT_TRANSMITTAL_LISTING = 30,
  QUALITY_TRANSMITTAL_LISTING = 135,
  TRANSMITTALS_TRANSMITTAL_LISTING = 159,
  SEARCH_TYPE_CONTACTS_LISTING = 62,
  APP_GLOBAL_SEARCH_LISTING = 39,
  FIELD_FORM_LISTING = 51,
  CONTRACT_FORM_LISTING = 52,
  FM_FORM_LISTING = 53,
  FINANCE_FORM_LISTING = 54,
  PPM_FORM_LISTING = 55,
  HR_FORM_LISTING = 56,
  H_AND_S_FORM_LISTING = 57,
  MARKETPLACE_FORM_LISTING = 128,
  MARKETPLACE_TRANSMITTAL_LISTING = 129,
  ADMIN_FETCH_RULE_LISTING = 45,
  PROJECT_LISTING_TYPE = 42,
  ATTRIBUTE_LISTING_TYPE = 189,
  ATTRIBUTE_SET_LISTING_TYPE = 194,
  INDIVIDUAL_SET_LISTING_TYPE = 207,
  ATTRIBUTE_SET_HIERARCHY_LISTING = 198,
  CUSTOM_ATTRIBUTE_LIBRARY_HISTORY_LISTING = 206,
  CUSTOM_ATTRIBUTE_SETS_HISTORY_LISTING = 211,
  MODEL_LISTING_TYPE = 47,
  BCF_ISSUE_LISTING = 150,     //required for BLDBTRCBIM-653"
  DOCUMENT_TEMPLATE_LISTING_TYPE = 151,
  TEMPLATE_SELECTION_LISTING_TYPE = 152,
  SUSTAINABILITY_FORM_LISTING = 153,
  APPS_TRANSMITTAL_FORM_LISTING = 158,
  ADMIN_LISTING_TYPE = 44,
  MODEL_GLOBAL_SEARCH_LISTING = 8,
  COMMUNICATION_FORM_TEMPLATE_LISTING = 33,
  ALL_APP_TYPES_LISTING = 34,
  PROCUREMENT_CATALOGUE_DATA_LISTING = 35,
  PROCUREMENT_CATALOGUE_BASKET_LISTING = 36,
  DESCIPLINE_WISE_MODELREVISIONS_LISTING = 37,
  MODEL_PENDING_ACTIONS_DOCUMENT_LISTING = 38,
  ALL_APPS_AND_PROCUREMENT_MESSAGES_LISTING = 39,
  PRINT_OPTIONS_VALUE_ALL = 63,
  PROCUREMENT_LISTING = 4,
  NEW_PROCUREMENT_LISTING = 126,
  NEW_PROCUREMENT_TRANSMITTAL_LISTING = 127,
  CATALOG_LISTING = 101,
  LEGACY_REPORT_LISTING = 60,
  LEGACY_REPORTING_LISTING = 7,
  LEGACY_REPORT_TEMPLATE_LISTING = 12,
  CREATE_LEGACY_REPORT = 13,
  SCHEDULED_REPORT_LISTING = 14,
  SEARCH_TYPE_TEMPLATE_LISTING = 58,
  PPMT_FORM_LISTING = 59,
  QUALITY_FORM_LISTING = 134,
  SCHEDULING_REPORT_LISTING_TYPE = 61,
  ACL_MATRIX_REPORT = 90,
  MANAGE_DISTRIBUTION_GROUP = 91,
  MANAGE_APP_LISTING_TYPE = 49,
  CONTACT_LISTING_TYPE = 62,
  WORKFLOW_LISTING_TYPE = 64,
  WOM_INST_LISTING_TYPE = 66,
  WORKFLOWS_RULE_LISTING = 67,
  WORKFLOWS_ACTION_LISTING = 68,
  DIRECTORY_CATALOUGE_DATA_LISTING = 63,
  DOC_MAILBOX_LISTING = 46,
  FORM_MAILBOX_LISTING = 48,
  ASSIGNED_FORMTYPE_LISTING = 49,
  DOCUMENT_HISTORY_EXPORT = 92,
  FILE_TRANSMITTAL_DISTRIBUTION_LISTING = 196,
  PUBLIC_FOLDER_LISTING = 94,
  FORM_HISTORY_EXPORT = 93,
  EXPORT_FILE_ASSOCIATIONS_LINK_INFO_LISTING_TYPE = 97,
  NOTICE_LISTING = 69,
  CAN_VIEW_HISTORIC_MARKUPS = 163,
  MANAGE_USER_LISTING = 70,
  ASSOCIATION_MARKUP_LISTING = 102,
  ASSOCIATION_STATIC_LISTING = 103,
  ASSOCIATED_FORM_LISTING = 71,
  ATTACHMENT_LISTING = 72,
  ASSOCIATED_FILE_LISTING = 73,
  ASSOCIATED_DISCUSSION_LISTING = 74,
  ASSOCIATED_SELECTED_FILE_LISTING = 77,
  ASSOCIATED_SELECTED_DISCUSSION_LISTING = 78,
  ASSOCIATED_SELECTED_FORM_LISTING = 79,
  ASSOCIATED_LOCATION_LISTING = 80,
  ASSOCIATED_SELECTED_REVIEW_LISTING = 148,
  ASSOCIATED_REVIEW_LISTING= 149,
  COMMENT_REVIEW_FOR_COMMENT_COORD_LISTING = 188,
  CREATE_FORM_ATTACHMENT_LISTING = 139,
  WORKSPACE_ACCESS_LOG_LISTING = 80,
  CHECK_FOR_AKAMAI_UPLOAD_LIMIT = 199,
  ADMIN_INTEGRATIONS_LISTING = 130,
  SAGE_COMPANY_LISTINGTYPE = 163,
  SAGE_CUSTOMER_LISTINGTYPE = 170,
  SAGE_VENDOR_LISTINGTYPE = 163,
  SAGE_USER_LISTINGTYPE = 164,
  SAGE_PROJECT_LISTINGTYPE = 165,
  SAGE_COSTCODE_LISTINGTYPE = 166,
  SAGE_INTACCT_CUSTOMER_LISTINGTYPE = 175,
  SAGE_INTACCT_VENDOR_LISTINGTYPE = 176,
  SAGE_INTACCT_VENDOR_CONTACT_LISTINGTYPE = 177,
  SAGE_INTACCT_PROJECT_LISTINGTYPE = 178,
  SAGE_INTACCT_COSTCODE_LISTINGTYPE = 179,
  ACC_PROJECT_LISTING_TYPE = 202,
  ACC_FOLDER_LISTING_TYPE = 203,
  ACC_ATTRIBUTE_LISTING_TYPE = 204,
  ACC_ATTRIBUTE_VALUE_LISTING_TYPE = 205,
  ASSET_LISTING = 168, 
  ACCESS_HISTORY_LISTING_TYPE = 187,
  ASSOCIATE_FILE_LISTING_BEFORE_SELECTION = 190,
  ASSOCIATE_FORM_LISTING_BEFORE_SELECTION = 191,
  ASSOCIATE_REVIEW_LISTING_BEFORE_SELECTION = 192,
  CUSTOM_REPORT_TEMPLATE_LISTING = 197,

  FOLDER_ADMIN_PERMISSION = 1023,
  FOLDER_PUBLISH_AND_LINK_PERMISSION = 239,
  FOLDER_PUBLISH_PERMISSION = 111,
  FOLDER_VIEW_ONLY = 23,
  FOLDER_PERMISSION_VALUE_FOR_NO_ACCESS = 0,
  FOLDER_PERMISSION_VALUE_FOR_VIEW = 47,
  FOLDER_PERMISSION_VALUE_FOR_VIEW_N_LINK = 175,
  FOLDER_ADMIN_PERMISSION_FOR_MODEL = 4,

  // APPLICATION PRIVILEGES
  PRIV_EDIT_USERS = 1,
  PRIV_EDIT_Organisations = 2,
  PRIV_MANAGE_SYSTEM_NOTICES = 3,
  PRIV_MANAGE_FORM_TEMPLATES = 4,
  PRIV_MANAGE_ROLE_TEMPLATES = 5,
  PRIV_MANAGE_DRAWING_SERIES_TEMPLATES = 6,
  PRIV_MANAGE_REPROGRAPHICS = 7,
  PRIV_CREATE_REPRO_ORDER = 8,

  // other common
  CALLER_APP = 10,
  OBJECT_TYPE_FILTER = 102,

  // upload config
  MAX_PARALLEL_UPLOAD = 3,
  DEFAULT_FILE_CHUNK_SIZE = 2097152,
  DEFAULT_MAX_FILE_SELECTION = 250,
  MAX_RETRIES_AFTER_ERROR = 1800,
  RETRY_INTERVAL_DURATION = 1000,
  ASYNC_UPLOAD_PROGRESS_MAX_RETRY = 1800,
  ASYNC_UPLOAD_PROGRESS_INTERVAL = 10000,

  // proxy user
  GET_SELECTED_PROXY_USERS = 1802,
  GET_ONLINE_ACTIVE_USERS_FOR_PROXY = 1803,
  ASSIGN_PROXY_USER = 1804,
  ASSIGN_PROXY_USER_ORG_ENABLED = 1806,

  ASSOC_ALL_FORM_VIEWER = 23,

  // upload types
  UPLOAD_TYPE_FILE = 1,
  UPLOAD_TYPE_ATTACHMENT = 2,
  UPLOAD_TYPE_COMMENT = 5,
  UPLOAD_TYPE_FORM = 4,
  UPLOAD_TYPE_MODEL_ATTACHMENT=11,
  UPLOAD_TYPE_TEMPLATE=13,

  USER_VIEWER_PREFERENCE_HTML_VIEWER= 5,
  USER_VIEWER_PREFERENCE_ACTIVEX_VIEWER= 6,
  USER_VIEWER_PREFERENCE_PDFTRON_VIEWER= 7,

  ACTIVITY_REVISION_UPLOAD = 101,
  ACTIVITY_FILE_DISTRIBUTION = 102,
  ACTIVITY_EDIT_ATTRIBUTES = 103,
  ACTIVITY_UPDATE_STATUS = 104,
  ACTIVITY_ADD_COMMENT = 105,
  ACTIVITY_KEY_REVISION_UPLOAD = 1001,
  ACTIVITY_KEY_FILE_DISTRIBUTION = 1002,
  ACTIVITY_KEY_EDIT_ATTRIBUTES = 1003,
  ACTIVITY_KEY_UPDATE_STATUS = 1004,
  ACTIVITY_KEY_ADD_COMMENT = 1005,

  /* Admin Tab Option Type START */
  ADMIN_ALL_OPTION_LISTINGID = 1000,
  ADMIN_GLOBAL_ATTRIBUTE_OPTIONID = 4,
  ADMIN_FETCH_ATTRIBUTE_OPTIONID = 5,
  ADMIN_SETUP_ADODDLE_NOTICE = 6,
  ADMIN_DESIGN_LAYOUT = 1,
  ADMIN_MANAGE_USERS_OPTIONID = 7,
  AODDLE_APPENGINE = 8,
  ADMIN_INTEGRATIONS = 9,
  ADMIN_DOCUMENT_TEMPLATE=11,
  ADMIN_RETENTION_POLICY= 12,
  ADODDLE_ADMIN_OPTION = 1333,
  ADODDLE_AUTO_FETCH_ATTRIBUTE_LISTING = 1334,
  ADODDLE_AUTO_FETCH_ATTRIBUTE_SAVE = 1335,
  ADODDLE_AUTO_FETCH_ATTRIBUTE_EDIT = 1336,
  GET_SYSTEM_ATTRIBUTES = 1339,
  ADODDLE_GET_FETCH_ATTRIBUTE_HISTORY = 1340,
  ADMIN_DEVELOPER_EDITION = 13,
  GET_USER_APPLICATION_PRIVILEGES_MULTI = 1342,
  // TODO-BLDBTRPLTF-1956 chnage id of RETENTION_POLICY & RETENTION_RECORD
  RETENTION_POLICY= 10001,
  RETENTION_RECORD= 10002,
  RETENTION_GET_USER_PUBLIC_VISIBILITY_CONTACT_TYPE = 4,
  RETENTION_GET_USER_EMAIL_VISIBILITY_CONTACT_TYPE = 5,
  RETENTION_RECORD_LISTING_TYPE = 169,
  RETENTION_RECORD_DELETED_PROJECT_LISTING_TYPE = 171,
  RETENTION_RECORD_DELETED_FILE_LISTING_TYPE = 172,
  RETENTION_RECORD_DELETED_FORM_LISTING_TYPE = 173,
  RETENTION_RECORD_TO_BE_DELETED_PROJECT_LISTING_TYPE = 183,
  RETENTION_RECORD_TO_BE_DELETED_FILE_LISTING_TYPE = 182,
  RETENTION_RECORD_TO_BE_DELETED_FORM_LISTING_TYPE = 181,

  /* Admin Tab Option Type END */
  VIEW_REPORT_ACTION = 8,
  GET_MESSAGE = 9,
  DOWNLOAD_REPORT = 10,
  EMAIL_REPORT = 15,
  /* TakeOffs Tab Option type START*/
  BIDWINNER_APPLICATION_ID = 6,
  TAKE_OFFS_LISTING_TYPE = 131,
  TAKE_OFF_ESTIMATES_LISTING_TYPE = 132,
  ESTIMATES_LISTING_TYPE = 133,
  /* TakeOffs Tab Option type END*/

  GET_FOLDER_TREE_MOVE_DOC = 777,
	MOVE_DOC_VALIDATION_SUMMARY = 389,
  COMMIT_MOVE_DOCUMENT= 390,
  FOLDER_LISTING_ADRIVE  = 138,
  GET_USER_CUSTOMISED_COLUMN_DATA  = 1735,

  ACTIVATE_RULE_STATUS = 8,
  DEACTIVATE_RULE_STATUS = 9,
  ACTIVATE_SYSTEM_ACTION = 11,
  DEACTIVATE_SYSTEM_ACTION = 10,

 /* Action Status Map*/
 ACTION_INCOMPLETE = 0,
 ACTION_INACTIVE = 4,

 DOCUMENT_TEMPLATE_NEW_REVISION_ACTION =  1,
 DOCUMENT_TEMPLATE_MANAGE_ACCESS_ACTION =  2,

 // cBIM Timeout for activity is 10 sec
 MODEL_ACTIVITY_TIMEOUT = 10000,

 // Async Progress for activity is 10 sec
 ASYNC_PROGRESS_ACTIVITY_TIMEOUT = 10000,

 // exportType
 EXPORT_TYPE = 4,
 CAN_MANAGE_INTEGRATIONS_ALL_ORG = 154,
 CAN_MANAGE_INTEGRATIONS_OWN_ORG = 153,
 CAN_MANAGE_EADDRESS_INTEGRATION_OWN_ORG = 227,
 CAN_MANAGE_EADDRESS_INTEGRATION_ALL_ORG = 226,
 INITIATE_DOCUSIGN_E_SIGNING = 1915,
 GET_ADDITIONAL_FILE_VIEW_DATA = 1916,

 // appType
 PLAYBOOK = 23,

 // Action Id for ProductRibbon details
 RIBBON_DETAILS = 1912,
 SAVE_RIBBON_DETAILS = 1911,
 PORTAL_PRODUCTS = 1913,

 // exportType for HTML
 BCF_EXPORT_TYPE_HTML = 1,
 BCF_EXPORT_BATCH_SIZE = 100,

  // FileType
  TYPE_APPS_ATTACHMENT = 4,
  TYPE_DISCUSSION_ATTACHMENT = 5,
  TYPE_APPS_INLINE_ATTACHMENT = 6,
  TYPE_ISSUE_ATTACHMENT = 7,

  DOC_TYPE_ID_NORMAL = 1,
  DOC_TYPE_ID_PLACEHOLDER = 2,

  // OCR feature
  ADD_NEW_STATUS_DATA= 813,
  GET_PROJECT_STATUS= 812,
  SAVE_POI= 822,
  CAN_ACCESS_SMART_UPLOAD = 221,

  DC_TYPE=2,

  FEATURE_VISIBILITY_ACTION = 1903,
  AUTO_CALIBRATION_FEATURE_VISIBILITY = 10,
  // // used mostly for create form area, from: {}
  CANCEL_AFTER_AUTO_SAVED = 26,
  CHECK_FORM_CLOSE = 968,

  ADODDLE_MOBILE_APP=3,
  ASITE_CDE_APP = 26,

  APPLICATION_ID = 2,

  //html appbuilder constant
  ZIPTYPE_FOR_NEW_RENDERING_APPBUILDER = 1,
  
  // Custom Object constants
  CUSTOM_OBJECT_MODEL_CONTEXT_ID = 3,
  CUSTOM_OBJECT_BCF_EXPORT_BATCH_SIZE = 100,

  // Preferences
  FILE_PREFERENCE = 12
}
