export enum TO_OPEN_DOC_ACTIONS_MAP {
    "createCommet" = 9,
    "coordination" = 10,
    "incorporation" = 11,
    "distribution" = 12,
    "publish" = 28,
    "ASSIGN_STATUS" = 14,
    "FOR_ACTION" = 35,
    "FOR_ACKNOWLEDGEMENT" = 8,
    "modelOption" = 33
}

export enum TO_OPEN_FORM_ACTIONS_MAP {
    "ASSIGN_STATUS" = 2,
    "Respond" = 3,
    "distributeApp" = 6,
    'Review Draft' = 34,
    "FOR_ACTION" = 36,
    "FOR_ACKNOWLEDGEMENT" = 37
}

export enum DOC_ACTIONS {
    FOR_ACKNOWLEDGEMENT = 8,
    FOR_ACTION = 35,
    FOR_COMMENT = 9,
    FOR_COMMENT_COORD = 10,
    FOR_COMMENT_COORDINATION = 10,
    FOR_COMMENT_INCORP = 11,
    FOR_DISTRIBUTION = 12,
    FOR_INFORMATION = 13,
    FOR_STATUS_CHANGE = 14,
    FOR_PUBLISHING = 28,
    FOR_VISIBILITY = 25,
    FOR_START_WORKFLOW_LIST = 4,
    INITIATE_WORKFLOW = 20,
    FOR_MODEL_OPTIONS = 33

}
export enum FORM_ACTIONS {
    READ = 0,
    ASSIGN_STATUS = 2,
    RESPOND = 3,
    RELEASE_RESPONSE = 4,
    ATTACH_DOCS = 5,
    DISTRIBUTE = 6,
    FOR_INFORMATION = 7,
    FOR_STATUS_CHANGE = 14,
    REVIEW_DRAFT = 34,
    FOR_ACTION = 36,
    FOR_ACKNOWLEDGEMENT = 37
}
export enum CUSTOM_OBJECT_ACTIONS {
    FOR_ACTION = 76,
    FOR_INFORMATION = 78,
    READ_REVIEW = 82
}
export enum PROJECT_STATUS {
	OPEN = 5,
	CLOSED = 6,
	ARCHIVED = 7
}

export enum HISTORY_TYPE {
	ALL = -1,
    REVISIONS = 1,
    STATUS = 6,
	DISTRIBUTION = 11,
    SIGNATORIES = 27
}

export enum DISTRIBUTION_LEVEL {
    ROLES = 1,
	ORGANISATIONS = 2,
	USERS = 3,
	USER_GROUPS = 4,
	DISTRIBUTION_GROUPS = 5
}

export enum INPUT_DATA_TYPE {
    CO_LATI_LONG = 35,
    DECIMAL = 8,
    INTEGER = 7,
    SURVEY_GRID_REF = 36
}

export enum WORKFLOW_CLONE_OBJ_ID {
	WORKFLOW_DEFINATION = 30,
	WORKFLOW_TRIGGER = 31,
	WORKFLOW_SYSTEM_ACTIONS = 32
};