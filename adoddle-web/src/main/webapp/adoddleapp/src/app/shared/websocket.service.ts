import { Injectable } from '@angular/core';
import * as Rx from 'rxjs';
import { environment } from '../../environments/environment.prod';
import { CommonUtilService } from './common-util.service';
import { ApiConstant } from './api-constant';

@Injectable()
export class WebsocketService {
  private websocketInsts = {};
  private _loginType = window['USP'] && window['USP'].loginMethodType;
  private _WSretryCount = 0;
  private _WSDashboardNotificationRetryCount = 0;
  private _SessionAPIretryCount = 0;
  private _isSessionActive = true;
  private subscriptions = {};

  constructor(private util:CommonUtilService) { }

  private connect(url, onopen?) {
    return;
    let ws = this.websocketInsts[url];
    if(!ws || ws.readyState === WebSocket.CLOSED) {
      ws = new WebSocket(url);
      this.websocketInsts[url] = ws;

      if(onopen) {
        ws.onopen = onopen;
      }
    } else {
      onopen && onopen();
    }

    ws.onmessage = (data) => {
      for(var i = 0; i < this.subscriptions[url].length; i++) {
        let obs = this.subscriptions[url][i];
        obs.next(data);
      }
    }
    ws.onerror = (err) => {
      // Not Handle Local Error because in local environment WS calls are getting fail.
      if(window['baseUrl'].indexOf('adoddle') < 0){ 
        return;
      }
      // Handle Error for live WS Call
      if(url ==  window['messagingServiceApiURL'] + ApiConstant.WS_CHAT){
        this._WSretryCount++;
        this._WSretryCount > 5 && this._SessionAPIretryCount < 5 && this.util.ajax({
          url: ApiConstant.GET_SESSION_TIMEOUT_DETAILS,
          method: 'GET',
          data: {},
          offline: (err) => {
           console.log('offline',err);
          },
          success: (response) => {
            this._SessionAPIretryCount++;
            this._WSretryCount = 0;
            if(response.body && response.body.timeout){
              let reasonType =response.body.reasonType;
              let reason = response.body.reason;
              if((reason == "USP_IS_NOT_FOUND" && reasonType == 1) || (this._loginType === 1 && ((reason == "SESSION_STATE_IS_TIMEOUT"  && reasonType == 2) || (reason == "SESSION_IS_TIMEOUT"  && reasonType == 3)))){
                this._isSessionActive = false;
              }
            }
          },
          error: (err) => { 
            this._SessionAPIretryCount++;
              if(err.error && err.error.errorCode && err.error.errorMsg && err.error.errorMsg.includes('ASessionID is not found from cookies')){
                this._isSessionActive = false;
              }else{
                this._WSretryCount = 0; 
                console.log('error',err);
              }
          }
        });
    }
      // for(var i = 0; i < this.subscriptions[url].length; i++) {
      //   let obs = this.subscriptions[url][i];
      //   obs.error(err);
      // }
    }
    ws.onclose = (e) => {
      // Stop the dashboardNotification Websocket when it's closed with USP_IS_NOT_FOUND error
      if(url == window['INIT_JSP_GLOBAL']?.asiteDashboardNotificationWebscoketUrl){
        if(e?.code == 1008 && e?.reason == 'USP_IS_NOT_FOUND'){
          console.error('Error occured in dashboardNotification websocket: ',e?.code, e?.reason);
          if(this._WSDashboardNotificationRetryCount >= 2){
            this._isSessionActive = false;
          } else {
            this._WSDashboardNotificationRetryCount++;
          }
        }
      }
      for(var i = 0; i < this.subscriptions[url].length; i++) {
        let obs = this.subscriptions[url][i];
        obs.next({data: JSON.stringify({identifier: 'CONNECTION_ERROR', payload: {}})});
      }
      
      setTimeout(() => {
        let sessionState = localStorage.getItem('sessionState');
        if (this._isSessionActive && sessionState != 'Inactive') {
          this.connect(url);
        }
      }, 5000);
    };

    return ws;
  }

  public reconnect() {
    return;
    if(!Object.keys(this.websocketInsts).length) {
      return;
    }
    this._isSessionActive = true;
    this._WSDashboardNotificationRetryCount = 0;
    for (const websocketInstanceUrl in this.websocketInsts) {
      if (this.websocketInsts.hasOwnProperty(websocketInstanceUrl)) {
        this.connect(websocketInstanceUrl);
      }
    }
  }

  public create(url, onopen?: Function): Rx.Subject<MessageEvent> {
    return;
    if(!this.subscriptions[url]) {
      this.subscriptions[url] = [];
    }

    this.connect(url, onopen);

    let observable = Rx.Observable.create((obs: Rx.Observer<MessageEvent>) => {
      this.subscriptions[url].push(obs);
      //return ws.close.bind(ws);
      return () => {
        var index = this.subscriptions[url].indexOf(obs);
        if(index > -1) {
          this.subscriptions[url].splice(index, 1);
        }
      };
    });

    let observer = {
		  next: (data: any) => {
        let ws = this.websocketInsts[url];
			  if (ws.readyState === WebSocket.OPEN) {
				  ws.send(JSON.stringify(data));
			  }
		  }
    };

	  return Rx.Subject.create(observer, observable);
  }

}
