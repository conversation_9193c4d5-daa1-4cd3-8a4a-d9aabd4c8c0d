import { ChangeDetector<PERSON><PERSON>, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { NgbDropdown } from '@ng-bootstrap/ng-bootstrap';
import { DOC_TYPE_ID, ENTITY_TYPE } from 'app/shared/action/entity-type.enum';
import { DOC_ACTIONS } from 'app/shared/actions.enum';
import { ApiConstant } from 'app/shared/api-constant';
import { AppConstant } from 'app/shared/app-constant';
import { ATTACH_ASSOC_LABEL_MAP, DOC_ACTION_MAP, INIT_COMPONENT_MAP } from 'app/shared/app-type.enum';
import { CommonUtilService } from 'app/shared/common-util.service';
import { CommonViewActionService } from 'app/shared/common-view/common-view-action-container/common-view-action.service';
import { ActivityService } from 'app/upload/activity-center/activity.service';
import { StartWorkflowService } from 'app/shared/start-workflow/start-workflow.service';
import { EditAttributesService } from 'app/upload/edit-attributes/edit-attributes.service';
import { FileItem } from 'app/upload/upload-directive/file-item.class';
import { FileUploader } from 'app/upload/upload-directive/file-uploader.class';
import { PrintFileModalService } from 'app/cross-platform/print-file-modal/print-file-modal.service';
import { FileFormUtilService } from 'app/shared/file-form-util.service';
import { SidebarService } from 'app/shared/sidebar.service';
import { WatcherContainerService } from '../../watch-notification/watcher-container.service';
import { CreateFileViewComponentService } from 'app/shared/create-file-view-component.service';
import { AuthenticationService } from 'app/common/authentication.service';
import { FileViewActionService } from 'app/shared/file-view-action/file-view-action.service';
import { TranslateService } from '@ngx-translate/core';
import { CognitiveCdeService } from 'app/cognitive-cde/cognitive-cde.service';
import { RESOURCE_TYPE_ID, VECTORIZATION_STATES } from 'app/cognitive-cde/cognitive.enum';

@Component({
    selector: 'adoddle-file-viewer',
    // standalone: true,
    // imports: [],
    templateUrl: './file-viewer.component.html',
    styleUrl: './file-viewer.component.scss',
    providers:  [ CreateFileViewComponentService ]
})
export class FileViewerComponent implements OnInit {

    /**
     * Dont allow to send amessages and edit user profile from user-card in file view
     * @memberof FileViewerComponent
     */
    isFromFileView: boolean = false;

    myConfig: any;
    folderHasUploadPermission: boolean;
    isMobile: boolean;
    isNavigator: boolean;
    isAsiteFieldApp: boolean;
    isAsiteCDEApp: boolean = false;
    currentRevisionId: string;
    fileNameWithOutExt: any;
    isFromAsiteWorks: boolean;
    AVAILABLE_USER_TABS: Array<string>;
    priv: any;
    fileExtName: string;
    canViewHistoricMarkups: boolean;
    fileData: any;
    microsoftBtnTitle: string;
    microsoftBtnIcon: string;
    showStartWorkflow: boolean;
    publishRevisionDisabled: boolean;
    publishRevisionDisabledTitle: string;
    publishMSRevisionDisabled: boolean;
    simpleFileView: boolean;
    AppConstant: any
    rightContentActionParams: {
        fileData: any;
        name: string,
        data: any,
        commonViewParams: any,
        configData: any
    };
    reviewEnabled: boolean;
    openReviewComment: boolean;
    meetingData: Array<any>;
    staticContentVersion: any;
    actionFnMap: any = {};
    isProArchived: boolean = false;
    curOpenActionPopover: any;
    unreadReviewCounts: any;
    fileNotSupportedMsg: string;
    fileNotSupportedButCanDownloadMsg: string;

    noCompareFileMain: boolean = false;
    compareFlag = 0;
    hideElements: boolean;
    loadCreateForm: boolean = false;

    // Identify user have access on Explore with AI feature or not
    _hasCognitiveCDEBtnVisible = false;

    // Identify chatBot section is opened or not
    _isChatBotSectionShow = false;

    // Hold cognitive cde supported filetypes
    private cognitiveSupportFileTypes = [];

    @ViewChild('createFormDD', { static: false, read: NgbDropdown }) createFormDD: NgbDropdown;
    @ViewChild('publishRevisionFileInput', { static: true }) publishRevisionFileInput: ElementRef;
    @ViewChild('statusChange', { static: false, read: NgbDropdown }) statusChange: NgbDropdown;

    @ViewChild('mediaFileViewer', { static: false }) mediaFileViewer: ElementRef;
    @ViewChild('hoopsWebSsrViewer', { static: false }) hoopsWebSsrViewer: ElementRef;
    publishRevisionUploader: FileUploader;

    private $: any = window['jQuery'];
    deviceApplicationId = [3, 20, 21, 22, AppConstant.ASITE_CDE_APP];
    isFullScreen: boolean;


    /**
     * Indicates whether DocuSign e-signature integration is enabled.
     * When true, DocuSign signing actions are available for supported files.
     * @type {boolean}
     * @memberof FileViewerComponent
     */
    enableDocusignIntegration:boolean = false;

    // /**
    //  * List of supported file extensions for DocuSign e-signature.
    //  * Only files with these extensions can be sent for DocuSign signing.
    //  * @type {string[]}
    //  * @memberof FileViewerComponent
    //  */
    // docuSignSupportedFileTypes = DOCUSIGN_SUPPORTED_FILE_FORMATS;

    enableOfficeForWeb: any = window['enableOfficeForWeb'];
    msOfficeData: any = window['data'];

    constructor(
        private util: CommonUtilService,
        private commonViewActionService: CommonViewActionService,
        private activity: ActivityService,
        private editAttrService: EditAttributesService,
        private startWorkFlowService: StartWorkflowService,
        private printFileModalService: PrintFileModalService,
        private fileFormUtilService: FileFormUtilService,
        private sideBarService: SidebarService,
        private changeDetectorRef: ChangeDetectorRef,
        private watcherService: WatcherContainerService,
        private fileViewActionService: FileViewActionService,
        private cogCdeService: CognitiveCdeService,
        private createFileViewComponentService: CreateFileViewComponentService,
        private authenticationService: AuthenticationService,
        private translate: TranslateService
    ) {
        translate.setTranslation((<any>window).USP.languageId, (<any>window).Language.getLangObj());
    translate.setDefaultLang((<any>window).USP.languageId);
        this.AVAILABLE_USER_TABS = [
            '2',
            '3',
            '5',
            '11',
            '30',
            '31',
            '32',
            '33',
            '34',
            '35',
            '36',
            '37'
        ];
        this.showStartWorkflow = true;
        this.publishRevisionDisabled = true;
        this.publishRevisionDisabledTitle = '';
        this.publishMSRevisionDisabled = true;
        this.AppConstant = AppConstant;
        this.openReviewComment = false;
        this.unreadReviewCounts = null;
        this.hideElements = false;
    }

    resetRightContentData() {
        this.resizeHoopsViewer();
        this.rightContentActionParams = {
            fileData: null,
            name: null,
            data: null,
            commonViewParams: null,
            configData: null
        };
    }

    ngOnInit(): void {
        this.fileViewActionService.register(this);
        window['notification'] = this.util.notification;
        this.myConfig = window['fileViewObj'];
        this.meetingData = window['meetingTypeData'];
        this.staticContentVersion = window['staticContentVersion'];
        this.reviewEnabled = this.myConfig.enableCommentReview || false;
        this.fileNotSupportedMsg = null;
        this.fileNotSupportedButCanDownloadMsg = null;
        this.cognitiveSupportFileTypes = this.myConfig?.exploreWithAiSupportedFiles?.split(',');

        let url = window['location'].href;
        if(url.indexOf('isCognitiveTabActive=true') > -1 && url.indexOf('DRILL_DOWN_COG_CDE') == -1 && !window['USP']?.isProxyUserWorking && window.opener){ 
            try { // Send and received the highlight data for cognitive cde tab
                window.opener.postMessage(JSON.stringify({ FileViewerInitialized: this.myConfig.revisionId }));
                let msgReceiver = (event)=>{
                    if(typeof event?.data == 'string' && event.data?.indexOf('cognitiveCDEHighlightData') > -1){
                        window['cognitiveCDEHighlightData'] = JSON.parse(event.data)['cognitiveCDEHighlightData'];
                        window.removeEventListener('message', msgReceiver);
                    }
                };
                window.addEventListener('message', msgReceiver);     
            } catch (error) {
                console.error(error);
            }
        }

        if(url.indexOf('DRILL_DOWN_COG_CDE') > -1 && !window['USP']?.isProxyUserWorking && url.indexOf('sessionID') > -1)  {
            let sessionID = url.split('sessionID=')[1].split('&')[0];
            window['cognitiveCdeSessionId']  = sessionID;
            window['cognitiveCdeRetriveSessionId']  = sessionID;
        }

        if(window['USP']?.isProxyUserWorking && this.myConfig?.toOpen == 'DRILL_DOWN_COG_CDE'){ 
            this.myConfig.toOpen = ''; // Prevent the drill down chat section while access by proxy user.
        }
        
        this.isAsiteCDEApp = this.myConfig.applicationId == AppConstant.ASITE_CDE_APP;

        if (!this.myConfig.isFolderActive && !this.myConfig.isOfflineMode) {
            return;
        }

        this.cogCdeService.getSelectedProject().subscribe((projectData)=>{
            this._hasCognitiveCDEBtnVisible = !!projectData;
            if(!projectData && this._isChatBotSectionShow){ // For close the chat section when vectorized project is deleted
                this.performAction(INIT_COMPONENT_MAP.DRILL_DOWN_COG_CDE);
            }
        });

        this.cogCdeService.getSelectedMsgData().subscribe((references)=>{
            if(references?.length){
                let data:any = this.cogCdeService.prepareCognitiveDataForHighlightAndListing(references);
                data = data[0][this.myConfig.revisionId] || [];
                if(!data.length) { return false; }
                window['cognitiveCDEHighlightData'] = data;
                window.postMessage({event: 'hightLightCognitiveText'}); // Handle this event in viewerConfig file
            }
        });

        this.commonViewActionService.isDock.subscribe(isDocked => {
            this.resizeHoopsViewer();
        });

        this.commonViewActionService.openRightContent.subscribe((data) => {
            if (this.rightContentActionParams.name == data.name && data.force) {
                return;
            }
            this.performAction(data.name, data);
        });

        this.commonViewActionService.updateStatusName$.subscribe((updatedStatusName) => {
            if(updatedStatusName && typeof(updatedStatusName) == 'string') {
                this.fileData.currentRevision.status = updatedStatusName;
            }
        })

        this.commonViewActionService.isFullScreen.subscribe(isFullScreen => {
            this.isFullScreen = isFullScreen;
        });

        this.commonViewActionService.isOpen.subscribe((data) => {
            if (!data) {
                this.resetRightContentData();
            }
        });

        this.commonViewActionService._closeComponentWithName.subscribe((componentName)=>{
            if(componentName == INIT_COMPONENT_MAP['DRILL_DOWN_COG_CDE']){
                this._isChatBotSectionShow = false;
                this.openCognitiveCDECallback = false;
                document.body.classList.remove('cognitive-cde-tab-active');
            }
        });

        this.sideBarService.updateCount$.subscribe((count) => {
            if (count > -1) {
                this.unreadReviewCounts = count;
            }
        });

        if (this.reviewEnabled && this.myConfig.toOpen == 'createCommet') {
            this.myConfig.toOpen = 'createReview';
        }

        this.simpleFileView = this.myConfig.documentTypeId == 13;
        this.resetRightContentData();
        if (this.myConfig.userFolderPermission == AppConstant.FOLDER_ADMIN_PERMISSION
            || this.myConfig.userFolderPermission == AppConstant.FOLDER_PUBLISH_AND_LINK_PERMISSION
            || this.myConfig.userFolderPermission == AppConstant.FOLDER_PUBLISH_PERMISSION) {
            this.folderHasUploadPermission = true;
        }

        this.isAsiteFieldApp = (window['asiteFieldAppIds'] || [3]).includes(this.myConfig.applicationId);

        if (this.util.isMobile() || this.isAsiteFieldApp) {
            this.isMobile = true;
            // angular.element("body").addClass("mobile");
        }

        // Check for Navigator
        if (this.myConfig.applicationId == 2) {
            this.isNavigator = true;
            // angular.element("body").addClass("navigator");
        }

        this.currentRevisionId = this.myConfig.revisionId;
        this.isFromAsiteWorks = this.myConfig.isFromAsiteWorks;

        this.priv = {
            isForFileViewOnly: (this.myConfig.viewAlwaysDocAssociation != false),
            canChangeStatus: false,
            canAccessAuditInfo: false,
            canDownloadFile: false,
            canEditFileAttr: false,
            canDistributeFile: this.myConfig.isActive && !this.util.isLocked(this.myConfig.lockActivityIds, "ACTIVITY_FILE_DISTRIBUTION"),
            canPrint: false,
            canStartWorkflow: false,
            canManageReviewFlags: false
        };

        if (this.myConfig.isOfflineMode) {
            this.initOffline();
        } else {
            this.init();
            this.initPublishRevisionUploader();
        }

        // Historic markup start - Thin Viewer to UWV
		/* Open historic markups component on click of warning message when viewing a migrated file,
		   only if the user has access to view historic markups, and if the markups are migrated
		*/
        if(this.myConfig.ispublicCommentAssocatedmarkupAvailable && this.myConfig.isMigratedProject) {
            let notificationElement = this.util.adoddleNotification({ type: 4, msg: this.util.lang.get("historic-markup-notification"), autoHide: false, hideCloseIcon: true });
            let openHistoricMarkups = notificationElement.find('#openHistoricMarkups');
            if(openHistoricMarkups && openHistoricMarkups.length) {
                openHistoricMarkups[0].style.textDecoration = 'underline';
                openHistoricMarkups[0].style.color = '#0000EE';
                openHistoricMarkups[0].addEventListener('click', () => {
                    this.util.submitForm({
                        url: ApiConstant.PUBLISH_PDF_CONTROLLER,
                        param: {
                            lm_projectId : this.myConfig.projectId.split('$$')[0],
                            lm_revisionId : this.myConfig.revisionId.split('$$')[0],
                            action_id : AppConstant.GENERATE_LEGACY_MARKUP_CONSOLIDATED
                        },
                        target: '_blank'
                    });
                });
            }
        }
		// Historic markup close

        this.watcherService.myConfig = this.myConfig;
        this.watcherService.getinstantNotificationData();
        this.callbackPolicyDetailsFile = this.callbackPolicyDetailsFile.bind(this);
    }

    // Returns true if file extention is match the given fileTypes array
    private matchesFileType(fileTypes=[]) {
        let fileName = this.myConfig?.fileNameWithExt;
        return fileTypes?.includes(fileName?.slice(fileName?.lastIndexOf('.') + 1)?.toLowerCase());
    }

    private initPublishRevisionUploader() {
        this.publishRevisionUploader = new FileUploader({
            url: ApiConstant.ADODDLE_UPLOAD_CONTROLLER + '?action_id=' + AppConstant.UPLOAD_CHUNK_FILE,
            isHTML5: true,
            itemAlias: 'file',
            autoUpload: false,
            removeAfterUpload: false
        });

        this.publishRevisionUploader.onAfterAddingAll = (files: Array<FileItem>) => {
            if (!files?.length) {
                return;
            }

            this.publishRevision(files[0]._file);
            this.publishRevisionUploader.clearQueue();
        };
    }

    goBackMobile() {
        this.util.sendEventToIpad("backClicked");
    }

    publishRevision(file?) {
        let selectedRows = [this.fileData.currentRevision];
        // Check if alteast one row has publishin task
        let found = selectedRows.find(row => {
            if (!row?.actions?.length) {
                return false;
            }

            return row.actions.find(action => {
                return action.actionStatus == 0 && action.actionId == DOC_ACTIONS.FOR_PUBLISHING;
            });
        });

        // Open file browse for publish revision screen when multiple documents are selected
        this.addUploadActivity({
            params: {
                projectId: this.util.getProjectId(selectedRows[0]),
                folderId: selectedRows[0].folderId,
                folderPath: selectedRows[0].documentFolderPath,
                dcId: selectedRows[0].dcId,
                selectedFiles: selectedRows,
                fileToPublish: file,
                hasPublishingAction: found ? true : false
            },
            excelImport: true,
            publishRevision: true,
            folderHasNoUploadPermission: this.folderHasUploadPermission,
        });
    }

    addUploadActivity(options: any) {
        this.activity.addActivity({
            type: 'Upload',
            options: {
                ...options,
                maxCharLength : true,
                onComplete: (obj) => {
                    this.informParent();
                    setTimeout(() => {
                        if (!this.folderHasUploadPermission) {
                            this.publishRevisionDisabled = true;
                        }
                        this.navigateToLatestRev();
                    }, 1000);
                    if (!this.activity.isOpen()) {
                        return;
                    }
                }
            }
        });
    }

    initOffline() {
        let offFileName = this.myConfig.fileName;
        this.fileNameWithOutExt = offFileName.substr(0, offFileName.lastIndexOf("."));

        // angular.element('#view-header-title').html(fileNameWithOutExt);

        switch (this.myConfig.toOpen) {
            case "ASSOC_ATTACH":
                window['offlineAttachAssocCallback'] = () => {
                    this.performAction("assoc-attach");
                };
                break;
            default:
                this.fileData = {
                    currentRevision: {
                        documentId: this.myConfig.documentId,
                        folderId: this.myConfig.folderId,
                        revisionId: this.myConfig.revisionId,
                        projectId: this.myConfig.projectId
                    },
                    basicDetails: {
                        filePath: "",
                        docTitle: ""
                    },
                    commentParam: {
                        docRevisionId: this.myConfig.revisionId,
                        doctId: this.myConfig.documentId,
                        folder_id: this.myConfig.folderId,
                        project_id: this.myConfig.projectId,
                        isOnlyPlaceholderSelected: this.myConfig.documentTypeId == AppConstant.DOC_TYPE_ID_PLACEHOLDER ? true : false,
                        isOnlyDocSelected: this.myConfig.documentTypeId == AppConstant.DOC_TYPE_ID_NORMAL ? true : false,
                        isAssocMarkups: this.myConfig.isFromFileViewForDomain ? true : false,
                        isAssocViews: this.myConfig.model_id ? true : false,
                        'action_id': AppConstant.SAVE_COMMENT
                    }
                }
                this.fetchAndSetPriv();
                break;
        }
        this.assignActionCallbacks();
    };

    init() {
        this.isProArchived = this.myConfig.isProjectArchived;
        window['highlightCustomObjectInstance'] = this.myConfig.reviewInstanceId;
        window['comment_msg_id'] = this.myConfig.commentMsgIdToExpand; //highlight comment if its comming from list

        if (this.myConfig.reviewCommentId) {
            window['highlightCustomObjectComment'] = this.myConfig.reviewInstanceId + "_" + this.myConfig.reviewCommentId;
        }

        // Commented below condition to allow BIM file to view for all users

        // if (this.myConfig.hasHoopsViewerSupport && !this.AVAILABLE_USER_TABS.includes('' + this.myConfig.userAccessableTabs)) {
        //     this.fileNotSupportedMsg = this.util.lang.get("you-do-not-have-the-permission-to-view-this-file");
        // }

        this.fetchAndSetPriv();

        if (this.myConfig.isFromAdrive) {
            if (!this.myConfig.isFromCompressedFile && !this.myConfig.hasOnlineViewerSupport) {
                this.checkRevValidForDownload();
            }
            return;
        }

        let isPdfTron = this.myConfig.viewerPreference == 7;
        if (this.myConfig.documentTypeId == AppConstant.DOC_TYPE_ID_PLACEHOLDER && !this.myConfig.viewFileWhenPlaceholder) {
            this.fileNotSupportedMsg = this.util.lang.get("the-placeholder-cannot-be-viewed-online");
            !isPdfTron && this.clearForInfoFromFileViewer();
        }
        else if (!this.myConfig.isFromCompressedFile && !this.myConfig.hasOnlineViewerSupport) {
            this.checkRevValidForDownload();
        }

        this.assignActionCallbacks();
        if ((window['asiteFieldAppIds'] || [3]).includes(this.myConfig.applicationId)) {
            setTimeout(() => {
                this.util.sendEventToIpad("checkIsMarkOffline:" + JSON.stringify(this.myConfig));
            }, 500);
        }
        this.util.getOfficeActionUrl();
        
        window.addEventListener('message', (event) => {
            if(typeof event.data !== 'string') { 
                return; 
            }
            if((window['asiteFieldAppIds'] || [3]).includes(window['applicationId']) && event.data.indexOf("lifecycle") == -1){
                try {
                    if(event.data && event.data.indexOf('backButtonClick') > -1){
                        let message = JSON.parse(event.data);
                        if (message.name == "backButtonClick") {
                            let button = this.$("#fileViewPort").find('button');
                            button.click();
                            return;
                        }
                    }
                } catch (e) {
                    console.log(e);
                }
            }
            
            if (event.data && event.data.indexOf('CreateNewMarkup') > -1) {
                if(this.rightContentActionParams.name){
                    if(this._isChatBotSectionShow){ // show the explore with ai button when create the markup
                        document.body.classList.remove('cognitive-cde-tab-active');
                        this._isChatBotSectionShow = false;
                    }
                    this.commonViewActionService.closeViewAction();
                }
            }
        });
    }

    checkRevValidForDownload() {
        this.util.ajax({
            url: ApiConstant.IS_DOCUMENT_DOWNLOAD_RESTRICTED,
            _cdnUrl: this.myConfig.downloadServiceURL,
            _dcId: this.myConfig.dcId || window['LOCAL_DC_ID'],
            data: {
                projectId: this.myConfig.projectId,
                revisionId: this.myConfig.revisionId
            },
            success: (response) => {
                let data = response.body;
                if (data.isRevValidForDownload) {
                    if(!this.fileData?.passwordProtected) {
                        this.setPrivateMessageAndShow("file-cannot-be-viewed-online");
                    }
                } else {
                    this.fileNotSupportedMsg = this.util.lang.get("The-file-you-are-trying-to-view-is-not-supported-for-online-viewing");
                }
            }
        });
    };

    assignActionCallbacks() {
        this.actionFnMap[DOC_ACTIONS.FOR_STATUS_CHANGE] = () => {
            if(this.isMobile) {
                this.performAction('status-change');
            } else {
                this.statusChange.open();
            }
        };
        this.actionFnMap[DOC_ACTIONS.FOR_DISTRIBUTION] = () => {
            this.performAction("file-distribution");
        };
        this.actionFnMap[DOC_ACTIONS.FOR_COMMENT] = () => {
            if (this.reviewEnabled) {
                this.commonViewActionService.openCommentReviewMarkupAction();
            } else {
                this.performAction("create-comment");
            }
        };
        this.actionFnMap[DOC_ACTIONS.FOR_COMMENT_COORDINATION] = () => {
            this.performAction("comment-coordination");
        };

        this.actionFnMap[DOC_ACTIONS.FOR_COMMENT_INCORP] = () => {
            this.performAction("comment-incorporation");
        };


        this.actionFnMap[DOC_ACTIONS.FOR_INFORMATION] = (action) => { };

        this.actionFnMap[DOC_ACTIONS.FOR_PUBLISHING] = (action) => {
            this.addUploadActivity({
                forPublish: true,
                singleUpload: true,
                title: this.util.lang.get('action_name_28'),
                params: {
                    projectId: this.myConfig.projectId,
                    folderId: this.myConfig.folderId,
                    documentId: this.myConfig.documentId,
                    revisionId: this.myConfig.revisionId,
                    fileType: '',
                    dcId: this.myConfig.dcId
                },
                excelImport: true
            });
        };
    }

    private isCognitiveCDEBtnShow(priv) {
        let isAdmin = window['CAN_ACCESS_ADMIN_COGNITIVE_CDE'] = this.util.hasAccess(priv, 'CAN_ACCESS_ADMIN_COGNITIVE_CDE');
        if(this.util.hasAccess(priv, 'CAN_ACCESS_COGNITIVE_CDE') || isAdmin){
            this.cogCdeService.fetchAIEnableProjects((resp)=>{
                if(typeof resp != 'boolean' && resp[0]?.projectId == this.myConfig.projectId && resp[0]?.progressStatus ==  VECTORIZATION_STATES.COMPLETED){
                    let plainFolderId = this.myConfig?.folderId?.split('$$')[0];
                    let folderData = resp.find(e => e.resourceTypeId == RESOURCE_TYPE_ID.FOLDER && e.resourceId == plainFolderId);
                    
                    if(folderData){
                        this._hasCognitiveCDEBtnVisible = true;
                        if(!window['cognitivCDESelectedAIModel']){ // hold selected ai model type
                            window['cognitivCDESelectedAIModel'] = {};
                        }
                        window['cognitivCDESelectedAIModel'][folderData?.projectId?.split('$$')[0]] = folderData?.provider;
                        this.openCognitiveCDECallback && this.openCognitiveCDECallback();
                        let projectData = {
                            projectID: this.myConfig?.projectId,
                            documentId: this.myConfig?.documentId,
                            folderId: this.myConfig?.folderId,
                            dcId: this.myConfig?.dcId,
                            projectName: this.myConfig?.projectName,
                            fileName: this.myConfig?.fileName,
                            fileNameWithExt: this.myConfig?.fileNameWithExt,
                        }
                        this.cogCdeService.setSelectedProject(projectData);
                    }
                }
            });
        }
    }

    async fetchAndSetPriv() {
        // Make forkJoin after the task is achieved..
        this.util.getUserAppPrivileges().then((userAppPrivileges) => {
            let url = window['location'].href;
            // Provide the drill-down chat section only when navigating from the Cognitive CDE.
            !this.isMobile && (url.indexOf('isCognitiveTabActive=true') > -1) && !window['USP']?.isProxyUserWorking && this.matchesFileType(this.cognitiveSupportFileTypes) && this.isCognitiveCDEBtnShow(userAppPrivileges);
            this.fileExtName = this.util.getExt(this.myConfig.fileNameWithExt).toLowerCase();
            let tempHiddenMicrosoftOfficeBtn = !(this.fileExtName === "docx" || this.fileExtName === "xlsx" || this.fileExtName === "pptx" || this.fileExtName === "wopitest");
            this.priv.hiddenMicrosoftOfficeBtn = tempHiddenMicrosoftOfficeBtn || !(this.util.hasAccess(userAppPrivileges, 'ENABLE_MICROSOFT_OFFICE') && this.myConfig.projectADriveCDEAddonPurchaseStatus);
        });

        this.util.getProjectPermission(this.myConfig.projectId, this.myConfig.dcId, (data) => {
            let privileges = data.privileges;

            if (window['SYSTEMPRIVILEGES'] && window['viewerProjectId']) {
                (window as any).SYSTEMPRIVILEGES.userPrivileges[window['viewerProjectId']] = data.privileges;
            }

            this.priv.canAccessAuditInfo = this.util.hasAccess(privileges, "PRIV_CAN_ACESS_AUDIT_INFO");
            this.canViewHistoricMarkups = this.util.hasAccess(privileges, 'CAN_VIEW_HISTORIC_MARKUPS');
            this.priv.canDownloadFile = this.util.hasAccess(privileges, "PRIV_CAN_DOWNLOAD_DOCUMENTS") && this.myConfig.userFolderPermission != AppConstant.FOLDER_VIEW_ONLY;
            this.priv.canPrint = this.util.hasAccess(privileges, "PRIV_CAN_PRINT_DOCUMENTS") && this.myConfig.userFolderPermission != AppConstant.FOLDER_VIEW_ONLY;

            this.fileExtName = this.util.getExt(this.myConfig.fileNameWithExt).toLowerCase();
            this.priv.disableMicrosoftOfficeBtn = !this.folderHasUploadPermission;

            this.priv.canCreateComment = this.util.hasAccess(privileges, "PRIV_CAN_CREATE_COMMENT");
            this.priv.canCreatePrivateComment = this.util.hasAccess(privileges, "CAN_CREATE_PRIVATE_COMMENTS");
            this.priv.markReviewPrivate = this.util.hasAccess(privileges, "MARK_REVIEW_PRIVATE");
            if (this.util.isLocked(this.myConfig.lockActivityIds, "ACTIVITY_ADD_COMMENT") || !this.myConfig.isActive) {
                this.priv.canCreateComment = false;
            }
            this.priv.canEditFileAttr = this.myConfig.isActive && this.util.hasAccess(privileges, "PRIV_ASSIGN_DOCUMENT_ATTRIBUTES");
            this.priv.canStartWorkflow = this.util.hasAccess(privileges, "PRIV_MANAGE_WORKFLOW_RULES");
            this.priv.canManageReviewFlags = this.util.hasAccess(privileges, "CAN_MANAGE_REVIEW_FLAGS");
            this.priv.hasPrivChangeStatus = this.util.hasAccess(privileges, "PRIV_CHANGE_STATUS");
            if(this.myConfig.isOfflineMode) {
                this.onComplete();
            }
        });

        this.fetchProjectDetails();

        this.fetchAdditionalFileDetails()
    }

    /**
     * Fetches additional file data (e.g., DocuSign integration flag) for the current document and project.
     * Updates the component state or shows an error notification on failure.
     * 
     * @private
     * @memberof FileViewerComponent
     */
    private fetchAdditionalFileDetails() {
        this.util.ajax({
            url: ApiConstant.HOME_CONTROLLER,
            data: {
                action_id: AppConstant.GET_ADDITIONAL_FILE_VIEW_DATA,
                documentId: this.myConfig.documentId,
                projectId: this.myConfig.projectId,
            },
            success: (response) => {
                const resp = response.body;
                if (resp) {
                    this.enableDocusignIntegration = resp.enableDocusignIntegration || false;
                }
            },
            error: (err) => {
                this.enableDocusignIntegration = false;
                this.util.notification.error({ 
                    msg: this.util.lang.get("file-details-error-msg")
                });
            },
            offline: (err) => {
                this.enableDocusignIntegration = false;
                this.util.notification.error({
                    msg: this.util.lang.get("file-details-error-msg")
                });
            }
        });
    }

    fetchProjectDetails() {
        if (this.myConfig.isOfflineMode || (!this.myConfig.isFromAdrive && !this.myConfig.isFolderActive)) {
            return;
        }
        let param = {};

        if (this.myConfig.isFromAdrive) {
            param = {
                action_id: AppConstant.GET_ADRIVE_FILE_VIEW_ATTRIBUTES_DETAILS,
                projectId: this.myConfig.projectId,
                latestRevId: this.myConfig.revisionId
            }
        } else {
            param = {
                action_id: AppConstant.GET_FILE_VIEW_ATTRIBUTES_DETAILS,
                documentId: this.myConfig.documentId,
                projectId: this.myConfig.projectId,
                latestRevId: this.myConfig.revisionId,
                folderId: this.myConfig.folderId,
                docTypeId: this.myConfig.documentTypeId,
                viewAlwaysDocAssociation: this.myConfig.viewAlwaysDocAssociation
            }
        }
        if (this.myConfig.formTypeIdForDocView) {
            param['formTypeId'] = this.myConfig.formTypeIdForDocView;
        }

        this.util.ajax({
            url: ApiConstant.DOCUMENT_CONTROLLER,
            data: param,
            _cdnUrl: window['TabServices'] && (window as any).getTabServiceUrl(window['TabServices'].FILES),
            success: (response) => {
                this.fileData = response.body;
                if (!this.myConfig.isOfflineMode) {
                    this.fetch();

                    setTimeout(() => {
                        this.changeDetectorRef.detectChanges();
                        if(this.mediaFileViewer && this.mediaFileViewer.nativeElement) {
                            this.mediaFileViewer.nativeElement.append.apply(this.mediaFileViewer.nativeElement, document.querySelectorAll('#mediaContent > div'));
                            const element = document.getElementById('videoWrapper') as HTMLElement || null;
                            if(element){
                                element.click();
                            }
                        }

                        if(!this.fileData.passwordProtected && this.hoopsWebSsrViewer && this.hoopsWebSsrViewer.nativeElement) {
                            this.hoopsWebSsrViewer.nativeElement.append.apply(this.hoopsWebSsrViewer.nativeElement, document.querySelectorAll('#hoopswebviewer > div'));
                        }
                    })
                }
            },
            error: (err) => {
                this.util.notification.error({ msg: this.util.lang.get("file-details-error-msg") });
            }
        });
    }

    fetch() {
        if (this.fileData.allRevisionList && this.fileData.allRevisionList.elementVOList) {
            let revisionList = this.fileData.allRevisionList.elementVOList;
            let foundRevision = revisionList.find(revision => revision.revisionId === this.myConfig.revisionId);
            let checkoutFileDisable = foundRevision.checkOutStatus && !foundRevision.hasOfficeFile;
            if (!this.priv.hiddenMicrosoftOfficeBtn) {
                let officeFileDetails = this.fileFormUtilService.getOfficeFileDetails(this.fileExtName);
                let checkFileSize = officeFileDetails?.msFileSizeInMB > this.convertKBtoMB(foundRevision.fileSize);
                this.priv.checkedWithMSFileSize = !checkFileSize;
                this.priv.disableMicrosoftOfficeBtn = this.priv.disableMicrosoftOfficeBtn || !checkFileSize || foundRevision.isLink || !foundRevision.isLatest || checkoutFileDisable || !foundRevision.isActive;
                this.microsoftBtnTitle = officeFileDetails?.btnName;
                this.microsoftBtnIcon = officeFileDetails?.fileTypeIcon;
            }
        }

        if (this.myConfig.viewerPreference == 7) {
            this.clearForInfoFromFileViewer(true);
        }


        if (this.myConfig.isFromAdrive) {
            if (this.fileData && this.fileData.data && this.fileData.data.length) {
                let revData = this.fileData.data[0];
                revData.publishDate && (revData.displayCreatedDate = revData.publishDate.split('#')[0].replace(',', '').replaceAll('-', ' '));
                revData.fileArcXhr = false;
                this.fileData.currentRevision = window['aDriveFileObj'].fileRevisionList = this.fileData.data[0];
                this.showInfoMsgForOldRev();
                if (revData.passwordProtected) {
                    this.setPrivateMessageAndShow('password-protected-notification');
                }

                this.fileData.downloadOpt = {
                    selectedFiles: [{
                        fileName: this.fileData.currentRevision.fileName,
                        revisionId: this.fileData.currentRevision.revisionId,
                        isLock: this.fileData.currentRevision.isLock ? "true" : "",
                        dcId: this.fileData.currentRevision.dcId,
                        projectId: this.fileData.currentRevision.projectId,
                        folderId: this.fileData.currentRevision.folderId,
                        docId: this.fileData.currentRevision.documentId
                    }],
                    currentEntityType: ATTACH_ASSOC_LABEL_MAP.FILES,
                    selectedFilesAdditionalData: [{
                        hasOnlineViewerSupport: this.fileData.currentRevision.hasOnlineViewerSupport
                    }]
                };

                // parent.postMessage(JSON.stringify({
                //   event: 'fileRevisionListsLoaded',
                //   data: window['aDriveFileObj'].fileRevisionList
                // }), "*");
            }
            return;
        }

        this.fileData.allRevisionList.elementVOList = this.fileData.allRevisionList.elementVOList || [];

        if (this.myConfig.isOfflineMode) {
            this.fileData.currentRevision = this.fileData.allRevisionList.elementVOList[0];
            this.fileData.commentParam = {
                docRevisionId: this.myConfig.revisionId,
                doctId: this.myConfig.documentId,
                folder_id: this.myConfig.folderId,
                project_id: this.myConfig.projectId,
                isOnlyPlaceholderSelected: this.myConfig.documentTypeId == AppConstant.DOC_TYPE_ID_PLACEHOLDER,
                isOnlyDocSelected: this.myConfig.documentTypeId == AppConstant.DOC_TYPE_ID_NORMAL,
                isAssocMarkups: this.myConfig.isFromFileViewForDomain ? true : false,
                isAssocViews: this.myConfig.model_id ? true : false,
                'action_id': AppConstant.SAVE_COMMENT
            };
        } else {
            this.fileData.currentRevision = this.findCurrentRevId() || {};
            if (!this.fileData.currentRevision.status && this.fileData.StandardAttributes?.status) {
                this.fileData.currentRevision.status = this.fileData.StandardAttributes.status;
            }
            if ((this.fileData.currentRevision.workflowStatusVO && this.fileData.currentRevision.workflowStatusVO.workflowStatusId == 1) || !this.priv.canStartWorkflow || !this.fileData.currentRevision.isActive || this.fileData.currentRevision.documentTypeId == DOC_TYPE_ID.PLACEHOLDER ) {
                this.showStartWorkflow = false;
            }
            this.onComplete();
            this.updatePublishRevisionDisabled(this.fileData.allRevisionList.elementVOList);
        }

        if (this.myConfig.hideElements == "true") {
            this.hideElements = true;
        }

        if (this.myConfig.viewerTypeNoServer != 'null') {
            let notSupportedExtList = [];
            let allRevExtList = [];
            let noSuppCount = 0;
            for (let i = 0; i < this.fileData.allRevisionList.elementVOList.length; i++) {
                this.fileData.allRevisionList.elementVOList[i].compareExt = false;
                let fileExtName = this.fileData.allRevisionList.elementVOList[i].uploadFilename.split('.')[1] ?? this.fileData.allRevisionList.elementVOList[i].fileName.split('.')[1];
                allRevExtList.push(fileExtName);
            }
            let mainFileExt = this.myConfig.fileNameWithExt.split('.')[1];
            notSupportedExtList = this.myConfig.viewerTypeNoServer.split(',');
            for (let i = 0; i < allRevExtList.length; i++) {
                let ext = allRevExtList[i];
                for (var j = 0; j < notSupportedExtList.length; j++) {
                    if (notSupportedExtList[j] == mainFileExt.toUpperCase()) {
                        this.noCompareFileMain = true;
                    }
                    if (notSupportedExtList[j] == ext.toUpperCase()) {
                        this.fileData.allRevisionList.elementVOList[i].compareExt = true;
                        noSuppCount++;
                    }
                }
            }
            if (allRevExtList.length == noSuppCount + 1) {
                this.compareFlag++;
            }
        }

        this.getAppliedPolicyDetails();
    }

    getAppliedPolicyDetails() {
        this.util.fetchAppliedPolicyDetails({
            projectId: this.myConfig.projectId,
            resourceId: this.myConfig.documentId,
            resourceTypeId: '2'
        }, this.myConfig.dcId, this.callbackPolicyDetailsFile);
    };

    callbackPolicyDetailsFile(data) {
        if (data) {
            let policyInfo = {
                message: this.util.lang.get("applied-policy-msg-for-file").replace("${date}", data.plannedDate)
            }
            this.util.adoddleNotification({ type: 4, msg: policyInfo.message, autoHide: true, hideCloseIcon: true, fadeOutDelayTime: 5000, fadeOutTime: 0 });
            window['policyInfo'] = policyInfo.message;
        }
    }

    trackByRevisionId(rev) {
        return rev.revisionId;
    }

    findCurrentRevId() {
        return this.fileData.allRevisionList.elementVOList.find((data) => data.revisionId === this.myConfig.revisionId);
    }

    checkCanChangeStatus() {
        let isChangeStatusLock = this.util.isLocked(this.myConfig.lockActivityIds, "ACTIVITY_UPDATE_STATUS");
        return this.fileData.currentRevision.documentTypeId != 2 && !isChangeStatusLock && (this.priv.hasPrivChangeStatus || this.hasPendingStatusChange());
    };

    hasPendingStatusChange() {
        if(this.fileData.currentRevision.actions) {
            let filteredData = this.fileData.currentRevision.actions.filter((data) => {
                return data.actionId == DOC_ACTION_MAP.FOR_STATUS_CHANGE && data.actionStatus == 0;
            });
    
            return filteredData.length;
        }
        return false;
    };

    onComplete() {
        if (!this.fileData) {
            return;
        }

        this.fileData.downloadOpt = {
            selectedFiles: [{
                fileName: this.fileData.currentRevision.fileName,
                revisionId: this.fileData.currentRevision.revisionId,
                isLock: this.fileData.currentRevision.isLock ? "true" : "",
                dcId: this.fileData.currentRevision.dcId,
                projectId: this.fileData.currentRevision.projectId,
                folderId: this.fileData.currentRevision.folderId,
                docId: this.fileData.currentRevision.documentId
            }],
            currentEntityType: ATTACH_ASSOC_LABEL_MAP.FILES,
            selectedFilesAdditionalData: [{
                hasOnlineViewerSupport: this.fileData.currentRevision.hasOnlineViewerSupport
            }]
        };

        this.priv.canChangeStatus = !this.myConfig.isProjectArchived && this.myConfig.isActive && this.checkCanChangeStatus();

        this.fileData.commentParam = {
            docRevisionId: this.myConfig.revisionId,
            doctId: this.myConfig.documentId,
            folder_id: this.myConfig.folderId,
            project_id: this.myConfig.projectId,
            isOnlyPlaceholderSelected: this.myConfig.documentTypeId == AppConstant.DOC_TYPE_ID_PLACEHOLDER,
            isOnlyDocSelected: this.myConfig.documentTypeId == AppConstant.DOC_TYPE_ID_NORMAL,
            isAssocMarkups: this.myConfig.isFromFileViewForDomain ? true : false,
            isAssocViews: this.myConfig.model_id ? true : false,
            'action_id': AppConstant.SAVE_COMMENT
        };

        if (this.myConfig.isActive) {
            if (this.util.isLocked(this.myConfig.lockActivityIds, "ACTIVITY_EDIT_ATTRIBUTES")) {
                this.priv.canEditFileAttr = false;
            } else if (!this.priv.canEditFileAttr) {
                //if user is folder admin or file publisher, then he/she can edit.
                if (this.fileData.currentRevision.isUserFolderAdmin || this.fileData.currentRevision.publisherId == this.myConfig.USP.userID) {
                    this.priv.canEditFileAttr = true;
                }
            }
            if (this.myConfig.isProjectArchived) {
                this.priv.canEditFileAttr = false;
            }
        }

        // TODO: this global variable is used in MarkupSaveDialog.js, do something about it
        if (!(window as any).objCreateMarkUp) {
            window['objCreateMarkUp'] = {};
        }
        (window as any).objCreateMarkUp.purposeOfIssue = this.fileData.currentRevision.purposeOfIssue;
        (window as any).objCreateMarkUp.status = this.fileData.currentRevision.status;

        this.fileData.currentRevision.type = "0";
        this.priv.canShareLink = this.priv.canDownloadFile && this.fileData.currentRevision.isActive;

        this.showInfoMsgForOldRev();

        if (this.fileData.currentRevision.passwordProtected) {
            this.setPrivateMessageAndShow('password-protected-notification');
            this.fileData.passwordProtected = this.fileData.currentRevision.passwordProtected;
            window['passwordProtectedFile'] = this.fileData.passwordProtected;
        }

        this.initComponent();

        this.$(window).trigger("onFileDataLoaded");
    };

    initComponent() {
        let toOpen = this.myConfig.toOpen;
        let isDockFound = localStorage.getItem('dock');

        if (this.reviewEnabled && this.myConfig?.userFolderPermission != '0' && this.myConfig?.userRevisionPermission != '0') {
            this.openReviewComment = true;
        }

        if (!toOpen || toOpen == 'FromFile') {
            if (this.myConfig.commentMsgIdToExpand || this.myConfig.isFromComm == 'true' || this.myConfig.isForViewComment == 'true') {
                toOpen = this.reviewEnabled ? 'createReview' : 'discussions';
            } else if (!(window['asiteFieldAppIds'] || [3]).includes(this.myConfig.applicationId) && !this.util.isMobile()) {
                if (isDockFound === "false") {
                    return;
                }
                if(this.myConfig?.userRevisionPermission != '0'){
                    toOpen = this.reviewEnabled ? 'createReview' : 'discussions';
                }
            }
        }

        if (toOpen == "forCommentCoordination") {
            let actions = this.fileData.currentRevision.actions;
            let action;
            for (let i = 0; i < actions.length; i++) {
                if (actions[i].actionId == DOC_ACTION_MAP.FOR_COMMENT_COORDINATION) {
                    action = actions[i];
                }
            }
            if (action) {
                // var fn = actionFnMap[action.actionId];
                // fn && fn(action);
                // open action panel in right side
            }
        }

        if(toOpen == 'ASSIGN_STATUS' && !this.isMobile) {
            toOpen = this.reviewEnabled ? 'createReview' : 'discussions';
            if(this.reviewEnabled){
                setTimeout(() => {
                    this.statusChange.open();
                }, 500);
            }
        }

        let componentName = INIT_COMPONENT_MAP[toOpen];
        if (!componentName) {
            return;
        }

        let obj: any = { name: componentName };
        if (componentName == 'discussions') {
            obj.stay = true;
        } else if (this.myConfig.historyType) {
            obj.data = { actionId: this.myConfig.historyType, revisionId: this.myConfig.revisionId };
        }

        setTimeout(() => {
            if (!isDockFound) {
                localStorage.setItem("dock", "true");
                obj.dock = true;
            }
            if (this.reviewEnabled && this.myConfig.reviewInstanceId) {
                return;
            }
            if (this.myConfig.isOfflineMode && this.myConfig.createdMarkupName) {
                let data = {
                    name: 'create-comment',
                    data: { markupName: this.myConfig.createdMarkupName },
                    isFromCreateMarkup: true,
                };
                this.performAction(data.name, data);
            } else if (this.myConfig.isDraftForComment) {
                // $rootScope.$broadcast('dd:open', { name: 'create-comment',isDraftSupportAvail: true, data : {'is_draft': true,'comment_msg_id': myConfig.commentMsgIdForDraft } });
            } else {
                obj.isDraftSupportAvail = true;
                obj.action_id = AppConstant.SAVE_COMMENT;
                this.performAction(obj.name, obj);
            }

            if(this.myConfig.toOpen == 'ASSIGN_STATUS') {
                this.statusChange.open();
            }
        }, 500);
    };

    showInfoMsgForOldRev() {
        let cv = this.fileData.currentRevision;
        if (!cv.isLatest) {
            this.util.ajax({
                url: ApiConstant.CHECK_USER_LATEST_REVISION_API,
                data: {
                    projectId: cv.projectId,
                    documentId: cv.documentId,
                    revisionId: cv.revisionId
                },
                success: (response) => {
                    let data = response.body;
                    let msg = '';
                    if (this.myConfig.viewFileWhenPlaceholder) {
                        msg = this.util.lang.get("placeholder-latest-version-file-alert-msg");
                    } else if (data.documentTypeId == 2) {
                        msg = this.util.lang.get("placeholder-oldest-version-file-alert-msg");
                    } else {
                        msg = this.util.lang.get("latest-version-file-alert-msg");
                        this.util.adoddleNotification({ type: 4, msg: msg, autoHide: false, hideCloseIcon: true });
                        return;
                    }
                    !data.isUserLatestRevision && this.util.adoddleNotification({ type: 4, msg: msg, autoHide: false, hideCloseIcon: true });
                }
            });
        }
    };

    updatePublishRevisionDisabled(revData) {
        if (revData.length == 0) {
            return;
        }

        let latestRev = revData[0];


        if (this.folderHasUploadPermission && latestRev.isLink) {
            this.publishRevisionDisabled = true;
            this.publishRevisionDisabledTitle = "publish-revision-disabled-tooltip-for-link-files";
            return;
        }

        if (this.folderHasUploadPermission && latestRev.hasOfficeFile && this.priv.hiddenMicrosoftOfficeBtn) {
            this.publishMSRevisionDisabled = false;
            this.publishRevisionDisabled = true;
            return;
        }

        if (this.folderHasUploadPermission) {
            this.publishRevisionDisabled = false;
            return;
        }

        if (!latestRev?.actions?.length) {
            this.publishRevisionDisabled = true;
            return;
        }

        if (latestRev.isLink) {
            this.publishRevisionDisabled = true;
            this.publishRevisionDisabledTitle = "publish-revision-disabled-tooltip-for-link-files";
            return;
        }

        let allHasPublishingAction = latestRev.actions.find((action) => {
            return action.actionStatus == 0 && action.actionId == DOC_ACTION_MAP.FOR_PUBLISHING;
        });

        this.publishRevisionDisabled = allHasPublishingAction ? false : true;
    };

    updateUrlParameter(uri, keys, values) {
        // remove the hash part before operating on the uri
        let i = uri.indexOf('#');
        let hash = i === -1 ? '' : uri.substr(i);
        uri = i === -1 ? uri : uri.substr(0, i);

        for (let i = 0; i < keys.length; i++) {
            let re = new RegExp("([?&])" + keys[i] + "=.*?(&|$)", "i");
            let separator = uri.indexOf('?') !== -1 ? "&" : "?";
            if (uri.match(re)) {
                uri = uri.replace(re, '$1' + keys[i] + "=" + values[i] + '$2');
            } else {
                uri = uri + separator + keys[i] + "=" + values[i];
            }
        }
        return uri + hash;  // finally append the hash as well
    }

    navigateToLatestRev() {
        this.util.ajax({
            url: ApiConstant.DOCUMENT_CONTROLLER,
            data: {
                action_id: AppConstant.GET_FILE_VIEW_ATTRIBUTES_DETAILS,
                documentId: this.myConfig.documentId,
                projectId: this.myConfig.projectId,
                latestRevId: this.myConfig.revisionId,
                folderId: this.myConfig.folderId,
                docTypeId: this.myConfig.documentTypeId,
                viewAlwaysDocAssociation: this.myConfig.viewAlwaysDocAssociation
            },
            success: (response) => {
                let data = response.body || {};
                let revData = data.allRevisionList.elementVOList || [];
                let latestRev = revData[0];
                window.location.replace(this.updateUrlParameter(window.location.href, [
                    'revisionId',
                    'folderId',
                    'projectId'
                ], [
                    latestRev.revisionId,
                    latestRev.folderId,
                    latestRev.projectId
                ]));
            }
        });
    }

    publishRevisionClicked() {
        if (!this.publishRevisionDisabled) {
            if (!this.priv.hiddenMicrosoftOfficeBtn) {
                this.fileFormUtilService.publishRevisionMSFile(this.fileData.currentRevision, () => {
                    this.informParent();
                    this.navigateToLatestRev();
                });
            }

            this.publishRevisionFileInput.nativeElement.click();
        }
    }

    setPrivateMessageAndShow(message) {
        this.fileNotSupportedButCanDownloadMsg = this.util.lang.get(message);
    }

    clearForInfoFromFileViewer(silent?: boolean) {
        if (!this.myConfig.userFolderPermission || this.myConfig.userFolderPermission === '0' || !this.myConfig.userRevisionPermission || this.myConfig.userRevisionPermission === '0') {
            return;
        }
        this.util.ajax({
            url: ApiConstant.ACTIONS_CONTROLLER,
            data: {
                projectId: this.myConfig.projectId,
                action_id: AppConstant.DOCUMENT_COMPLETE_FOR_INFORMATION,
                isForDownload: false,
                revisionId: this.myConfig.revisionId
            },
            _cdnUrl: window['TabServices'] && (window as any).getTabServiceUrl(window['TabServices'].FILES),
            success: (response) => {
                if (response.body.isActionCompleted) {
                    if(!silent) {
                        this.informParent();
                    }
                    this.commonViewActionService.updateActionList(DOC_ACTIONS.FOR_INFORMATION);
                }
            }
        });
    };

    editInMSOffice() {
        if (this.priv.disableMicrosoftOfficeBtn) {
            return;
        }

        let params = {
            uploadFilename: this.myConfig.fileNameWithExt,
            projectId: this.myConfig.projectId,
            folderId: this.myConfig.folderId,
            revisionId: this.myConfig.revisionId,
            documentId: this.myConfig.documentId,
            dcId: this.myConfig.dcId,
        }

        this.fileFormUtilService.openInMSOffice(params, () => { });
    };

    convertKBtoMB(fileSizeInKB) {
        let fileSizeInMB = fileSizeInKB / 1024;
        return fileSizeInMB;
    }

    closeRightPanelIfUndocked() {
        let isDockFound = localStorage.getItem('dock');
        if (this.rightContentActionParams.name && (isDockFound == 'false')) {
            if(this._isChatBotSectionShow) { // not close right section if chat bot is opened
                return;
            }
            this.commonViewActionService.closeViewAction();
        }
    }

    hideSideBar() {
        let isDockFound = localStorage.getItem('dock');
        return (this.rightContentActionParams.name && (isDockFound == 'true' || this._isChatBotSectionShow) && this.rightContentActionParams.name != 'status-change');
    }

    informParent() {
        let updateDetail = {};
        updateDetail[ENTITY_TYPE.FILE] = this.myConfig.revisionId;
        this.util.informParentWin(updateDetail);
    }

    switchRevision(revData) {
        let data: any = {
            projectId: revData.projectId,
            revisionId: revData.revisionId,
            documentId: revData.documentId,
            folderId: revData.folderId,
            hasOnlineViewerSupport: revData.hasOnlineViewerSupport,
            toOpen: "FromFile",
            dcId: revData.dcId,
            documentTypeId: revData.documentTypeId,
            viewerId: revData.viewerId,
            isFromFileViewForDomain: "true",
            isActive: revData.isActive
        };

        if (this.myConfig.applicationId) {
            data.applicationId = this.myConfig.applicationId;
        }

        this.util.viewFile(data, revData.dcId, null);
    };

    printFile() {
        this.printFileModalService.start({
            params: {
                projectId: this.myConfig.projectId,
                folderId: this.myConfig.folderId,
                dcId: this.myConfig.dcId,
                documentTypeId: this.myConfig.documentTypeId,
                selectedFiles: [this.fileData.currentRevision]
            }
        })
    }

    saveAsPdf() {
        this.util.saveAsPDF(this.fileData.currentRevision, this.myConfig.applicationId);
    }

    navigateToFolder() {
        if(this.isAsiteCDEApp) {
            return;
        }
        
        localStorage.setItem("isFromLogin", "true");
        let assocDocProjectId = this.fileData.currentRevision.projectId;

        this.util.ajax({
            url: ApiConstant.ASITE_POPUP_CONTROLLER,
            data: {
                action_id: AppConstant.GET_PROJECT_LIST_POPUP,
                recordBatchSize: 10
            },
            success: (response) => {
                let data = response.body;
                if (data?.data?.length) {
                    let isProjectAvailable = false, selectedProjectList = [], proData = data.data;
                    let selectedProjectData = proData.filter((val, i) => {
                        return val.isSelected;
                    });
                    for (let i = 0; i < selectedProjectData.length; i++) {
                        selectedProjectList.push(selectedProjectData[i].id);
                        if (selectedProjectData[i].id.toString().split("$$")[0] == assocDocProjectId.toString().split("$$")[0]) {
                            isProjectAvailable = true;
                            break;
                        }
                    }
                    if (!isProjectAvailable) {
                        selectedProjectList.push(assocDocProjectId);
                        this.util.ajax({
                            url: ApiConstant.PROJECTS_CONTROLLER,
                            data: {
                                action_id: AppConstant.SAVE_USER_SEL_PROJECTS,
                                selFilterIds: selectedProjectList.join(","),
                            },
                            success: (response) => {
                                this.$('#frmOpenContFold').submit();
                            }
                        });
                    } else {
                        this.$('#frmOpenContFold').submit();
                    }
                }
            }
        });
    }

    navigateToDashboard() {
        localStorage.setItem("isFromLogin", "true");
        (window as any).location = "/adoddle/home?action_id=1#";
    }

    markOffline() {
        let str = "removeOffline:" + JSON.stringify(this.myConfig);
        if (this.myConfig.isMarkOffline) {
            this.util.sendEventToIpad(str);
        } else {
            str = "markOffline:" + JSON.stringify(this.myConfig);
            this.util.sendEventToIpad(str);
        }
    }

    downloadZip() {
        let downloadParam: any = {};
        downloadParam.fileName = this.myConfig.fileNameWithExt;
        downloadParam.dcId = this.myConfig.dcId;
        downloadParam.projectId = this.myConfig.projectId;
        downloadParam.folderId = this.myConfig.folderId;
        downloadParam.documentId = this.myConfig.documentId;
        downloadParam.revisionId = this.myConfig.revisionId;

        this.util.sendEventToIpad("downloadZip:" + JSON.stringify(downloadParam));
    }

    associateForm() {
        if(this.isAsiteCDEApp) {
            return;
        }
        this.util.sendEventToIpad("associateForm");
    };

    editAttribute() {
        let param = {
            projectId: this.myConfig.projectId,
            folderIds: this.myConfig.folderId,
            revisionIds: this.myConfig.revisionId,
            dcId: this.myConfig.dcId,
        }
        this.editAttrService.start({
            params: param,
            includeNoti: true,
            onComplete: () => {
                this.informParent();
            }
        });
    }

    resizeHoopsViewer() {
        setTimeout(() => {
            if((window as any).is3DRepoViewer) {
                document.dispatchEvent(new CustomEvent("on-window-resize"));
				return;		
			}
            if((window as any).hwv) {
                (window as any).hwv.resizeCanvas();
                (window as any).hwv.markupManager.refreshMarkup((window as any).hwv.view);
            }
        }, 150);
    }

    performAction(actionName, obj?, force = false) {
        this.resizeHoopsViewer()
        if (this.rightContentActionParams.name) {
            let isSameMenuClicked = this.rightContentActionParams.name == actionName;
            if(this._isChatBotSectionShow){
                this.openCognitiveCDECallback = false;
                this._isChatBotSectionShow = false;
                document.body.classList.remove('cognitive-cde-tab-active');
            }
            this.commonViewActionService.closeViewAction();
            if (isSameMenuClicked && !force) {
                return;
            }
        }
        setTimeout(() => {
            this.rightContentActionParams.name = actionName;

            switch (actionName) {
                case "history":
                    this.rightContentActionParams.data = this.fileData.currentRevision;
                    this.rightContentActionParams.commonViewParams = {
                        targetBtn: 'file-action-btn',
                        ddName: 'file-history',
                        dialog: true,
                        dockable: true,
                        expandable: true,
                        autoClose: 'outsideClick',
                        appendTo: 'right-content'
                    };
                    this.rightContentActionParams.configData = {
                        viewerPreference: this.myConfig.viewerPreference,
                        hasHoopsViewerSupport: this.myConfig.hasHoopsViewerSupport,
                        userFolderPermission: this.myConfig.userFolderPermission,
                        applicationId: this.myConfig.applicationId,
                        fileName: this.myConfig.fileName,
                        documentId: this.myConfig.documentId,
                        projectId: this.myConfig.projectId,
                        folderId: this.myConfig.folderId,
                        docTypeId: this.myConfig.documentTypeId,
                        documentFolderPath: this.myConfig.documentFolderPath,
                        latestRevId: this.myConfig.revisionId,
                        userDateFormat: this.myConfig.userDateFormat,
                        isOfflineMode: this.myConfig.isOfflineMode,
                        profile: this.myConfig.profile,
                        dcId: this.myConfig.dcId,
                        historyType: this.myConfig.historyType,
                        enableDocusignIntegration: this.enableDocusignIntegration
                    };
                    break;
                case "discussions":
                    this.rightContentActionParams.commonViewParams = {
                        targetBtn: 'file-discussion-btn',
                        ddName: 'discussions',
                        dialog: true,
                        dockable: true,
                        expandable: true,
                        autoClose: 'outsideClick',
                        appendTo: 'right-content'
                    };
                    this.rightContentActionParams.data = {
                        fileDetail: this.fileData,
                        priv: this.priv,
                        myConfig: this.myConfig,
                        obj: obj
                    };
                    break;
                case 'file-distribution':
                    this.rightContentActionParams.data = {
                        'entityDetail': [this.fileData.currentRevision]
                    };
                    this.rightContentActionParams.commonViewParams = {
                        targetBtn: 'file-action-btn',
                        ddName: 'file-distribution',
                        dockable: true,
                        expandable: true,
                        autoClose: 'outsideClick',
                        appendTo: 'right-content'
                    };
                    if (this.isMobile) {
                        this.rightContentActionParams.commonViewParams.targetBtn = 'mobile-more-opt';
                    }
                    break;
                case "Share_Link":
                    this.rightContentActionParams.data = {
                        currentRevision: this.fileData.currentRevision,
                        isEdit: false,
                    };
                    if (obj.data) {
                        this.rightContentActionParams.data = { ...this.rightContentActionParams.data, ...obj.data };
                    }

                    this.rightContentActionParams.commonViewParams = {
                        targetBtn: 'file-action-btn',
                        ddName: 'share-link',
                        dialog: true,
                        dockable: true,
                        expandable: true,
                        autoClose: 'outsideClick',
                        appendTo: 'right-content'
                    };
                    break;
                case "Workflow_List":
                    this.rightContentActionParams.data = {
                        modelTypeId: 1,
                        configData: this.myConfig
                    };
                    this.rightContentActionParams.commonViewParams = {
                        targetBtn: 'file-action-btn',
                        ddName: 'workflow-list',
                        dialog: true,
                        dockable: true,
                        expandable: true,
                        autoClose: 'outsideClick',
                        appendTo: 'right-content'
                    };
                    break;
                case "file-info":
                    this.rightContentActionParams.data = {
                        fileDetail: this.fileData,
                        myConfig: { 'revisionId': this.myConfig.revisionId, 'applicationId': this.myConfig.applicationId }
                    };
                    this.rightContentActionParams.commonViewParams = {
                        targetBtn: 'file-action-btn',
                        ddName: 'file-info',
                        dialog: true,
                        dockable: true,
                        expandable: true,
                        autoClose: 'outsideClick',
                        appendTo: 'right-content'
                    };
                    break;
                case "create-comment":
                    this.rightContentActionParams.data = {
                        currentRevision: [{
                            ...this.fileData.currentRevision,
                            fileCommentData: {
                                fileDetail: { ...this.fileData, isDraftSupportAvail: true },
                                priv: this.priv,
                                myConfig: this.myConfig,
                                viewerPreference: this.myConfig.viewerPreference
                            }
                        }],
                        commentType: '1',
                        param: { 
                            'isAssocMarkups': this.fileData.commentParam.isAssocMarkups, 
                            'isAssocViews': this.fileData.commentParam.isAssocViews,
                            extraParam: obj
                        },
                        viewerPreference: this.myConfig.viewerPreference
                    };

                    if (obj?.data && obj.data?.currentRevision && obj.data?.currentRevision[0]?.data) {
                        this.rightContentActionParams.data = { ...this.rightContentActionParams.data, ...obj.data }
                    }
                    this.rightContentActionParams.commonViewParams = {
                        targetBtn: 'file-discussion-btn',
                        ddName: 'create-comment',
                        dialog: true,
                        dockable: true,
                        expandable: true,
                        autoClose: 'outsideClick',
                        appendTo: 'right-content'
                    };
                    break;
                case 'status-change':
                    if(this.isMobile) {
                        this.rightContentActionParams.data = [this.fileData.currentRevision];
                        this.rightContentActionParams.commonViewParams = {
                            targetBtn: 'status-change-btn',
                            ddName: 'status-change',
                            dockable: true,
                            autoClose: 'outsideClick',
                            appendTo: 'right-content',
                            placement: 'left'
                        };
                        return;
                    }
                    this.statusChange.open();
                    break;
                case "assoc-attach":
                    this.rightContentActionParams.data = {
                        myConfig: this.myConfig

                    }
                    this.rightContentActionParams.commonViewParams = {
                        targetBtn: 'file-action-btn',
                        ddName: 'assoc-attach',
                        dialog: true,
                        dockable: true,
                        expandable: true,
                        autoClose: 'outsideClick',
                        appendTo: 'right-content'
                    }
                    break;
                case 'comment-coordination':
                    this.rightContentActionParams.data = [this.fileData.currentRevision];
                    this.rightContentActionParams.commonViewParams = {
                        targetBtn: 'adoddle-file-action-btn',
                        ddName: 'comment-coordination',
                        dockable: true,
                        autoClose: 'outsideClick',
                        appendTo: 'right-content',
                        placement: 'left'
                    };
                case 'comment-incorporation':
                    this.rightContentActionParams.data = [this.fileData.currentRevision];
                    this.rightContentActionParams.fileData = [this.fileData];
                    this.rightContentActionParams.commonViewParams = {
                        targetBtn: 'adoddle-file-action-btn',
                        ddName: 'comment-incorporation',
                        dockable: true,
                        autoClose: 'outsideClick',
                        appendTo: 'right-content',
                        placement: 'left'
                    };
                    break;
                case 'cognitive_cde_chatbot':
                    if(!this.matchesFileType(this.cognitiveSupportFileTypes) || window['USP']?.isProxyUserWorking){ return; }
                    this.openCognitiveCDEChatBot();
                    break;
                default:
                    return;
            }
        }, 100);
    };

    private openCognitiveCDECallback:any = false;
    
    private openCognitiveCDEChatBot(){
        if(!this._hasCognitiveCDEBtnVisible){ this.openCognitiveCDECallback = this.openCognitiveCDEChatBot; return; }
        this._isChatBotSectionShow = true;
        window['cognitiveCDEHighlightData'] = null;
        window.postMessage({event: 'clearhighlights'}); // Handle this event in viewerConfig file
        document.body.classList.add('cognitive-cde-tab-active');
        let fileName = this.myConfig?.fileNameWithExt;
        let fileData = {
            fileProjectID: this.myConfig?.projectId , 
            documentId: this.myConfig?.documentId, 
            documentTypeId: this.myConfig?.documentTypeId, 
            revisionID: this.myConfig?.revisionId, 
            dc: this.myConfig?.dcId, 
            folderID: this.myConfig?.folderId,
            filename: this.myConfig?.fileNameWithExt,
            fileType: fileName?.slice(fileName?.lastIndexOf('.') + 1)?.toLowerCase()
        };

        this.rightContentActionParams.data = {
            projectDetails: { dcId: this.myConfig.dcId, projectID: this.myConfig.projectId, fileName: this.myConfig.fileName, projectName:this.myConfig.projectName, files: [fileData] },
            revisionID: this.myConfig.revisionId
        };
        this.rightContentActionParams.commonViewParams = {
            targetBtn: 'cognitive-cde-btn',
            ddName: 'file-info',
            dialog: true,
            notAllowDockUndock: true,
            dockable: true,
            expandable: true,
            autoClose: 'outsideClick',
            appendTo: 'right-content'
        };
    }

    openReviewCommentsDialog() {
        if(this._isChatBotSectionShow){
            document.body.classList.remove('cognitive-cde-tab-active');
            this._isChatBotSectionShow = false;
        }
        this.openCognitiveCDECallback = false;
        if (this.rightContentActionParams.name) {
            this.commonViewActionService.closeViewAction();
        }
        this.commonViewActionService.toggleSideBar();
    }

    openHelp(view) {
        this.util.loadHelpContent({
            name: view
        });
    }

    startWorkflow() {
        let fileDetail = [{
            projectId: this.myConfig.projectId,
            revId: this.myConfig.revisionId,
            fromFileView: true,
            isActive: this.myConfig.isActive
        }]
        this.startWorkFlowService.start(fileDetail, () => { })
    }

    startWatching() {
        this.fileData.watchDetails.isWatching = true;
        window['subscribeWatch']({
            watchingFrom: 'file',
            action: 'start',
            paramObj: [{
                projectId: this.fileData.currentRevision.projectId,
                revisionId: this.fileData.currentRevision.revisionId,
                documentId: this.fileData.currentRevision.documentId
            }],
            watchedByResourceTypeId: this.fileData.watchDetails.watchedByResourceTypeId,
            watchedId: this.fileData.watchDetails.watchedId,
            isBatch: false
        });
    }

    stopWatching() {
        this.fileData.watchDetails.isWatching = false;
        window['subscribeWatch']({
            watchingFrom: 'file',
            action: 'stop',
            paramObj: [{
                projectId: this.fileData.currentRevision.projectId,
                revisionId: this.fileData.currentRevision.revisionId,
                documentId: this.fileData.currentRevision.documentId
            }],
            watchedByResourceTypeId: this.fileData.watchDetails.watchedByResourceTypeId,
            watchedId: this.fileData.watchDetails.watchedId,
            isBatch: false
        });
    }
    
    /**
     * Initiates the DocuSign e-signing process for the current file revision.
     * Sends a request to the backend to get the DocuSign signing URL and opens it in a new tab.
     * Shows an error notification if the process fails.
     * @private
     * @memberof FileViewerComponent
     */
    _initiateSigningProcess(isViewMode){
        const selectedFile = this.fileData?.currentRevision;

        if(!selectedFile){
            return;
        }
        
        // Pre-open the window immediately to prevent popup blocking
        const signingWindow = window.open('about:blank', '_blank');
        if (!signingWindow) {
            return; // Popup blocked
        }
        
        const payload = {
            action_id: AppConstant.INITIATE_DOCUSIGN_E_SIGNING,
            projectId: selectedFile.projectId,
            revisionId: selectedFile.revisionId,
            folderId: selectedFile.folderId,
            fileName: selectedFile.uploadFilename || selectedFile.fileName,
            fileSizeInBytes: selectedFile.fileSizeInBytes,
            docusignAction: isViewMode ? 'VIEW_SIGNING' : 'INITIATE_SIGNING'
        }
        const actionText = isViewMode ? 'viewing e-signing document. Please try again.' : 'initiating e-signing process. Please try again.';
        
        this.util.ajax({
            url: ApiConstant.HOME_CONTROLLER,
            method: 'POST',
            data: payload,
            _dcId: selectedFile.dcId,
            success: (response) => {
                const resp = response?.body;
                
                if (!resp) {
                    signingWindow.close();
                    return;
                }
                if (!signingWindow.closed) {
                    signingWindow.location.href = resp;
                } else {
                    window.open(resp, '_blank');
                }
            },
            error: (err) => {
                signingWindow.close();
                const error = err.error;
                if(error.status == 400) {
                    this.util.notification.error({
                        theClass: 'notification-sm',
                        msg: error?.errorMsg,
                    });
                } else {
                    this.util.notification.error({
                        theClass: 'notification-sm',
                        msg:`An error occurred while ${actionText}`,
                    });
                }
                return;
            },
            offline: (err) => {
                signingWindow.close();
                this.util.notification.error({
                    theClass: 'notification-sm',
                    title: 'Error',
                    msg: `An error occurred while ${actionText}`
                });
                return;
            }
        });
    }

    compare(openDocData) {
        let hasDownloadRight = this.myConfig.userFolderPermission != AppConstant.FOLDER_VIEW_ONLY;

        let compareDocData = this.fileData.allRevisionList.elementVOList.find((elem) => {
            return elem.revisionId == this.myConfig.revisionId;
        });

        //Check for viewer support.
        if (!openDocData.hasOnlineViewerSupport || !compareDocData.hasOnlineViewerSupport || openDocData.documentTypeId !== 1 || compareDocData.documentTypeId !== 1) {
            this.util.notification.error(this.util.lang.get('msg-for-compare-document-not-supported'));
            return;
        }

        let param = {
            projectId: openDocData.projectId,
            action_id: AppConstant.COMPARE_FILES,
            proj_id: compareDocData.projectId,
            openDocId: openDocData.revisionId,
            compareDocId: compareDocData.revisionId,
            dcId: openDocData.dcId,
            docDisplayName: openDocData.uploadFilename,
            compareDocDisplayName: compareDocData.uploadFilename,
            hasDownloadRight: hasDownloadRight
        }

        this.util.ajax({
            url: ApiConstant.PUBLISH_PDF_CONTROLLER,
            data: param,
            _cdnUrl: window['TabServices'] && (window as any).getTabServiceUrl(window['TabServices'].FILES),
            success: (response) => {
                let viewer = response.body;

                if (openDocData.issueNo < compareDocData.issueNo) {
                    let existingDoc = compareDocData;
                    let comparingDoc = openDocData;
                    openDocData = existingDoc;
                    compareDocData = comparingDoc;
                }

                let data = {
                    "projectId": openDocData.projectId,
                    "proj_id": compareDocData.projectId,
                    "openDocId": openDocData.revisionId,
                    "compareDocId": compareDocData.revisionId,
                    "dcId": openDocData.dcId,
                    "docDisplayName": openDocData.uploadFilename,
                    "compareDocDisplayName": compareDocData.uploadFilename,
                    "hasDownloadRight": hasDownloadRight
                };

                let urlAction = '/adoddle/viewer/compareDocsHTMLViewer7.jsp';

                this.util.submitForm({
                    url: urlAction,
                    param: data,
                    target: '_compare_' + openDocData.revisionId + '_' + compareDocData.revisionId,
                    _dcId: openDocData.dcId
                })
            },
            error: (err) => {
                this.util.notification.error({ msg: this.util.lang.get("Error while getting viewer preference") });
            }
        });
    };

    openCreateFormDD() {
        this.loadCreateForm = true;
        setTimeout(() => {
            this.createFormDD.open();
        }, 200);
    }

}
