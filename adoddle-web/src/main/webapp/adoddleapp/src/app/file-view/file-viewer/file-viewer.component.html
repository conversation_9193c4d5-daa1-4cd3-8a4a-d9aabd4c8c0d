<div class="file-view-container folder-inactive" *ngIf="!myConfig.isFolderActive">
    <div class="page-wrapper">
        <div class="main-header">
        </div>
        <div class="main-container inactive-container">
            <p class="deactivate-msg">
                {{"file-not-available-because-of-deactivated-folder" | translate}}
            </p>
        </div>
    </div>
</div>

<div id="file-view-page" class="adrive-file-view" *ngIf="myConfig.isFromAdrive && fileData?.currentRevision">
    <adoddle-apryse-pdftron-viewer [myConfig]="myConfig" [priv]="priv"
        [downloadOpt]="fileData.downloadOpt"></adoddle-apryse-pdftron-viewer>
</div>

<div id="file-view-page" [ngClass]="{mobile:isMobile}" class="file-view-container adoddle-view"
    *ngIf="!myConfig.isFromAdrive && myConfig.isFolderActive && fileData?.currentRevision; else showLoading">
    <!-- Header Section STarts -->
    <main>
        <div class="main-header">

        <!-- Left Section of header starts -->
        <button class="btn btn-default dropdown-toggle pull-left go-back" type="button"
            *ngIf="isMobile && isAsiteFieldApp" title="{{'back' | translate}}" [attr.aria-label]="('back' | translate)"  [title]="('back' | translate)" (click)="goBackMobile();">
            <i class="fa fa-arrow-left" aria-hidden="true"></i>
        </button>

        <div class="left-header-content" *ngIf="fileData && fileData?.StandardAttributes">
            <div class="calWidth docRefDropDown titleContainer" ngbDropdown #docRefPopup="ngbDropdown"
                (mouseenter)="docRefPopup.open()" (mouseleave)="docRefPopup.close()" (click)="docRefPopup.open()">
                <div class="titleContent">
                    <p id="fileName" ngbDropdownToggle  tabindex="0" adoddleHandleEnterKeydown  (click)="rightContentActionParams.name != 'file-info' && performAction('file-info', $event)"
                        title="{{'clickToViewMore' | translate}}">{{fileData.StandardAttributes.docRef}}</p>
                </div>
                <div class="docRefpopover" ngbDropdownMenu>
                    <strong>{{'upload_filename' | translate}}:</strong>
                    <span
                        title="{{fileData.StandardAttributes.fileName}}">{{fileData.StandardAttributes.fileName}}</span>
                    <br>
                    <strong>{{'path' | translate}}:</strong>
                    <span title="{{fileData.basicDetails.filePath}}">{{fileData.basicDetails.filePath}}</span>
                    <br>
                    <strong>{{'poi' | translate}}:</strong>
                    <span title="{{fileData.StandardAttributes.poi}}">{{fileData.StandardAttributes.poi}}</span>
                    <br>
                    <strong>{{'title' | translate}}:</strong>
                    <span title="{{fileData.StandardAttributes.title}}">{{fileData.StandardAttributes.title}}</span>
                    <br>
                    <a adoddleHandleEnterKeydown tabindex="0" (click)="rightContentActionParams.name != 'file-info' && performAction('file-info', $event)"
                        title="{{'clickToViewMore' | translate}}">{{'showMore' | translate}}</a>
                </div>
            </div>
            <div *ngIf="!simpleFileView && !isMobile" ngbDropdown #statusChange="ngbDropdown">
                <a id="status-change-btn" ngbDropdownToggle class="btn calWidth" (click)="priv.canChangeStatus && commonViewActionService.closeTheDDIfUndocked()"
                    *ngIf="fileData.currentRevision.status && !isMobile" type="button"
                    role="button" adoddleHandleEnterKeydown tabindex="0" title="{{'status-change' | translate}}">
                    {{'status' | translate}}: <span>{{fileData.currentRevision.status}}</span>
                    <span *ngIf="priv.canChangeStatus" class="caret" aria-hidden="true"></span>
                </a>
                <adoddle-file-status-change class="file-status-header" *ngIf="priv.canChangeStatus" ngbDropdownMenu [isOpenFromFileView]="true" (taskCompleted)="statusChange.close()" (taskCanceled)="statusChange.close()" [data]="[fileData.currentRevision]"></adoddle-file-status-change>
            </div>
            <div class="display-inline-block calWidth"
                *ngIf="fileData.currentRevision.actions && fileData.currentRevision.actions.length"
                id="adoddle-file-action-btn">
                <adoddle-file-actions class="file-action-component" [actions]="fileData.currentRevision.actions"
                    [fileDetail]="fileData.currentRevision" [actionFnMap]="actionFnMap" [readOnly]="isProArchived"
                    isFrom="file-viewer"></adoddle-file-actions>
            </div>

            <div class="dropdown revision-dd calWidth display-inline-block" ngbDropdown
                *ngIf="fileData.allRevisionList.elementVOList?.length > 1 && !isMobile">
                <div *ngIf="!simpleFileView">
                    <button class="btn rev-btn dropdown-toggle" ngbDropdownToggle type="button"
                        title="{{'rev' | translate}}">
                        <span class="text-ellipsis"
                            [ngClass]="{italic: !fileData.currentRevision.isActive}">{{fileData.currentRevision.revisionNum}}:
                            {{fileData.currentRevision.publishDate.split("#")[0]}}:
                            {{fileData.currentRevision.status}}</span>
                        <span class="caret" aria-hidden="true"></span>
                    </button>
                </div>
                <ul class="dropdown-menu dropdown-menu-left" ngbDropdownMenu>
                    <li *ngFor="let revData of fileData.allRevisionList.elementVOList">
                        <a role="button" adoddleHandleEnterKeydown tabindex="0" (click)="revData != fileData.currentRevision && switchRevision(revData)"
                            [ngClass]="{italic: !revData.isActive}"><strong>{{revData.revisionNum}}:</strong>
                            {{revData.publishDate.split("#")[0]}}: {{revData.status}}</a>
                    </li>
                </ul>
            </div>

            <div *ngIf="!simpleFileView">
                <button class="btn rev-btn text-ellipsis calWidth" type="button" tabindex="-1"
                    *ngIf="fileData.allRevisionList.elementVOList.length == 1 && !isMobile">{{'rev' | translate}}:
                    <span>{{fileData.currentRevision.revisionNum}}:
                        {{fileData.currentRevision.publishDate.split("#")[0]}}:
                        {{fileData.currentRevision.status}}</span>
                </button>

                <button class="btn title-info-btn text-ellipsis calWidth" type="button" tabindex="-1"
                    *ngIf="fileData && !isMobile" title="{{fileData.StandardAttributes.title}}">{{'title' | translate}}:
                    <span>{{fileData.StandardAttributes.title}}</span>
                </button>
            </div>
        </div>
        <!-- Left Section of header Ends -->

        <!-- Right Section of header starts -->
        <div class="right-header-content mobile-more-opt" *ngIf="isMobile" [ngClass]="{'hidden': rightContentActionParams.name}">
            <!-- display-inline-block pull-right -->
            <div *ngIf="!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission == 0">
                <div *ngIf="myConfig.isActive">
                    <!-- Create Comment -->
                    <!-- <custom-common-dropdown
                        *ngIf="priv.canCreateComment && fileData" 
                        dd-name="create-comment" target-btn="file-discussion-btn"
                        dd-title="{{'new-amessage' | translate}}"
                        dialog="true"
                        auto-close="outsideClick" append-to="right-content">	                       	
                        <create-comment param="fileData.commentParam" comment-type=1 on-success="commentCreatedSuccess(data, commentMsgId)"></create-comment>
                    </custom-common-dropdown> -->
                </div>
                <div *ngIf="myConfig.userRevisionPermission != 0 && !simpleFileView">
                    <button type="button" *ngIf="!reviewEnabled && fileData" class="btn btn-default button-toggle"
                        (click)="performAction('discussions')" title="{{'amessages' | translate}}" [attr.aria-label]="'amessages' | translate" id="file-discussion-btn">
                        <i class="fa ng-scope fa-comments-o" aria-hidden="true"></i>
                    </button>
                    <a role="button" adoddleHandleEnterKeydown tabindex="0" class="btn btn-default" *ngIf="reviewEnabled"
                        (click)="openReviewCommentsDialog()" title="{{'reviews' | translate}}" [attr.aria-label]="'reviews' | translate">
                        <i class="fa fa-comments-o" style="color:white" aria-hidden="true"></i>
                        <span id="review-unread-count" class="badge thread-unread-count"
                            [ngClass]="{'hidden': unreadReviewCounts < 1}">{{ unreadReviewCounts }}</span>
                    </a>
                </div>
            </div>

            <div class="dropdown display-inline-block" ngbDropdown adoddleNgbDropdownKeydownHandler [placement]="['bottom-right']" >
                <button class="btn btn-default dropdown-toggle action-dd" id="mobile-more-opt" ngbDropdownToggle
                    type="button" title="{{'more-options' | translate}}" [attr.aria-label]="'more-options' | translate">
                    <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                </button>
                <ul id="mobileFileMoreActionDD" class="dropdown-menu dropdown-menu-right" ngbDropdownMenu>

                    <div *ngIf="!simpleFileView && (!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0')">
                        <div class="submenu" 
                            *ngIf="!myConfig.isProjectArchived && myConfig.isActive && myConfig.userRevisionPermission != '0' && myConfig.fldV != '2' && isAsiteFieldApp && !isAsiteCDEApp">
                            <li>
                                <a role="button" ngbDropdownItem (click)="associateForm()"
                                    title="{{'associate-form' | translate}}">
                                    <svg aria-hidden="true" width="16" height="16" viewBox="0 0 16 16" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M5.50008 8.50016C5.50008 8.67697 5.42984 8.84654 5.30482 8.97157C5.17979 9.09659 5.01023 9.16683 4.83341 9.16683C4.6566 9.16683 4.48703 9.09659 4.36201 8.97157C4.23699 8.84654 4.16675 8.67697 4.16675 8.50016C4.16675 8.32335 4.23699 8.15378 4.36201 8.02876C4.48703 7.90373 4.6566 7.8335 4.83341 7.8335C5.01023 7.8335 5.17979 7.90373 5.30482 8.02876C5.42984 8.15378 5.50008 8.32335 5.50008 8.50016Z"
                                            fill="#222222" />
                                        <path
                                            d="M3.58341 1.66675C3.07508 1.66675 2.58757 1.86868 2.22813 2.22813C1.86868 2.58757 1.66675 3.07508 1.66675 3.58341V10.7501C1.66675 11.2584 1.86868 11.7459 2.22813 12.1054C2.58757 12.4648 3.07508 12.6667 3.58341 12.6667H10.7501C11.2584 12.6667 11.7459 12.4648 12.1054 12.1054C12.4648 11.7459 12.6667 11.2584 12.6667 10.7501V3.58341C12.6667 3.07508 12.4648 2.58757 12.1054 2.22813C11.7459 1.86868 11.2584 1.66675 10.7501 1.66675H3.58341ZM6.33342 8.50008C6.33342 8.89791 6.17538 9.27944 5.89407 9.56074C5.61277 9.84205 5.23124 10.0001 4.83341 10.0001C4.43559 10.0001 4.05406 9.84205 3.77275 9.56074C3.49145 9.27944 3.33341 8.89791 3.33341 8.50008C3.33341 8.10226 3.49145 7.72073 3.77275 7.43942C4.05406 7.15812 4.43559 7.00008 4.83341 7.00008C5.23124 7.00008 5.61277 7.15812 5.89407 7.43942C6.17538 7.72073 6.33342 8.10226 6.33342 8.50008ZM3.33341 5.08341C3.33341 4.85341 3.52008 4.66675 3.75008 4.66675H10.5834C10.6939 4.66675 10.7999 4.71065 10.878 4.78879C10.9562 4.86693 11.0001 4.97291 11.0001 5.08341C11.0001 5.19392 10.9562 5.2999 10.878 5.37804C10.7999 5.45618 10.6939 5.50008 10.5834 5.50008H3.75008C3.52008 5.50008 3.33341 5.31341 3.33341 5.08341ZM7.41675 8.16675H10.5834C10.6939 8.16675 10.7999 8.21065 10.878 8.28879C10.9562 8.36693 11.0001 8.47291 11.0001 8.58342C11.0001 8.69392 10.9562 8.7999 10.878 8.87804C10.7999 8.95618 10.6939 9.00008 10.5834 9.00008H7.41675C7.30624 9.00008 7.20026 8.95618 7.12212 8.87804C7.04398 8.7999 7.00008 8.69392 7.00008 8.58342C7.00008 8.47291 7.04398 8.36693 7.12212 8.28879C7.20026 8.21065 7.30624 8.16675 7.41675 8.16675Z"
                                            fill="#222222" />
                                        <path
                                            d="M5.08331 14.1666C4.77154 14.1669 4.46443 14.0909 4.18867 13.9455C3.9129 13.8 3.67684 13.5894 3.50098 13.332C3.52831 13.333 3.55564 13.3333 3.58331 13.3333H10.75C11.4351 13.3333 12.0922 13.0611 12.5767 12.5767C13.0611 12.0922 13.3333 11.4351 13.3333 10.75V3.58331C13.3333 3.55598 13.3326 3.52831 13.332 3.50098C13.5894 3.67684 13.8 3.9129 13.9455 4.18867C14.0909 4.46443 14.1669 4.77154 14.1666 5.08331V10.75C14.1666 12.6366 12.6366 14.1666 10.75 14.1666H5.08331Z"
                                            fill="#222222" />
                                    </svg>
                                    {{'associate-form' | translate}}</a>
                            </li>
                        </div>

                        <div class="submenu"  *ngIf="myConfig.userRevisionPermission != '0'">
                            <li>
                                <a role="button" ngbDropdownItem (click)="performAction('assoc-attach',$event)"
                                    title="{{'attachments' | translate}} & {{'associations' | translate}}">
                                    <svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                        fill="none">
                                        <g clip-path="url(#clip0_180_233)">
                                            <path
                                                d="M10.9998 4.00008V11.6667C10.9998 13.1401 9.8065 14.3334 8.33317 14.3334C6.85984 14.3334 5.6665 13.1401 5.6665 11.6667V3.33341C5.6665 2.41341 6.41317 1.66675 7.33317 1.66675C8.25317 1.66675 8.99984 2.41341 8.99984 3.33341V10.3334C8.99984 10.7001 8.69984 11.0001 8.33317 11.0001C7.9665 11.0001 7.6665 10.7001 7.6665 10.3334V4.00008H6.6665V10.3334C6.6665 11.2534 7.41317 12.0001 8.33317 12.0001C9.25317 12.0001 9.99984 11.2534 9.99984 10.3334V3.33341C9.99984 1.86008 8.8065 0.666748 7.33317 0.666748C5.85984 0.666748 4.6665 1.86008 4.6665 3.33341V11.6667C4.6665 13.6934 6.3065 15.3334 8.33317 15.3334C10.3598 15.3334 11.9998 13.6934 11.9998 11.6667V4.00008H10.9998Z"
                                                fill="#222222" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_180_233">
                                                <rect width="16" height="16" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    {{'attachments' | translate}} & {{'associations' | translate}}
                                </a>
                            </li>
                        </div>

                        <div class="submenu" *ngIf="myConfig.isActive && !myConfig.isProjectArchived && 
                                    !myConfig.lockActivityIds.includes(AppConstant.ACTIVITY_FILE_DISTRIBUTION) &&
                                    myConfig.userRevisionPermission != '0' && myConfig.fldV != '2'">

                            <li *ngIf="priv.canDistributeFile && fileData">
                                <a class="" role="button" ngbDropdownItem
                                    (click)="performAction('file-distribution',$event)"
                                    title="{{'distribute-file' | translate}}">
                                    <svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                        fill="none">
                                        <g clip-path="url(#clip0_180_291)">
                                            <path
                                                d="M1.34016 14L15.3335 8L1.34016 2L1.3335 6.66667L11.3335 8L1.3335 9.33333L1.34016 14Z"
                                                fill="#222222" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_180_291">
                                                <rect width="16" height="16" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    {{'distribute-file' | translate}}
                                </a>
                            </li>
                        </div>
                    </div>

                    <div class="submenu"
                        *ngIf="myConfig.userRevisionPermission != '0' && priv.canDownloadFile">
                        <li>
                            <a role="button" ngbDropdownItem (click)="downloadZip()">
                                <i class="fa fa-download" aria-hidden="true"></i>{{'download' | translate}}
                            </a>
                        </li>
                    </div>

                    <div *ngIf="!simpleFileView">
                        <div class="submenu" *ngIf="(!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0') &&
                                    myConfig.isActive && !myConfig.isProjectArchived && 
                                    !myConfig.lockActivityIds.includes(AppConstant.ACTIVITY_EDIT_ATTRIBUTES) &&
                                    myConfig.userRevisionPermission != '0'" [hidden]="!priv.canEditFileAttr">
                            <li>
                                <a role="button" ngbDropdownItem (click)="editAttribute()"
                                    title="{{'edit-attributes' | translate}}">
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16" viewBox="0 0 16 16"
                                        fill="none">
                                        <g clip-path="url(#clip0_181_372)">
                                            <path
                                                d="M14.6668 16.0001H1.3335V13.3335H14.6668V16.0001ZM8.70683 3.46012L11.2068 5.96012L5.16683 12.0001H2.66683V9.50012L8.70683 3.46012ZM11.9202 5.24679L9.42016 2.74679L10.6402 1.52679C10.9002 1.26679 11.3202 1.26679 11.5802 1.52679L13.1402 3.08679C13.4002 3.34679 13.4002 3.76679 13.1402 4.02679L11.9202 5.24679Z"
                                                fill="#222222" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_181_372">
                                                <rect width="16" height="16" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    {{'edit-attributes' | translate}}
                                </a>
                            </li>
                        </div>

                        <div class="submenu" *ngIf="fileData">
                            <li>
                                <a role="button" ngbDropdownItem (click)="performAction('file-info', $event)"
                                    title="{{'file-details' | translate }}" [ngClass]="{'hide':hideElements}">
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16" viewBox="0 0 16 16"
                                        fill="none">
                                        <path
                                            d="M7.3335 11.3333H8.66683V7.33325H7.3335V11.3333ZM8.00016 5.99992C8.18905 5.99992 8.3475 5.93592 8.4755 5.80792C8.6035 5.67992 8.66727 5.5217 8.66683 5.33325C8.66683 5.14436 8.60283 4.98592 8.47483 4.85792C8.34683 4.72992 8.18861 4.66614 8.00016 4.66659C7.81127 4.66659 7.65283 4.73059 7.52483 4.85859C7.39683 4.98659 7.33305 5.14481 7.3335 5.33325C7.3335 5.52214 7.3975 5.68059 7.5255 5.80859C7.6535 5.93659 7.81172 6.00036 8.00016 5.99992ZM8.00016 14.6666C7.07794 14.6666 6.21127 14.4915 5.40016 14.1413C4.58905 13.791 3.8835 13.3161 3.2835 12.7166C2.6835 12.1166 2.20861 11.411 1.85883 10.5999C1.50905 9.78881 1.33394 8.92214 1.3335 7.99992C1.3335 7.0777 1.50861 6.21103 1.85883 5.39992C2.20905 4.58881 2.68394 3.88325 3.2835 3.28325C3.8835 2.68325 4.58905 2.20836 5.40016 1.85859C6.21127 1.50881 7.07794 1.3337 8.00016 1.33325C8.92239 1.33325 9.78905 1.50836 10.6002 1.85859C11.4113 2.20881 12.1168 2.6837 12.7168 3.28325C13.3168 3.88325 13.7919 4.58881 14.1422 5.39992C14.4924 6.21103 14.6673 7.0777 14.6668 7.99992C14.6668 8.92214 14.4917 9.78881 14.1415 10.5999C13.7913 11.411 13.3164 12.1166 12.7168 12.7166C12.1168 13.3166 11.4113 13.7917 10.6002 14.1419C9.78905 14.4921 8.92239 14.667 8.00016 14.6666Z"
                                            fill="#222222" />
                                    </svg>
                                    {{'file-details' | translate }}
                                </a>
                            </li>
                        </div>

                        <div class="submenu"
                            *ngIf="(!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0') && myConfig.userRevisionPermission != '0' && priv.canAccessAuditInfo && fileData">
                            <li>
                                <a class="" role="button" ngbDropdownItem (click)="performAction('history', $event)"
                                    title="{{'history' | translate }}" [ngClass]="{'hide':hideElements}">
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="14" height="14" viewBox="0 0 14 14"
                                        fill="none">
                                        <path
                                            d="M7.00016 0.333252C3.3335 0.333252 0.333496 3.33325 0.333496 6.99992C0.333496 10.6666 3.3335 13.6666 7.00016 13.6666C10.6668 13.6666 13.6668 10.6666 13.6668 6.99992C13.6668 3.33325 10.6668 0.333252 7.00016 0.333252ZM9.80016 9.79992L6.3335 7.66659V3.66659H7.3335V7.13325L10.3335 8.93325L9.80016 9.79992Z"
                                            fill="#222222" />
                                    </svg>
                                    {{'history' | translate }}
                                </a>
                            </li>
                        </div>

                        <div class="submenu"
                            *ngIf="myConfig.userRevisionPermission != '0' && myConfig.fldV != '2' && isAsiteFieldApp && priv.canDownloadFile">
                            <li [hidden]="!!isMarkOffline">
                                <a role="button" ngbDropdownItem (click)="markOffline()"
                                    title="{{'mark-offline' | translate}}">
                                    <i class="fa fa-download" aria-hidden="true"></i>{{'mark-offline' | translate}}
                                </a>
                            </li>
                        </div>

                        <div class="submenu" *ngIf="myConfig.isFromDirectAccessLinkURL">
                            <li>
                                <a role="button" ngbDropdownItem (click)="navigateToDashboard()"
                                    title="{{'dashboard' | translate}}">
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16" viewBox="0 0 16 16"
                                        fill="none">
                                        <path
                                            d="M10.6668 13.3334H1.3335V2.66675H10.6668V13.3334ZM12.0002 5.33341H14.6668V2.66675H12.0002V5.33341ZM12.0002 13.3334H14.6668V10.6667H12.0002V13.3334ZM12.0002 9.33341H14.6668V6.66675H12.0002V9.33341Z"
                                            fill="#222222" />
                                    </svg>{{'dashboard' | translate}}
                                </a>
                            </li>
                        </div>

                        <div
                            *ngIf="(!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0') && myConfig.userRevisionPermission != '0' && myConfig.fldV != '2'">
                            <div class="submenu"  *ngIf="!isAsiteCDEApp">
                                <li>
                                    <a role="button" ngbDropdownItem (click)="navigateToFolder()"
                                        title="{{'navigate-to-folder' | translate}}">
                                        <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                            viewBox="0 0 16 16" fill="none">
                                            <g clip-path="url(#clip0_181_406)">
                                                <path
                                                    d="M13.3335 4.00008H8.00016L6.66683 2.66675H2.66683C1.9335 2.66675 1.3335 3.26675 1.3335 4.00008V12.0001C1.3335 12.7334 1.9335 13.3334 2.66683 13.3334H13.3335C14.0668 13.3334 14.6668 12.7334 14.6668 12.0001V5.33341C14.6668 4.60008 14.0668 4.00008 13.3335 4.00008ZM9.3335 12.0001V10.0001H6.66683V7.33341H9.3335V5.33341L12.6668 8.66675L9.3335 12.0001Z"
                                                    fill="#222222" />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_181_406">
                                                    <rect width="16" height="16" fill="white" />
                                                </clipPath>
                                            </defs>
                                        </svg>{{'navigate-to-folder' | translate}}
                                    </a>
                                </li>
                            </div>

                            <div class="submenu" >
                                <li>
                                    <a role="button" ngbDropdownItem [class.disabled]="publishRevisionDisabled"  (click)="publishRevisionClicked()"
                                        [disabled]="publishRevisionDisabled"
                                        title="{{(publishRevisionDisabled ? publishRevisionDisabledTitle : 'new-revision')  | translate }}">
                                        <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                            viewBox="0 0 16 16" fill="none">
                                            <g clip-path="url(#clip0_180_314)">
                                                <path
                                                    d="M11.9998 2.66659V1.99992C11.9998 1.63325 11.6998 1.33325 11.3332 1.33325H3.33317C2.9665 1.33325 2.6665 1.63325 2.6665 1.99992V4.66659C2.6665 5.03325 2.9665 5.33325 3.33317 5.33325H11.3332C11.6998 5.33325 11.9998 5.03325 11.9998 4.66659V3.99992H12.6665V6.66659H5.99984V13.9999C5.99984 14.3666 6.29984 14.6666 6.6665 14.6666H7.99984C8.3665 14.6666 8.6665 14.3666 8.6665 13.9999V7.99992H13.9998V2.66659H11.9998Z"
                                                    fill="#222222" />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_180_314">
                                                    <rect width="16" height="16" fill="white" />
                                                </clipPath>
                                            </defs>
                                        </svg>
                                        {{'new-revision' | translate}}
                                        <i class="fa fa-info-circle" *ngIf="publishRevisionDisabled"></i>
                                    </a>
                                </li>
                            </div>
                        </div>
                    </div>

                    <div class="submenu"
                        *ngIf="myConfig.userRevisionPermission != '0' && myConfig.applicationId == 1">
                        <li>
                            <a role="button" ngbDropdownItem [class.disabled]="!priv.canPrint" [disabled]="!priv.canPrint" (click)="printFile()"
                                title="{{'print-file' | translate}}">
                                <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16" viewBox="0 0 16 16"
                                    fill="none">
                                    <g clip-path="url(#clip0_180_317)">
                                        <path
                                            d="M12.6668 5.33333H3.3335C2.22683 5.33333 1.3335 6.22667 1.3335 7.33333V11.3333H4.00016V14H12.0002V11.3333H14.6668V7.33333C14.6668 6.22667 13.7735 5.33333 12.6668 5.33333ZM10.6668 12.6667H5.3335V9.33333H10.6668V12.6667ZM12.6668 8C12.3002 8 12.0002 7.7 12.0002 7.33333C12.0002 6.96667 12.3002 6.66667 12.6668 6.66667C13.0335 6.66667 13.3335 6.96667 13.3335 7.33333C13.3335 7.7 13.0335 8 12.6668 8ZM12.0002 2H4.00016V4.66667H12.0002V2Z"
                                            fill="#222222" />
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_180_317">
                                            <rect width="16" height="16" fill="white" />
                                        </clipPath>
                                    </defs>
                                </svg>
                                {{'print-file' | translate}}
                            </a>
                        </li>
                    </div>

                    <div *ngIf="!simpleFileView">
                        <div class="submenu" 
                            *ngIf="myConfig.userRevisionPermission != '0' && myConfig.fldV != '2' && isAsiteFieldApp">
                            <li [hidden]="!isMarkOffline">
                                <a role="button" ngbDropdownItem adoddleHandleEnterKeydown tabindex="0" (click)="markOffline()"
                                    title="{{'remove-offline' | translate}}">
                                    <i class="fa fa-times" aria-hidden="true"></i>{{'remove-offline' | translate}}</a>
                            </li>
                        </div>
                        <div *ngIf="!myConfig.viewAlwaysDocAssociation">
                            <div class="submenu" *ngIf="myConfig.applicationId == 1">
                                <li>
                                    <a role="button" ngbDropdownItem [class.disabled]="!priv.canDownloadFile" [disabled]="!priv.canDownloadFile"
                                        (click)="saveAsPdf()" title="{{'Save-as-pdf' | translate}}">
                                        <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                            viewBox="0 0 16 16" fill="none">
                                            <path
                                                d="M5.51118 9.78662C5.38852 9.78662 5.30585 9.79862 5.26318 9.81062V10.596C5.31385 10.608 5.37718 10.6113 5.46452 10.6113C5.78385 10.6113 5.98052 10.45 5.98052 10.1773C5.98052 9.93329 5.81118 9.78662 5.51118 9.78662ZM7.83585 9.79462C7.70252 9.79462 7.61585 9.80662 7.56452 9.81862V11.5586C7.61585 11.5706 7.69852 11.5706 7.77318 11.5706C8.31785 11.5746 8.67252 11.2746 8.67252 10.64C8.67652 10.0866 8.35318 9.79462 7.83585 9.79462Z"
                                                fill="#222222" />
                                            <path
                                                d="M9.33317 1.33325H3.99984C3.64622 1.33325 3.30708 1.47373 3.05703 1.72378C2.80698 1.97382 2.6665 2.31296 2.6665 2.66659V13.3333C2.6665 13.6869 2.80698 14.026 3.05703 14.2761C3.30708 14.5261 3.64622 14.6666 3.99984 14.6666H11.9998C12.3535 14.6666 12.6926 14.5261 12.9426 14.2761C13.1927 14.026 13.3332 13.6869 13.3332 13.3333V5.33325L9.33317 1.33325ZM6.33184 10.7933C6.12584 10.9866 5.82184 11.0733 5.46784 11.0733C5.3992 11.074 5.33059 11.07 5.2625 11.0613V12.0119H4.6665V9.38792C4.93547 9.3478 5.20725 9.32951 5.47917 9.33325C5.8505 9.33325 6.1145 9.40392 6.2925 9.54592C6.46184 9.68058 6.5765 9.90125 6.5765 10.1613C6.57584 10.4226 6.48917 10.6433 6.33184 10.7933ZM8.86984 11.6966C8.58984 11.9293 8.16384 12.0399 7.64317 12.0399C7.33117 12.0399 7.1105 12.0199 6.9605 11.9999V9.38859C7.22957 9.34931 7.50126 9.33081 7.77317 9.33325C8.27784 9.33325 8.60584 9.42392 8.86184 9.61725C9.1385 9.82258 9.31184 10.1499 9.31184 10.6199C9.31184 11.1286 9.12584 11.4799 8.86984 11.6966ZM11.3332 9.84659H10.3118V10.4539H11.2665V10.9433H10.3118V12.0126H9.70784V9.35325H11.3332V9.84659ZM9.33317 5.99992H8.6665V2.66659L11.9998 5.99992H9.33317Z"
                                                fill="#222222" />
                                        </svg>
                                        {{'Save-as-pdf' | translate}}
                                    </a>
                                </li>
                            </div>
                        </div>
                        <div *ngIf="!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0'">

                            <div class="submenu" ngbDropdownItem
                                *ngIf="myConfig.isActive && !myConfig.isProjectArchived && myConfig.userRevisionPermission != '0' && priv.canShareLink && fileData">
                                <li>
                                    <a class="" role="button" (click)="performAction('Share_Link',$event)"
                                        title="{{'share-link' | translate}}" [ngClass]="{'hide':hideElements}">
                                        <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                            viewBox="0 0 16 16" fill="none">
                                            <g clip-path="url(#clip0_180_294)">
                                                <path
                                                    d="M12 10.7199C11.4933 10.7199 11.04 10.9199 10.6933 11.2333L5.94 8.46659C5.97333 8.31325 6 8.15992 6 7.99992C6 7.83992 5.97333 7.68659 5.94 7.53325L10.64 4.79325C11 5.12659 11.4733 5.33325 12 5.33325C13.1067 5.33325 14 4.43992 14 3.33325C14 2.22659 13.1067 1.33325 12 1.33325C10.8933 1.33325 10 2.22659 10 3.33325C10 3.49325 10.0267 3.64659 10.06 3.79992L5.36 6.53992C5 6.20659 4.52667 5.99992 4 5.99992C2.89333 5.99992 2 6.89325 2 7.99992C2 9.10659 2.89333 9.99992 4 9.99992C4.52667 9.99992 5 9.79325 5.36 9.45992L10.1067 12.2333C10.0733 12.3733 10.0533 12.5199 10.0533 12.6666C10.0533 13.7399 10.9267 14.6133 12 14.6133C13.0733 14.6133 13.9467 13.7399 13.9467 12.6666C13.9467 11.5933 13.0733 10.7199 12 10.7199Z"
                                                    fill="#222222" />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_180_294">
                                                    <rect width="16" height="16" fill="white" />
                                                </clipPath>
                                            </defs>
                                        </svg>
                                        {{'share-link' | translate}}
                                    </a>
                                </li>
                            </div>

                            <div *ngIf="myConfig.userRevisionPermission != '0'">
                                <div class="submenu"  *ngIf="!fileData.watchDetails.isWatching">
                                    <li>
                                        <a role="button" ngbDropdownItem (click)="startWatching()"
                                            title="{{'startwatching' | translate}}">
                                            <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                                viewBox="0 0 16 16" fill="none">
                                                <g clip-path="url(#clip0_181_428)">
                                                    <path
                                                        d="M8.00016 1.33325C4.32016 1.33325 1.3335 4.31992 1.3335 7.99992C1.3335 11.6799 4.32016 14.6666 8.00016 14.6666C11.6802 14.6666 14.6668 11.6799 14.6668 7.99992C14.6668 4.31992 11.6802 1.33325 8.00016 1.33325ZM6.66683 10.9999V4.99992L10.6668 7.99992L6.66683 10.9999Z"
                                                        fill="#222222" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_181_428">
                                                        <rect width="16" height="16" fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                            {{'startwatching' | translate}}
                                        </a>
                                    </li>
                                </div>

                                <div class="submenu"  *ngIf="priv.canChangeStatus && myConfig.isActive">
                                    <li>
                                        <a role="button" ngbDropdownItem (click)="performAction('status-change',$event)"
                                            title="{{'status-change' | translate}}">
                                            <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="12"
                                                viewBox="0 0 16 12" fill="none">
                                                <path
                                                    d="M8.00512 4.39789C8.50157 4.39127 8.99438 4.48333 9.45495 4.66873C9.91553 4.85413 10.3347 5.12918 10.6881 5.4779C11.0415 5.82662 11.3221 6.24207 11.5136 6.70013C11.7052 7.15819 11.8038 7.64974 11.8038 8.14622C11.8038 8.64271 11.7052 9.13426 11.5136 9.59232C11.3221 10.0504 11.0415 10.4658 10.6881 10.8145C10.3347 11.1633 9.91553 11.4383 9.45495 11.6237C8.99438 11.8091 8.50157 11.9012 8.00512 11.8946C7.01966 11.8814 6.07902 11.4807 5.38679 10.7792C4.69456 10.0777 4.30644 9.13177 4.30644 8.14622C4.30644 7.16067 4.69456 6.21477 5.38679 5.51325C6.07902 4.81173 7.01966 4.41103 8.00512 4.39789ZM0.102455 3.81122L2.11579 6.19389C2.15234 6.23748 2.19726 6.27331 2.24789 6.29926C2.29851 6.3252 2.35383 6.34075 2.41056 6.34498C2.46729 6.3492 2.52429 6.34202 2.57821 6.32386C2.63212 6.3057 2.68185 6.27692 2.72446 6.23922C5.98112 3.31989 10.0378 3.31989 13.2845 6.23922C13.3272 6.27697 13.3771 6.30579 13.4312 6.32398C13.4853 6.34217 13.5424 6.34936 13.5993 6.34514C13.6562 6.34091 13.7117 6.32536 13.7625 6.29938C13.8133 6.27341 13.8584 6.23754 13.8951 6.19389L15.8991 3.81122C15.9721 3.72427 16.0077 3.61204 15.9983 3.49894C15.989 3.38584 15.9354 3.281 15.8491 3.20722C11.1158 -0.928775 4.87445 -0.928775 0.145789 3.20722C0.0611344 3.28231 0.00920925 3.38753 0.00111178 3.5004C-0.00698569 3.61327 0.0293869 3.72482 0.102455 3.81122Z"
                                                    fill="#222222" />
                                            </svg>
                                            <span>{{'status' | translate}}: {{fileData.currentRevision.status}}</span>
                                        </a>
                                        <a role="button" ngbDropdownItem adoddleHandleEnterKeydown tabindex="0"
                                            *ngIf="!priv.canChangeStatus && fileData.currentRevision.status" disabled>
                                            <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="12"
                                                viewBox="0 0 16 12" fill="none">
                                                <path
                                                    d="M8.00512 4.39789C8.50157 4.39127 8.99438 4.48333 9.45495 4.66873C9.91553 4.85413 10.3347 5.12918 10.6881 5.4779C11.0415 5.82662 11.3221 6.24207 11.5136 6.70013C11.7052 7.15819 11.8038 7.64974 11.8038 8.14622C11.8038 8.64271 11.7052 9.13426 11.5136 9.59232C11.3221 10.0504 11.0415 10.4658 10.6881 10.8145C10.3347 11.1633 9.91553 11.4383 9.45495 11.6237C8.99438 11.8091 8.50157 11.9012 8.00512 11.8946C7.01966 11.8814 6.07902 11.4807 5.38679 10.7792C4.69456 10.0777 4.30644 9.13177 4.30644 8.14622C4.30644 7.16067 4.69456 6.21477 5.38679 5.51325C6.07902 4.81173 7.01966 4.41103 8.00512 4.39789ZM0.102455 3.81122L2.11579 6.19389C2.15234 6.23748 2.19726 6.27331 2.24789 6.29926C2.29851 6.3252 2.35383 6.34075 2.41056 6.34498C2.46729 6.3492 2.52429 6.34202 2.57821 6.32386C2.63212 6.3057 2.68185 6.27692 2.72446 6.23922C5.98112 3.31989 10.0378 3.31989 13.2845 6.23922C13.3272 6.27697 13.3771 6.30579 13.4312 6.32398C13.4853 6.34217 13.5424 6.34936 13.5993 6.34514C13.6562 6.34091 13.7117 6.32536 13.7625 6.29938C13.8133 6.27341 13.8584 6.23754 13.8951 6.19389L15.8991 3.81122C15.9721 3.72427 16.0077 3.61204 15.9983 3.49894C15.989 3.38584 15.9354 3.281 15.8491 3.20722C11.1158 -0.928775 4.87445 -0.928775 0.145789 3.20722C0.0611344 3.28231 0.00920925 3.38753 0.00111178 3.5004C-0.00698569 3.61327 0.0293869 3.72482 0.102455 3.81122Z"
                                                    fill="#222222" />
                                            </svg>
                                            <span>{{'status' | translate}}: {{fileData.currentRevision.status}}</span>
                                        </a>
                                    </li>
                                </div>

                                <div class="submenu"  *ngIf="fileData.watchDetails.isWatching">
                                    <li>
                                        <a role="button" ngbDropdownItem (click)="stopWatching()"
                                            title="{{'stopwatching' | translate}}">
                                            <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="14" height="14"
                                                viewBox="0 0 14 14" fill="none">
                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                    d="M4.3335 9.66659H9.66683V4.33325H4.3335V9.66659ZM7.00016 0.333252C3.32016 0.333252 0.333496 3.31992 0.333496 6.99992C0.333496 10.6799 3.32016 13.6666 7.00016 13.6666C10.6802 13.6666 13.6668 10.6799 13.6668 6.99992C13.6668 3.31992 10.6802 0.333252 7.00016 0.333252Z"
                                                    fill="#222222" />
                                            </svg>
                                            {{'stopwatching' | translate}}
                                        </a>
                                    </li>
                                </div>

                                <div class="submenu" 
                                    *ngIf="!isAsiteFieldApp && priv.canAccessAuditInfo && fileData">
                                    <li>
                                        <a *ngIf="priv.canAccessAuditInfo && fileData" class=""
                                            role="button" ngbDropdownItem (click)="performAction('Workflow_List',$event)"
                                            title="{{'workflows' | translate }}" [ngClass]="{'hide':hideElements}">
                                            <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                                viewBox="0 0 16 16" fill="none">
                                                <g clip-path="url(#clip0_181_458)">
                                                    <path
                                                        d="M13.3335 3.99992H10.6668V2.66659C10.6668 1.92659 10.0735 1.33325 9.3335 1.33325H6.66683C5.92683 1.33325 5.3335 1.92659 5.3335 2.66659V3.99992H2.66683C1.92683 3.99992 1.34016 4.59325 1.34016 5.33325L1.3335 12.6666C1.3335 13.4066 1.92683 13.9999 2.66683 13.9999H13.3335C14.0735 13.9999 14.6668 13.4066 14.6668 12.6666V5.33325C14.6668 4.59325 14.0735 3.99992 13.3335 3.99992ZM9.3335 3.99992H6.66683V2.66659H9.3335V3.99992Z"
                                                        fill="#222222" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_181_458">
                                                        <rect width="16" height="16" fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                            {{'workflows' | translate }}
                                        </a>
                                    </li>
                                </div>
                            </div>
                        </div>
                    </div>
                </ul>
            </div>
        </div>


        <div class="right-header-content" *ngIf="!isMobile" [ngClass]="{'hidden': isFullScreen}">
            <div *ngIf="myConfig.documentTypeId != 2 || myConfig.viewFileWhenPlaceholder">
                <div
                    *ngIf="!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0'; else otherDownloadOpt">
                    <div *ngIf="simpleFileView; else nonSimpleFileDownload">
                        <div *ngIf="priv.canDownloadFile" class="file-download-pref-dd">
                            <adoddle-file-download-preference class="download-pref-header" [data]="fileData.downloadOpt"
                                [configData]="myConfig" [autoPref]="true" [labelDownloadPref]="'download-pref-header'" [isFromDocumentTemplate]="true"></adoddle-file-download-preference>
                        </div>
                    </div>
                </div>
                <ng-template #otherDownloadOpt>
                    <div class="file-download-pref-dd">
                        <adoddle-file-download-preference class="download-pref-header" [data]="fileData.downloadOpt"
                            [configData]="myConfig" [labelDownloadPref]="'file-download-preference-dd'"></adoddle-file-download-preference>
                    </div>
                </ng-template>
                <ng-template #nonSimpleFileDownload>
                    <div *ngIf="priv.canDownloadFile" class="btn-group file-download-pref-dd">
                        <adoddle-file-download-preference class="download-pref-header" [data]="fileData.downloadOpt"
                            [configData]="myConfig" [labelDownloadPref]="'download-pref-dd'"></adoddle-file-download-preference>
                    </div>
                </ng-template>
            </div>

            <div *ngIf="!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0'">
                <div *ngIf="myConfig.isActive">
                    <!-- Create Comment -->
                    <!-- <custom-common-dropdown
                    *ngIf="priv.canCreateComment && fileData" 
                    dd-name="create-comment" target-btn="file-discussion-btn"
                    dd-title="{{'new-amessage' | translate}}"
                    dialog="true" dockable="true" expandable="true"
                    auto-close="outsideClick" append-to="right-content">	                       	
                        <create-comment param="fileData.commentParam" comment-type=1 on-success="commentCreatedSuccess(data, commentMsgId)"></create-comment>
                    </custom-common-dropdown> -->
                </div>
                <div *ngIf="myConfig.userRevisionPermission != '0' && !simpleFileView">
                    <button type="button" *ngIf="!reviewEnabled && fileData" class="btn btn-default button-toggle"
                        (click)="performAction('discussions')" title="{{'amessages' | translate}}" [attr.aria-label]="'amessages' | translate" id="file-discussion-btn">
                        <i class="fa ng-scope fa-comments-o" aria-hidden="true"></i>
                    </button>
                    <a *ngIf="reviewEnabled" role="button" adoddleHandleEnterKeydown tabindex="0" class="btn btn-default"
                        (click)="openReviewCommentsDialog()" title="{{'reviews' | translate}}"
                        aria-label="{{'reviews' | translate}}">
                        <i class="fa fa-comments-o" style="color:white" aria-hidden="true"></i>
                        <span id="review-unread-count" class="badge thread-unread-count"
                            [ngClass]="{'hidden': unreadReviewCounts < 1}">{{ unreadReviewCounts }}</span>
                    </a>
                </div>
            </div>

            <div class="createFormDDMenu" *ngIf="fileData?.currentRevision && loadCreateForm" ngbDropdown #createFormDD="ngbDropdown"
                autoClose="outside" [placement]="['bottom-right']">
                <button ngbDropdownToggle hidden class="unobserved" id="createFormDDToggle" aria-hidden="true" tabindex="-1" aria-disabled="true"></button>
                <adoddle-create-form-action ngbDropdownMenu [selectedItems]="[fileData.currentRevision]"
                    [params]="{isFromWhere:6}" (close)="createFormDD.close()"></adoddle-create-form-action>
            </div>

            <span class="dropdown meeting-group display-inline-block" ngbDropdown [autoClose]="true"
                [ngClass]="{'hide': hideElements}" *ngIf="meetingData?.length">
                <button class="btn btn-default dropdown-toggle" [attr.aria-label]="'meeting-options' | translate" [attr.title]="'meeting-options' | translate" ngbDropdownToggle type="button"
                    aria-label="{{'meeting-options' | translate}}">
                    <i class="fa fa-video-camera display-inline-block" aria-hidden="true"></i>
                </button>
                <div class="dropdown-menu dropdown-menu-right" ngbDropdownMenu
                    [ngClass]="{'dropdown-menu-left': isMobile}">
                        <a *ngFor="let meetingType of meetingData" ngbDropdownItem href="{{meetingType.url}}?meetingTitle=Asite Meeting: {{fileData.StandardAttributes.docRef}}"
                            target="_blank"
                            title="{{(meetingType.languageKey || meetingType.labelText) | translate}}">
                            <img src="/images/{{meetingType.icon}}?ver={{staticContentVersion}}"
                                [alt]="((meetingType.languageKey || meetingType.labelText) | translate) +  '-' + fileData.StandardAttributes.docRef" />
                            {{(meetingType.languageKey || meetingType.labelText) | translate}}
                        </a>
                </div>
            </span>

            <span class="dropdown action-dd" ngbDropdown adoddleNgbDropdownKeydownHandler *ngIf="fileData" [ngClass]="{'hide':hideElements}"
                [placement]="['bottom-right']">
                <div *ngIf="simpleFileView">
                    <button class="btn btn-default" type="button" [disabled]="!priv.canPrint"
                        (click)="printFile()" title="{{'print-file' | translate}}" aria-label="{{'print-file' | translate}}">
                        <i class="fa fa-print" aria-hidden="true"></i>
                    </button>
                </div>
                <div *ngIf="!simpleFileView">
                    <button class="btn btn-default dropdown-toggle" ngbDropdownToggle type="button" id="file-action-btn"
                        title="{{'more-options' | translate}}" (click)="closeRightPanelIfUndocked()" aria-label="{{'more-options' | translate}}">
                        <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                    </button>
                </div>

                <ul id="fileMoreActionDD" class="dropdown-menu dropdown-menu-right" ngbDropdownMenu
                    [ngClass]="{'dropdown-menu-left': isMobile}">

                    <div *ngIf="(!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0')">
                        <div *ngIf="myConfig.isActive && !myConfig.isProjectArchived">
                            <div *ngIf="myConfig.userRevisionPermission != '0'">
                                <div *ngIf="(priv.canDistributeFile || priv.canShareLink) && fileData"
                                    class="dropdown-header" title="{{'share' | translate}}">{{'share' | translate}}
                                </div>
                                <div class="submenu" 
                                    *ngIf="!myConfig.lockActivityIds.includes(AppConstant.ACTIVITY_FILE_DISTRIBUTION)">
                                    <li>
                                        <a *ngIf="priv.canDistributeFile && fileData" class="" role="button" ngbDropdownItem
                                            (click)="performAction('file-distribution',$event)"
                                            title="{{'distribute-file' | translate}}">
                                            <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                                viewBox="0 0 16 16" fill="none">
                                                <g clip-path="url(#clip0_180_291)">
                                                    <path
                                                        d="M1.34016 14L15.3335 8L1.34016 2L1.3335 6.66667L11.3335 8L1.3335 9.33333L1.34016 14Z"
                                                        fill="#222222" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_180_291">
                                                        <rect width="16" height="16" fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                            {{'distribute-file' | translate}}
                                        </a>
                                    </li>
                                </div>
                                <div class="submenu"  *ngIf="priv.canShareLink && fileData">
                                    <li>
                                        <!-- Share-link -->
                                        <a class="" role="button" ngbDropdownItem
                                            (click)="performAction('Share_Link',$event)"
                                            title="{{'share-link' | translate}}" [ngClass]="{'hide': hideElements}">
                                            <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                                viewBox="0 0 16 16" fill="none">
                                                <g clip-path="url(#clip0_180_294)">
                                                    <path
                                                        d="M12 10.7199C11.4933 10.7199 11.04 10.9199 10.6933 11.2333L5.94 8.46659C5.97333 8.31325 6 8.15992 6 7.99992C6 7.83992 5.97333 7.68659 5.94 7.53325L10.64 4.79325C11 5.12659 11.4733 5.33325 12 5.33325C13.1067 5.33325 14 4.43992 14 3.33325C14 2.22659 13.1067 1.33325 12 1.33325C10.8933 1.33325 10 2.22659 10 3.33325C10 3.49325 10.0267 3.64659 10.06 3.79992L5.36 6.53992C5 6.20659 4.52667 5.99992 4 5.99992C2.89333 5.99992 2 6.89325 2 7.99992C2 9.10659 2.89333 9.99992 4 9.99992C4.52667 9.99992 5 9.79325 5.36 9.45992L10.1067 12.2333C10.0733 12.3733 10.0533 12.5199 10.0533 12.6666C10.0533 13.7399 10.9267 14.6133 12 14.6133C13.0733 14.6133 13.9467 13.7399 13.9467 12.6666C13.9467 11.5933 13.0733 10.7199 12 10.7199Z"
                                                        fill="#222222" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_180_294">
                                                        <rect width="16" height="16" fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                            {{'share-link' | translate}}
                                        </a>
                                    </li>
                                </div>
                                <div *ngIf="(priv.canDistributeFile || priv.canShareLink) && fileData" class="divider">
                                </div>
                            </div>
                        </div>

                        <div *ngIf="myConfig.userRevisionPermission != '0'">
                            <div class="dropdown-header" title="{{'new' | translate}}">{{'new' | translate}}</div>
                        </div>

                        <div class="submenu"  *ngIf="myConfig.isActive && !myConfig.isProjectArchived &&
                                    myConfig.userRevisionPermission != '0'&&
                                    !myConfig.lockActivityIds.includes(AppConstant.ACTIVITY_EDIT_ATTRIBUTES)">
                            <li>
                                <a *ngIf="priv.canEditFileAttr" class="" role="button" ngbDropdownItem
                                    (click)="editAttribute()" title="{{'edit-attributes' | translate}}"
                                    [ngClass]="{hide:hideElements}">
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16" viewBox="0 0 16 16"
                                        fill="none">
                                        <g clip-path="url(#clip0_181_372)">
                                            <path
                                                d="M14.6668 16.0001H1.3335V13.3335H14.6668V16.0001ZM8.70683 3.46012L11.2068 5.96012L5.16683 12.0001H2.66683V9.50012L8.70683 3.46012ZM11.9202 5.24679L9.42016 2.74679L10.6402 1.52679C10.9002 1.26679 11.3202 1.26679 11.5802 1.52679L13.1402 3.08679C13.4002 3.34679 13.4002 3.76679 13.1402 4.02679L11.9202 5.24679Z"
                                                fill="#222222" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_181_372">
                                                <rect width="16" height="16" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    {{'edit-attributes' | translate}}
                                </a>
                            </li>

                        </div>
                    </div>

                    <div class="submenu"  *ngIf="!priv.hiddenMicrosoftOfficeBtn">
                        <li>
                            <a class="open-in-microsoft-office" [class.disabled]="priv.disableMicrosoftOfficeBtn"  [disabled]="priv.disableMicrosoftOfficeBtn"
                                role="button" ngbDropdownItem (click)="editInMSOffice()"
                                title="{{(priv.disableMicrosoftOfficeBtn && priv.checkedWithMSFileSize ? 'the-file-size-exceeds-the-specified-microsoft-document-size' : 'tooltip-edit-file-in-microsoft-office-button') | translate}}">
                                <img src="{{microsoftBtnIcon}}" [alt]="'tooltip-edit-file-in-microsoft-office-button' | translate" />
                                {{microsoftBtnTitle}}
                                <i class="fa fa-info-circle" aria-hidden="true"
                                    title="{{'the-file-size-exceeds-the-specified-microsoft-document-size' | translate}}"
                                    *ngIf="priv.disableMicrosoftOfficeBtn && priv.checkedWithMSFileSize"></i>
                            </a>
                        </li>
                    </div>

                    <div *ngIf="!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0'">
                        <div class="submenu"  *ngIf="myConfig.isActive && !myConfig.isProjectArchived && 
                            (myConfig.applicationId != 2 && (myConfig.documentTypeId != 2 || myConfig.viewFileWhenPlaceholder )) &&
                            myConfig.userRevisionPermission != '0'">
                            <li>
                                <a class="create-form-button" role="button" ngbDropdownItem (click)="openCreateFormDD();"
                                    title="{{'new-form' | translate}}">
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16" viewBox="0 0 16 16"
                                        fill="none">
                                        <g clip-path="url(#clip0_180_337)">
                                            <path
                                                d="M9.33317 1.33325H3.99984C3.2665 1.33325 2.67317 1.93325 2.67317 2.66659L2.6665 13.3333C2.6665 14.0666 3.25984 14.6666 3.99317 14.6666H11.9998C12.7332 14.6666 13.3332 14.0666 13.3332 13.3333V5.33325L9.33317 1.33325ZM10.6665 11.9999H5.33317V10.6666H10.6665V11.9999ZM10.6665 9.33325H5.33317V7.99992H10.6665V9.33325ZM8.6665 5.99992V2.33325L12.3332 5.99992H8.6665Z"
                                                fill="#222222" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_180_337">
                                                <rect width="16" height="16" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    {{'new-form' | translate}}
                                </a>
                            </li>
                        </div>
                        <div class="submenu"  *ngIf="myConfig.userRevisionPermission != '0'">
                            <li>
                                <a class="publish-revision-link" [class.disabled]="publishRevisionDisabled" role="button" ngbDropdownItem
                                    (click)="publishRevisionClicked()" [disabled]="publishRevisionDisabled"
                                    title="{{ (publishRevisionDisabled && publishMSRevisionDisabled ? publishRevisionDisabledTitle : 'new-revision') | translate }}">
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16" viewBox="0 0 16 16"
                                        fill="none">
                                        <g clip-path="url(#clip0_180_314)">
                                            <path
                                                d="M11.9998 2.66659V1.99992C11.9998 1.63325 11.6998 1.33325 11.3332 1.33325H3.33317C2.9665 1.33325 2.6665 1.63325 2.6665 1.99992V4.66659C2.6665 5.03325 2.9665 5.33325 3.33317 5.33325H11.3332C11.6998 5.33325 11.9998 5.03325 11.9998 4.66659V3.99992H12.6665V6.66659H5.99984V13.9999C5.99984 14.3666 6.29984 14.6666 6.6665 14.6666H7.99984C8.3665 14.6666 8.6665 14.3666 8.6665 13.9999V7.99992H13.9998V2.66659H11.9998Z"
                                                fill="#222222" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_180_314">
                                                <rect width="16" height="16" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    {{'new-revision' | translate}}
                                    <i class="fa fa-info-circle" aria-hidden="true"
                                        *ngIf="publishRevisionDisabled && publishMSRevisionDisabled"></i>
                                </a>
                            </li>
                        </div>
                    </div>

                    <div *ngIf="myConfig.userRevisionPermission != '0'">
                        <div *ngIf="myConfig.viewAlwaysDocAssociation && myConfig.userFolderPermission == '0'">
                            <div *ngIf="showStartWorkflow" class="dropdown-header">{{'new' | translate}}</div>
                        </div>
                        <div *ngIf="fileData && enableDocusignIntegration && myConfig.documentTypeId != 2 && fileData?.currentRevision?.isActive && fileData?.currentRevision?.isLatest && !fileData?.currentRevision?.isLink && !fileData?.currentRevision?.isActivityLocked && !fileData?.currentRevision?.passwordProtected" class="submenu">
                            <li>
                                <a role="button" ngbDropdownItem (click)="_initiateSigningProcess(false)"
                                    title="{{'sign-with-docusign' | translate }}" [ngClass]="{'hide':hideElements}">
                                    <img class="esigning-img"  src="/images/contextmenuImg/signing.svg">
                                    <span>{{'sign-with-docusign' | translate }}</span>
                                </a>
                            </li>
                        </div>
                        <div class="submenu" *ngIf="showStartWorkflow">
                            <li>
                                <a role="button" ngbDropdownItem (click)="startWorkflow()"
                                    title="{{'start-workflow' | translate}}">
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16" viewBox="0 0 16 16"
                                        fill="none">
                                        <g clip-path="url(#clip0_181_467)">
                                            <path
                                                d="M10.6668 3.99992V2.66659C10.6668 1.92659 10.0735 1.33325 9.3335 1.33325H6.66683C5.92683 1.33325 5.3335 1.92659 5.3335 2.66659V3.99992H1.3335V12.6666C1.3335 13.4066 1.92683 13.9999 2.66683 13.9999H13.3335C14.0735 13.9999 14.6668 13.4066 14.6668 12.6666V3.99992H10.6668ZM6.66683 2.66659H9.3335V3.99992H6.66683V2.66659ZM6.00016 11.9999V5.99992L11.0002 8.66659L6.00016 11.9999Z"
                                                fill="#222222" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_181_467">
                                                <rect width="16" height="16" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    {{'start-workflow' | translate}}
                                </a>
                            </li>
                        </div>
                        <div *ngIf="fileData && enableDocusignIntegration && myConfig.documentTypeId != 2 && fileData?.currentRevision?.isActive && fileData?.currentRevision?.isLatest && !fileData?.currentRevision?.isLink && !fileData?.currentRevision?.isActivityLocked && !fileData?.currentRevision?.passwordProtected" class="submenu">
                            <li>
                                <a role="button" ngbDropdownItem (click)="_initiateSigningProcess(true)"
                                    title="{{'sign-with-docusign' | translate }}" [ngClass]="{'hide':hideElements}">
                                    <!-- todo: update image once provided by UX -->
                                    <img class="esigning-img"  src="/images/contextmenuImg/signing.svg">
                                    <span>{{'view-with-docusign' | translate }}</span>
                                </a>
                            </li>
                        </div>
                        <div class="divider"
                            *ngIf="(myConfig.viewAlwaysDocAssociation && myConfig.userFolderPermission == '0') && showStartWorkflow">
                        </div>
                    </div>

                    <div class="divider"
                        *ngIf="(!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0') && myConfig.userRevisionPermission != '0'">
                    </div>

                    <div class="dropdown-submenu-wrapper" ngbDropdown tabindex="0" 
                        *ngIf="(myConfig.viewerId != 2 && !myConfig.hasHoopsViewerSupport && !myConfig.has3DRepoViewerSupport) && (myConfig.userRevisionPermission != '0' && (myConfig.documentTypeId != 2 || myConfig.viewFileWhenPlaceholder)) ||
                        (!myConfig.viewAlwaysDocAssociation && (myConfig.documentTypeId != 2 || myConfig.viewFileWhenPlaceholder))">
                        <div class="submenu"
                            *ngIf="myConfig.applicationId == 1 && (myConfig.documentTypeId != 2 || myConfig.viewFileWhenPlaceholder )"
                            class="dropdown-header" title="{{'print' | translate}}">
                            {{'print' | translate}}
                            <svg class="right-arrow-icon" aria-hidden="true" width="16" height="16" viewBox="0 0 16 16" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_2080_274)">
                                    <path
                                        d="M5.72656 11.06L8.7799 8L5.72656 4.94L6.66656 4L10.6666 8L6.66656 12L5.72656 11.06Z"
                                        fill="#757575" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_2080_274">
                                        <rect width="16" height="16" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <div id="printSubmenu" class="dropdown-submenu">
                            <div class="submenu-right-arrow"></div>
                            <div class="dropdown-submenu-options">
                                <div class="submenu"  *ngIf="myConfig.userRevisionPermission != '0'">
                                    <li>
                                        <a class="print-file-link" role="button" ngbDropdownItem [class.disabled]="!priv.canPrint" [disabled]="!priv.canPrint"
                                            (click)="printFile()" title="{{'print-file' | translate}}"><svg
                                                xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                                viewBox="0 0 16 16" fill="none">
                                                <g clip-path="url(#clip0_180_317)">
                                                    <path
                                                        d="M12.6668 5.33333H3.3335C2.22683 5.33333 1.3335 6.22667 1.3335 7.33333V11.3333H4.00016V14H12.0002V11.3333H14.6668V7.33333C14.6668 6.22667 13.7735 5.33333 12.6668 5.33333ZM10.6668 12.6667H5.3335V9.33333H10.6668V12.6667ZM12.6668 8C12.3002 8 12.0002 7.7 12.0002 7.33333C12.0002 6.96667 12.3002 6.66667 12.6668 6.66667C13.0335 6.66667 13.3335 6.96667 13.3335 7.33333C13.3335 7.7 13.0335 8 12.6668 8ZM12.0002 2H4.00016V4.66667H12.0002V2Z"
                                                        fill="#222222" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_180_317">
                                                        <rect width="16" height="16" fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg> 
                                            {{'print-file' | translate}}
                                        </a>
                                    </li>
                                </div>
                                <div class="submenu" *ngIf="!myConfig.viewAlwaysDocAssociation">
                                    <li>
                                        <a class="save-as-pdf-link" role="button" ngbDropdownItem [class.disabled]="!priv.canDownloadFile"
                                            [disabled]="!priv.canDownloadFile" (click)="saveAsPdf()"
                                            title="{{'Save-as-pdf' | translate}}">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                                viewBox="0 0 16 16" fill="none">
                                                <path
                                                    d="M5.51118 9.78662C5.38852 9.78662 5.30585 9.79862 5.26318 9.81062V10.596C5.31385 10.608 5.37718 10.6113 5.46452 10.6113C5.78385 10.6113 5.98052 10.45 5.98052 10.1773C5.98052 9.93329 5.81118 9.78662 5.51118 9.78662ZM7.83585 9.79462C7.70252 9.79462 7.61585 9.80662 7.56452 9.81862V11.5586C7.61585 11.5706 7.69852 11.5706 7.77318 11.5706C8.31785 11.5746 8.67252 11.2746 8.67252 10.64C8.67652 10.0866 8.35318 9.79462 7.83585 9.79462Z"
                                                    fill="#222222" />
                                                <path
                                                    d="M9.33317 1.33325H3.99984C3.64622 1.33325 3.30708 1.47373 3.05703 1.72378C2.80698 1.97382 2.6665 2.31296 2.6665 2.66659V13.3333C2.6665 13.6869 2.80698 14.026 3.05703 14.2761C3.30708 14.5261 3.64622 14.6666 3.99984 14.6666H11.9998C12.3535 14.6666 12.6926 14.5261 12.9426 14.2761C13.1927 14.026 13.3332 13.6869 13.3332 13.3333V5.33325L9.33317 1.33325ZM6.33184 10.7933C6.12584 10.9866 5.82184 11.0733 5.46784 11.0733C5.3992 11.074 5.33059 11.07 5.2625 11.0613V12.0119H4.6665V9.38792C4.93547 9.3478 5.20725 9.32951 5.47917 9.33325C5.8505 9.33325 6.1145 9.40392 6.2925 9.54592C6.46184 9.68058 6.5765 9.90125 6.5765 10.1613C6.57584 10.4226 6.48917 10.6433 6.33184 10.7933ZM8.86984 11.6966C8.58984 11.9293 8.16384 12.0399 7.64317 12.0399C7.33117 12.0399 7.1105 12.0199 6.9605 11.9999V9.38859C7.22957 9.34931 7.50126 9.33081 7.77317 9.33325C8.27784 9.33325 8.60584 9.42392 8.86184 9.61725C9.1385 9.82258 9.31184 10.1499 9.31184 10.6199C9.31184 11.1286 9.12584 11.4799 8.86984 11.6966ZM11.3332 9.84659H10.3118V10.4539H11.2665V10.9433H10.3118V12.0126H9.70784V9.35325H11.3332V9.84659ZM9.33317 5.99992H8.6665V2.66659L11.9998 5.99992H9.33317Z"
                                                    fill="#222222" />
                                            </svg> 
                                            {{'Save-as-pdf' | translate}}
                                        </a>
                                    </li>
                                </div>
                            </div>
                        </div>
                        <div *ngIf="myConfig.applicationId == 1 && (myConfig.documentTypeId != 2 || myConfig.viewFileWhenPlaceholder)"
                            class="hide-divider"></div>
                    </div>

                    <div class="dropdown-submenu-wrapper" ngbDropdown tabindex="0">
                        <div class="dropdown-header" title="{{'more_info' | translate}}">
                            {{'more_info' | translate}}
                            <svg class="right-arrow-icon" aria-hidden="true" width="16" height="16" viewBox="0 0 16 16" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_2080_274)">
                                    <path
                                        d="M5.72656 11.06L8.7799 8L5.72656 4.94L6.66656 4L10.6666 8L6.66656 12L5.72656 11.06Z"
                                        fill="#757575" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_2080_274">
                                        <rect width="16" height="16" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <div id="moreInfoSubmenu" class="dropdown-submenu">
                            <div class="submenu-right-arrow"></div>
                            <ul class="dropdown-submenu-options">
                                <div class="submenu" 
                                    *ngIf="(!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0') && myConfig.userRevisionPermission != '0'">
                                    <li>
                                        <!-- File attach assoc -->
                                        <a class="" role="button" ngbDropdownItem
                                            (click)="performAction('assoc-attach',$event)"
                                            title="{{'attachments' | translate}} & {{'associations' | translate}}"
                                            [ngClass]="{'hide': hideElements}">
                                            <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                                viewBox="0 0 16 16" fill="none">
                                                <g clip-path="url(#clip0_180_233)">
                                                    <path
                                                        d="M10.9998 4.00008V11.6667C10.9998 13.1401 9.8065 14.3334 8.33317 14.3334C6.85984 14.3334 5.6665 13.1401 5.6665 11.6667V3.33341C5.6665 2.41341 6.41317 1.66675 7.33317 1.66675C8.25317 1.66675 8.99984 2.41341 8.99984 3.33341V10.3334C8.99984 10.7001 8.69984 11.0001 8.33317 11.0001C7.9665 11.0001 7.6665 10.7001 7.6665 10.3334V4.00008H6.6665V10.3334C6.6665 11.2534 7.41317 12.0001 8.33317 12.0001C9.25317 12.0001 9.99984 11.2534 9.99984 10.3334V3.33341C9.99984 1.86008 8.8065 0.666748 7.33317 0.666748C5.85984 0.666748 4.6665 1.86008 4.6665 3.33341V11.6667C4.6665 13.6934 6.3065 15.3334 8.33317 15.3334C10.3598 15.3334 11.9998 13.6934 11.9998 11.6667V4.00008H10.9998Z"
                                                        fill="#222222" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_180_233">
                                                        <rect width="16" height="16" fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                            {{'attachments' | translate}} & {{'associations' | translate}}
                                        </a>
                                    </li>
                                </div>

                                <div class="submenu sub-open-dropdown-menu"
                                    ngbDropdown #compareDropDown="ngbDropdown" 
                                        [placement]="['left-top']"
                                        (blur)="compareDropDown.close()"
                                        (mouseover)='compareDropDown.open()'
                                        (mouseout)='compareDropDown.close()'
                                        *ngIf="myConfig.applicationId != 2 && !myConfig.hasMediaFilesSupport && myConfig.documentTypeId != 2  && myConfig.viewerPreference == 7">
                                    <li *ngIf="fileData.allRevisionList.elementVOList.length > 1 && !noCompareFileMain && compareFlag == 0"
                                        class="compare-revision">
                                        <a (focus)="compareDropDown.open()" tabindex="0" title="{{'viewer-compare-file' | translate}}"  ngbDropdownToggle><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16"
                                                height="16" viewBox="0 0 16 16" fill="none">
                                                <path
                                                    d="M6.66667 15.3334V14.0001H2V2.00008H6.66667V0.666748H8V15.3334H6.66667ZM3.33333 12.0001H6.66667V8.00008L3.33333 12.0001ZM9.33333 14.0001V8.00008L12.6667 12.0001V3.33341H9.33333V2.00008H14V14.0001H9.33333Z"
                                                    fill="#222222" />
                                            </svg> {{'viewer-compare-file' | translate}}<svg width="16" height="16" class="right-arrow-icon" aria-hidden="true"
                                                viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <g clip-path="url(#clip0_2080_274)">
                                                    <path
                                                        d="M5.72656 11.06L8.7799 8L5.72656 4.94L6.66656 4L10.6666 8L6.66656 12L5.72656 11.06Z"
                                                        fill="#757575" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_2080_274">
                                                        <rect width="16" height="16" fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg></a>
                                        <ul id="compareRevisionSubmenu" class="dropdown-menu dropdown-menu-right"
                                            ngbDropdownMenu [ngClass]="{'dropdown-menu-left': isMobile}">
                                            <li
                                                *ngFor="let rev of fileData.allRevisionList.elementVOList;  trackBy: trackByRevisionId">
                                                <ng-container
                                                    *ngIf="rev.revisionId != currentRevisionId && !rev.compareExt">
                                                    <a role="button" ngbDropdownItem
                                                        (click)="compare(rev)">{{rev.revisionNum}} :
                                                        {{rev.publishDate.split("#")[0]}} :{{rev.status}}</a>
                                                </ng-container>
                                            </li>
                                        </ul>
                                    </li>
                                </div>

                                <div class="submenu" >
                                    <li>
                                        <a role="button" ngbDropdownItem (click)="navigateToDashboard()"
                                            title="{{'dashboard' | translate}}">
                                            <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                                viewBox="0 0 16 16" fill="none">
                                                <path
                                                    d="M10.6668 13.3334H1.3335V2.66675H10.6668V13.3334ZM12.0002 5.33341H14.6668V2.66675H12.0002V5.33341ZM12.0002 13.3334H14.6668V10.6667H12.0002V13.3334ZM12.0002 9.33341H14.6668V6.66675H12.0002V9.33341Z"
                                                    fill="#222222" />
                                            </svg>
                                            {{'dashboard' | translate}}
                                        </a>
                                    </li>
                                </div>
                                <div class="submenu"  *ngIf="fileData">
                                    <li>
                                        <a role="button" ngbDropdownItem (click)="performAction('file-info', $event)"
                                            title="{{'file-details' | translate }}" [ngClass]="{'hide':hideElements}">
                                            <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                                viewBox="0 0 16 16" fill="none">
                                                <path
                                                    d="M7.3335 11.3333H8.66683V7.33325H7.3335V11.3333ZM8.00016 5.99992C8.18905 5.99992 8.3475 5.93592 8.4755 5.80792C8.6035 5.67992 8.66727 5.5217 8.66683 5.33325C8.66683 5.14436 8.60283 4.98592 8.47483 4.85792C8.34683 4.72992 8.18861 4.66614 8.00016 4.66659C7.81127 4.66659 7.65283 4.73059 7.52483 4.85859C7.39683 4.98659 7.33305 5.14481 7.3335 5.33325C7.3335 5.52214 7.3975 5.68059 7.5255 5.80859C7.6535 5.93659 7.81172 6.00036 8.00016 5.99992ZM8.00016 14.6666C7.07794 14.6666 6.21127 14.4915 5.40016 14.1413C4.58905 13.791 3.8835 13.3161 3.2835 12.7166C2.6835 12.1166 2.20861 11.411 1.85883 10.5999C1.50905 9.78881 1.33394 8.92214 1.3335 7.99992C1.3335 7.0777 1.50861 6.21103 1.85883 5.39992C2.20905 4.58881 2.68394 3.88325 3.2835 3.28325C3.8835 2.68325 4.58905 2.20836 5.40016 1.85859C6.21127 1.50881 7.07794 1.3337 8.00016 1.33325C8.92239 1.33325 9.78905 1.50836 10.6002 1.85859C11.4113 2.20881 12.1168 2.6837 12.7168 3.28325C13.3168 3.88325 13.7919 4.58881 14.1422 5.39992C14.4924 6.21103 14.6673 7.0777 14.6668 7.99992C14.6668 8.92214 14.4917 9.78881 14.1415 10.5999C13.7913 11.411 13.3164 12.1166 12.7168 12.7166C12.1168 13.3166 11.4113 13.7917 10.6002 14.1419C9.78905 14.4921 8.92239 14.667 8.00016 14.6666Z"
                                                    fill="#222222" />
                                            </svg>
                                            {{'file-details' | translate }}
                                        </a>
                                    </li>
                                </div>
                                <div
                                    *ngIf="(!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0') && myConfig.userRevisionPermission != '0'">
                                    <div class="submenu" *ngIf="priv.canAccessAuditInfo && fileData">
                                        <li>
                                            <a class="" role="button" ngbDropdownItem
                                                (click)="performAction('history', $event)"
                                                title="{{'history' | translate }}" [ngClass]="{'hide':hideElements}">
                                                <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="14" height="14"
                                                    viewBox="0 0 14 14" fill="none">
                                                    <path
                                                        d="M7.00016 0.333252C3.3335 0.333252 0.333496 3.33325 0.333496 6.99992C0.333496 10.6666 3.3335 13.6666 7.00016 13.6666C10.6668 13.6666 13.6668 10.6666 13.6668 6.99992C13.6668 3.33325 10.6668 0.333252 7.00016 0.333252ZM9.80016 9.79992L6.3335 7.66659V3.66659H7.3335V7.13325L10.3335 8.93325L9.80016 9.79992Z"
                                                        fill="#222222" />
                                                </svg>
                                                {{'history' | translate }}
                                            </a>
                                        </li>
                                    </div>
                                    <div class="submenu" >
                                        <li>
                                            <a role="button" ngbDropdownItem (click)="navigateToFolder()"
                                                title="{{'navigate-to-folder' | translate}}"><svg
                                                    xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                                    viewBox="0 0 16 16" fill="none">
                                                    <g clip-path="url(#clip0_181_406)">
                                                        <path
                                                            d="M13.3335 4.00008H8.00016L6.66683 2.66675H2.66683C1.9335 2.66675 1.3335 3.26675 1.3335 4.00008V12.0001C1.3335 12.7334 1.9335 13.3334 2.66683 13.3334H13.3335C14.0668 13.3334 14.6668 12.7334 14.6668 12.0001V5.33341C14.6668 4.60008 14.0668 4.00008 13.3335 4.00008ZM9.3335 12.0001V10.0001H6.66683V7.33341H9.3335V5.33341L12.6668 8.66675L9.3335 12.0001Z"
                                                            fill="#222222" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_181_406">
                                                            <rect width="16" height="16" fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg> {{'navigate-to-folder' | translate}}
                                            </a>
                                        </li>
                                    </div>
                                    <div class="submenu" >
                                        <li>
                                            <a role="button" ngbDropdownItem class="" [ngClass]="{'hide':hideElements}"
                                                *ngIf="!fileData.watchDetails.isWatching" (click)="startWatching()"
                                                title="{{'startwatching' | translate}}">
                                                <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                                    viewBox="0 0 16 16" fill="none">
                                                    <g clip-path="url(#clip0_181_428)">
                                                        <path
                                                            d="M8.00016 1.33325C4.32016 1.33325 1.3335 4.31992 1.3335 7.99992C1.3335 11.6799 4.32016 14.6666 8.00016 14.6666C11.6802 14.6666 14.6668 11.6799 14.6668 7.99992C14.6668 4.31992 11.6802 1.33325 8.00016 1.33325ZM6.66683 10.9999V4.99992L10.6668 7.99992L6.66683 10.9999Z"
                                                            fill="#222222" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_181_428">
                                                            <rect width="16" height="16" fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg>
                                                {{'startwatching' | translate}}
                                            </a>
                                            <a role="button" ngbDropdownItem class="" [ngClass]="{'hide':hideElements}"
                                                *ngIf="fileData.watchDetails.isWatching" (click)="stopWatching()"
                                                title="{{'stopwatching' | translate}}">
                                                <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="14" height="14"
                                                    viewBox="0 0 14 14" fill="none">
                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                        d="M4.3335 9.66659H9.66683V4.33325H4.3335V9.66659ZM7.00016 0.333252C3.32016 0.333252 0.333496 3.31992 0.333496 6.99992C0.333496 10.6799 3.32016 13.6666 7.00016 13.6666C10.6802 13.6666 13.6668 10.6799 13.6668 6.99992C13.6668 3.31992 10.6802 0.333252 7.00016 0.333252Z"
                                                        fill="#222222" />
                                                </svg>
                                                {{'stopwatching' | translate}}
                                            </a>
                                        </li>
                                    </div>
                                </div>
                                <div class="submenu" 
                                    *ngIf="(!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0') && myConfig.userRevisionPermission != '0'">
                                    <li>
                                        <!-- workflow list -->
                                        <a *ngIf="priv.canAccessAuditInfo && fileData" class=""
                                            role="button" ngbDropdownItem (click)="performAction('Workflow_List',$event)"
                                            title="{{'workflows' | translate }}" [ngClass]="{'hide':hideElements}">
                                            <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16"
                                                viewBox="0 0 16 16" fill="none">
                                                <g clip-path="url(#clip0_181_458)">
                                                    <path
                                                        d="M13.3335 3.99992H10.6668V2.66659C10.6668 1.92659 10.0735 1.33325 9.3335 1.33325H6.66683C5.92683 1.33325 5.3335 1.92659 5.3335 2.66659V3.99992H2.66683C1.92683 3.99992 1.34016 4.59325 1.34016 5.33325L1.3335 12.6666C1.3335 13.4066 1.92683 13.9999 2.66683 13.9999H13.3335C14.0735 13.9999 14.6668 13.4066 14.6668 12.6666V5.33325C14.6668 4.59325 14.0735 3.99992 13.3335 3.99992ZM9.3335 3.99992H6.66683V2.66659H9.3335V3.99992Z"
                                                        fill="#222222" />
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_181_458">
                                                        <rect width="16" height="16" fill="white" />
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                            {{'workflows' | translate }}
                                        </a>
                                    </li>
                                </div>
                            </ul>
                        </div>
                    </div>
                </ul>
            </span>
            <div *ngIf="(!myConfig.viewAlwaysDocAssociation || myConfig.userFolderPermission != '0') &&
                    myConfig.isActive && !myConfig.isProjectArchived &&
                    myConfig.applicationId != 2 && (myConfig.documentTypeId != 2 || myConfig.viewFileWhenPlaceholder)"
                [hidden]="true">
                <button class="print-btn btn btn-default hide-trigger-button" (click)="startWorkflow()"
                    title="{{'start-workflow' | translate}}">
                    <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <g clip-path="url(#clip0_181_467)">
                            <path
                                d="M10.6668 3.99992V2.66659C10.6668 1.92659 10.0735 1.33325 9.3335 1.33325H6.66683C5.92683 1.33325 5.3335 1.92659 5.3335 2.66659V3.99992H1.3335V12.6666C1.3335 13.4066 1.92683 13.9999 2.66683 13.9999H13.3335C14.0735 13.9999 14.6668 13.4066 14.6668 12.6666V3.99992H10.6668ZM6.66683 2.66659H9.3335V3.99992H6.66683V2.66659ZM6.00016 11.9999V5.99992L11.0002 8.66659L6.00016 11.9999Z"
                                fill="#222222" />
                        </g>
                        <defs>
                            <clipPath id="clip0_181_467">
                                <rect width="16" height="16" fill="white" />
                            </clipPath>
                        </defs>
                    </svg>
                </button>
            </div>
            <div *ngIf="!simpleFileView">
                <button class="print-btn btn btn-default helpcontent" [attr.aria-label]="'help' | translate" title="{{'help' | translate}}"
                    (click)="openHelp('fileViewer')" [ngClass]="{'hide':hideElements}">
                    <i class="fa fa-question" aria-hidden="true"></i>
                </button>
            </div>
            <button *ngIf="_hasCognitiveCDEBtnVisible && myConfig?.fileName" id="cognitive-cde-btn" [class.hidden]="_isChatBotSectionShow" class="btn btn-default" type="button" [title]="'explore-with-ai' | translate" (click)="performAction('cognitive_cde_chatbot')">
                <img src="/images/cognitive_cde_tab_icon.svg" [alt]="'cognitive-cde' | translate"/>
                <span>{{ 'explore-with-ai' | translate }} </span>
            </button>
        </div>
        <!-- Right Section of header Ends -->
        </div>
    </main>
    <div class="help-content close" id="help-content"></div>
    <!-- Header Section Ends -->
    <div class="main-container">
        <div id="left-content" class="left-content-container">
            <div #mediaFileViewer class="mediaFileViewer"
                *ngIf="myConfig.hasMediaFilesSupport && (myConfig.documentTypeId != 2 || myConfig.viewFileWhenPlaceholder); else otherViewer">
            </div>
            <ng-template #otherViewer>
                <div class="apryse-viewer"
                    *ngIf="myConfig.viewerId == 1 || (myConfig.hasOnlineViewerSupport && myConfig.documentTypeId != 2 && myConfig.viewerId != 2 && !myConfig.hasHoopsViewerSupport && !myConfig.has3DRepoViewerSupport) || (myConfig.hasOnlineViewerSupport && myConfig.viewFileWhenPlaceholder && myConfig.viewerId != 2 && !myConfig.hasHoopsViewerSupport && !myConfig.has3DRepoViewerSupport) || myConfig.isFromCompressedFile; else notViewable">
                    <adoddle-apryse-pdftron-viewer *ngIf="!fileData.passwordProtected && myConfig.viewerPreference == 7"
                        [myConfig]="myConfig" [priv]="priv"
                        [downloadOpt]="fileData.downloadOpt"></adoddle-apryse-pdftron-viewer>
                    <div class="password-file" *ngIf="fileData.passwordProtected || myConfig.viewerPreference != 7">
                        <adoddle-file-download-preference *ngIf="priv.canDownloadFile && myConfig.viewerPreference != 7"
                            [data]="fileData.downloadOpt" [configData]="myConfig"
                            [autoPref]="true" [labelDownloadPref]="'password-file-pref'"></adoddle-file-download-preference>
                        <p>{{"password-protected-notification" | translate}}</p>
                    </div>
                </div>

                <ng-template #notViewable>
                    <div class="file-download-pref"
                        *ngIf="!fileNotSupportedMsg && myConfig.viewerId != 2 && (!myConfig.hasOnlineViewerSupport || (myConfig.documentTypeId == 2 && !myConfig.viewFileWhenPlaceholder)) else isHoopsWebSsrViewer">
                        <adoddle-file-download-preference *ngIf="priv.canDownloadFile" [data]="fileData.downloadOpt"
                        [configData]="myConfig" [autoPref]="true" [labelDownloadPref]="'download-msg-pref'"></adoddle-file-download-preference>
                        <p class="download-msg">
                            {{"file-cannot-be-viewed-online" | translate}}
                        </p>
                    </div>
                    <ng-template #isHoopsWebSsrViewer> 
                        <div *ngIf="fileNotSupportedButCanDownloadMsg" class="file-download-pref">
                            <adoddle-file-download-preference *ngIf="priv.canDownloadFile" [data]="fileData.downloadOpt"
                            [configData]="myConfig" [autoPref]="true" [labelDownloadPref]="'file-download-msg-pref'"></adoddle-file-download-preference>
                            <p class="download-msg">
                                {{fileNotSupportedButCanDownloadMsg}}
                            </p>
                        </div>
                        <div #hoopsWebSsrViewer class="hoopsWebSsrViewer" *ngIf="!fileNotSupportedMsg && !fileNotSupportedButCanDownloadMsg">                       
                        </div>
                    </ng-template>
                </ng-template>
            </ng-template>
            <div id="inventorFileNotSupported" [innerHTML]="fileNotSupportedMsg" *ngIf="fileNotSupportedMsg">
            </div>
        </div>
        <div class="right-content-container"
            *ngIf="rightContentActionParams.name && rightContentActionParams.name != ''">
            <adoddle-file-view-action [actionParams]="rightContentActionParams"></adoddle-file-view-action>
        </div>
        <div class="rightSideBar" *ngIf="openReviewComment && reviewEnabled && myConfig" [hidden]="hideSideBar()">
            <adoddle-sidebar [priv]="priv" [myConfig]="myConfig"></adoddle-sidebar>
        </div>
    </div>

    <adoddle-file-upload></adoddle-file-upload>
    <adoddle-edit-attributes></adoddle-edit-attributes>
    <simple-notifications></simple-notifications>
    <adoddle-start-workflow></adoddle-start-workflow>
    <adoddle-activity-center></adoddle-activity-center>
    <adoddle-open-async-app></adoddle-open-async-app>
    <adoddle-print-file-modal></adoddle-print-file-modal>
    <adoddle-workflow-distribution-view></adoddle-workflow-distribution-view>
    <adoddle-file-edit-in-ms-office *ngIf="enableOfficeForWeb" [data]="msOfficeData"></adoddle-file-edit-in-ms-office>
    <adoddle-watch-settings></adoddle-watch-settings>

    <form action='/adoddle/documents?action_id=1' method="post" id="frmOpenContFold" name="frmOpenContFold" aria-hidden="true">
        <input type="hidden" value="{{fileData.currentRevision.projectId}}|{{fileData.currentRevision.folderId}}"
            name="isFromFileView" />
    </form>
</div>

<input #publishRevisionFileInput class="publish-revision-file-select" type="file" name="files" ng2FileSelect
    [uploader]="publishRevisionUploader" aria-labelledby="label-select-file-to-upload" />

<ng-template #showLoading>
    <div class="aLoader loading" *ngIf="myConfig.isFolderActive" [hidden]="myConfig.isFromAdrive && fileData?.currentRevision"></div>
</ng-template>

<div id="fileViewPort" *ngIf="deviceApplicationId.includes(myConfig.applicationId)" class="modal close fade"
    tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="width: auto;">
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="{{close | translate}}">x</button>
    </div>
    <div class="modal-body" id="fileViewPortBody">
    </div>
</div>
<adoddle-user-card [isFromFileView]="isFromFileView"></adoddle-user-card>
<div style="display:none"><adoddle-login></adoddle-login></div>